# ResponseModel 中 Result 不為 "0001" 的所有地方（包含 Utilities）

| 檔案 | 方法 | 結果代碼 | 錯誤訊息 | 描述 |
|------|------|----------|----------|------|
| JobService.cs | GetJobInfoAsync | Batch-Job-Ex-0003 | Job not found, JobId: {jobId} | 當找不到指定的 Job ID 時返回 |
| JobService.cs | UpdateTotalRecordsAsync | Batch-Job-Ex-0002 | No job found with the provided workflowId: {workflowId} | 找不到指定的 workflow ID |
| JobService.cs | UpdateProcessRecordsAsync | Batch-Job-Ex-0002 | No job found with the provided workflowId: {workflowId} | 找不到指定的 workflow ID |
| JobService.cs | GetJobInfoTemplateAsync | Batch-Job-Ex-0002 | No job found with the provided jobId: {jobId} | 找不到指定的 job ID |
| JobService.cs | CreateJobAsync | Batch-Job-Ex-0002 | Create job failed, {submitResult.ErrorMessage} | 創建任務失敗 |
| JobService.cs | CreateJobAsync (CreateScheduleJob) | Batch-ScheduleJob-Ex-0002 | (沒有提供錯誤訊息) | Schedule job 更新失敗 |
| JobService.cs | UpdateJobAsync | Batch-Job-Ex-0002 | No job found with the provided jobId: {jobId} | 找不到指定的 job ID |
| JobService.cs | CanceledJobAsync | 0004 | Job not exist. | Job 不存在 |
| JobService.cs | CanceledJobAsync | 0004 | error | 取消 Job 操作錯誤 |
| JobService.cs | CanceledJobAsync | 0006 | Job cancelled failed | Job 取消失敗 |
| JobService.cs | UpdateJobStatusAsync | Batch-Job-Ex-0002 | Update Job status failed, WorkflowId: {updateJob.WorkflowId} | 更新任務狀態失敗 |
| JobService.cs | UpdateJobStatusAsync | Batch-Job-Ex-0003 | Job not found, WorkflowId: {updateJob.WorkflowId}, JobId: {updateJob.JobId} | 找不到指定的 Job |
| JobService.cs | GetJobInternalAsync | Batch-Job-Ex-0003 | Job not found, {action} ID: {idValue} | 找不到指定的 Job |
| WorkflowService.cs | GetWorkflowAsync | 0002 | Workflow not found | 找不到工作流程 |
| WorkflowService.cs | UpdateWorkflowStatusAsync | 0002 | Workflow not found | 找不到工作流程 |
| ScheduleJobService.cs | DeleteScheduleJobAsync | Batch-ScheduleJob-Ex-0003 | Schedule Job not exist. | 排程任務不存在 |
| ScheduleJobService.cs | DeleteScheduleJobAsync | Batch-ScheduleJob-Ex-0002 | disable schedule Job failed | 禁用排程任務失敗 |
| ScheduleJobService.cs | UpdateScheduleJobAsync | Batch-ScheduleJob-Ex-0003 | Schedule Job not exist. | 排程任務不存在 |
| ScheduleJobService.cs | UpdateScheduleJobStatusAsync | Batch-ScheduleJob-Ex-0003 | Schedule Job not exist. | 排程任務不存在 |
| ScheduleJobService.cs | UpdateScheduleJobStatusAsync | Batch-ScheduleJob-Ex-0004 | Workflow not exist. | 工作流程不存在 |
| ScheduleJobService.cs | ChangeScheduleJobEnabledStateAsync | Batch-ScheduleJob-Ex-0003 | Schedule Job not exist. | 排程任務不存在 |
| ScheduleJobService.cs | ChangeScheduleJobEnabledStateAsync | Batch-ScheduleJob-Ex-0005 | {action} schedule Job failed, {message} | 啟用/禁用排程任務失敗 |
| ScheduleJobService.cs | ChangeScheduleJobEnabledStateAsync | Batch-ScheduleJob-Ex-0002 | {action} schedule Job failed | 啟用/禁用排程任務失敗 |
| ArgoWorkflowService.cs | SubmitWorkflowAsync | Argo-Workflow-Ex-0002 | Failed to submit workflow | 提交工作流程失敗 |
| ArgoWorkflowService.cs | SubmitCronWorkflowAsync | Argo-Workflow-Ex-0002 | Failed to submit workflow | 提交定時工作流程失敗 |
| ArgoWorkflowService.cs | SubmitWorkflowAsync (CreateJob) | Argo-Workflow-Ex-0002 | Failed to submit workflow | 提交工作流程失敗 |
| ArgoWorkflowService.cs | GetWorkflowTemplatesAsync | Argo-Workflow-Ex-{errorCode} | {queryResult.ErrorMessage} | 獲取工作流程模板失敗 |
| ArgoWorkflowService.cs | GetWorkflowTemplateAsync | Argo-Workflow-Ex-{errorCode} | {queryResult.ErrorMessage} | 獲取工作流程模板失敗 |
| ArgoWorkflowService.cs | GetWorkflows | Argo-Workflow-Ex-{errorCode} | {queryResult.ErrorMessage} | 獲取工作流程列表失敗 |
| ArgoWorkflowService.cs | GetWorkflowLog | Argo-Workflow-Ex-{errorCode} | {queryResult.ErrorMessage} | 獲取工作流程日誌失敗 |
| ArgoWorkflowService.cs | GetSensors | Argo-Workflow-Ex-{errorCode} | {queryResult.ErrorMessage} | 獲取感應器列表失敗 |
| ArgoWorkflowService.cs | GetEventsource | Argo-Workflow-Ex-{errorCode} | {queryResult.ErrorMessage} | 獲取事件源列表失敗 |
| ArgoWorkflowService.cs | DeleteWorkflowTemplate | Argo-Workflow-Ex-{errorCode} | {queryResult.ErrorMessage} | 刪除工作流程模板失敗 |
| ArgoWorkflowService.cs | DeleteCronWorkflow | Argo-Workflow-Ex-{errorCode} | {queryResult.ErrorMessage} | 刪除定時工作流程失敗 |
| ArgoWorkflowService.cs | DeleteEventsource | Argo-Workflow-Ex-{errorCode} | {queryResult.ErrorMessage} | 刪除事件源失敗 |
| ArgoWorkflowService.cs | DeleteSensor | Argo-Workflow-Ex-{errorCode} | {queryResult.ErrorMessage} | 刪除感應器失敗 |
| ArgoWorkflowService.cs | HandleWorkflowActionAsync | Argo-Workflow-Ex-{errorCode} | {queryResult.ErrorMessage} | 工作流程操作失敗 |

## Utilities 中的 QueryResult 錯誤處理

在 Utilities 目錄的 ArgoWorkflowUtility.cs 中，使用了 QueryResult 類來表示 API 調用結果。雖然這些方法不直接返回 ResponseModel，但它們的結果會被 ArgoWorkflowService 轉換為 ResponseModel。以下是錯誤處理模式：

| 方法 | 錯誤代碼來源 | 錯誤訊息來源 | 備註 |
|------|------------|------------|------|
| SubmitWorkflowAsync | API 返回的 "code" 字段 | API 返回的 "message" 字段 | 失敗時設置 IsSuccess = false |
| SubmitCronWorkflowAsync | API 返回的 "code" 字段 | API 返回的 "message" 字段 | 失敗時設置 IsSuccess = false |
| GetWorkflowsAsync | API 返回的 "code" 字段 | API 返回的 "message" 字段 | 失敗時設置 IsSuccess = false |
| GetWorkflowLogAsync | API 返回的 "code" 字段 | API 返回的 "message" 字段 | 失敗時設置 IsSuccess = false |
| GetCronWorkflowsAsync | API 返回的 "code" 字段 | API 返回的 "message" 字段 | 失敗時設置 IsSuccess = false |
| GetWorkflowTemplatesAsync | API 返回的 "code" 字段 | API 返回的 "message" 字段 | 失敗時設置 IsSuccess = false |
| GetWorkflowTemplateAsync | API 返回的 "code" 字段 | API 返回的 "message" 字段 | 失敗時設置 IsSuccess = false |
| GetSensors | API 返回的 "code" 字段 | API 返回的 "message" 字段 | 失敗時設置 IsSuccess = false |
| GetEventsource | API 返回的 "code" 字段 | API 返回的 "message" 字段 | 失敗時設置 IsSuccess = false |
| ExecuteWorkflowActionAsync | API 返回的 "code" 字段 | API 返回的 "message" 字段 | 用於各種工作流程操作 |
| DeleteResource | API 返回的 "code" 字段 | API 返回的 "message" 字段 | 用於刪除各種資源 |

當這些 QueryResult 實例在 ArgoWorkflowService 中被使用時，會被轉換為 ResponseModel，錯誤代碼會被轉換為 "Argo-Workflow-Ex-XXXX" 格式。