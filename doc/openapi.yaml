openapi: 3.0.1
info:
  title: JKOPay Batchsystem API
  description: API endpoints for JKOPay batch system operations
  version: v1
security:
  - Bearer: []
paths:
  /api/v1/event-sources/{namespace}:
    get:
      tags:
        - Eventsource
      summary: 取得目前的 Eventsource 清單
      description: 取得目前的 Eventsource 清單
      operationId: GetEventsource
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: path
          description: 團隊使用的 k8s 的空間名稱
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfListOfEventsource'
  /api/v1/event-sources/{namespace}/{eventsourceName}:
    delete:
      tags:
        - Eventsource
      summary: 刪除 Eventsource
      description: 刪除 Eventsource
      operationId: DeleteEventsource
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: path
          description: 團隊使用的 k8s 的空間名稱
          required: true
          schema:
            type: string
        - name: eventsourceName
          in: path
          description: Eventsource 名稱
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/job:
    get:
      tags:
        - Job
      summary: 取得 Job 資訊
      description: 取得 Job 資訊
      operationId: GetJob
      security:
        - Bearer: []
      parameters:
        - name: jobId
          in: query
          description: Job Id
          schema:
            type: string
        - name: workflowId
          in: query
          description: Workflow Id
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/job/info/{jobId}:
    get:
      tags:
        - Job
      summary: 取得 Job 資訊
      description: 取得 Job 資訊
      operationId: GetJobInfo
      security:
        - Bearer: []
      parameters:
        - name: jobId
          in: path
          description: Job Id
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/job/list/{team}/{page}:
    get:
      tags:
        - Job
      summary: 取得 Job 列表
      description: 取得 Job 列表
      operationId: GetList
      security:
        - Bearer: []
      parameters:
        - name: team
          in: path
          description: 團隊名稱
          required: true
          schema:
            type: string
        - name: page
          in: path
          description: Page
          required: true
          schema:
            type: integer
            format: int32
        - name: count
          in: query
          description: 單頁顯示筆數
          required: true
          schema:
            type: integer
            format: int32
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
  /api/v1/job/execute:
    post:
      tags:
        - Job
      summary: 建立 Job
      description: 建立 Job
      operationId: CreateJob
      security:
        - Bearer: []
      requestBody:
        description: Job 相關資訊
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateJob'
        required: true
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfDictionaryOfStringAndString'
  /api/v1/job/info/template/{namespace}/{templateName}/{jobId}/{source}:
    get:
      tags:
        - Job
      summary: Get Job Info Template
      description: Get Job Info Template
      operationId: GetInfoTemplate
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: path
          required: true
          schema:
            type: string
        - name: templateName
          in: path
          required: true
          schema:
            type: string
        - name: jobId
          in: path
          required: true
          schema:
            type: string
        - name: source
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/job/resubmit/{namespace}/{jobId}:
    put:
      tags:
        - Job
      summary: 重新執行 Job
      description: 重新執行 Job
      operationId: ResubmitJob
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: path
          description: 團隊使用的 k8s 的空間名稱
          required: true
          schema:
            type: string
        - name: jobId
          in: path
          description: Job id
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/job/create/schedule:
    post:
      tags:
        - Job
      summary: 建立 Schedule job 的 job record
      description: 建立 schedule job 的 job record
      operationId: CreateScheduleJob
      security:
        - Bearer: []
      requestBody:
        description: Schedule workflow 相關資訊
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateScheduleJob'
        required: true
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/job/cancel/{namespace}/{jobId}:
    post:
      tags:
        - Job
      summary: 取消 Job
      description: 取消 Job
      operationId: CancelJob
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: path
          description: 團隊使用的 k8s 的空間名稱
          required: true
          schema:
            type: string
        - name: jobId
          in: path
          description: 建立 Job 時回傳的 Job ID
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/job/update/totalRecords:
    post:
      tags:
        - Job
      summary: 更新 Job Record 中的需要處理資料筆數
      description: 更新 Job Record 中的需要處理資料筆數
      operationId: UpdateTotalRecords
      security:
        - Bearer: []
      parameters:
        - name: totalCount
          in: query
          required: true
          schema:
            type: integer
            format: int32
        - name: workflowId
          in: query
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/meter/add:
    get:
      tags:
        - Meter
      summary: Increments the BooksAddedCounter metric
      description: Increments the BooksAddedCounter metric
      operationId: Add
      security:
        - Bearer: []
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
  /api/v1/meter/test:
    get:
      tags:
        - Meter
      summary: Tests the meter functionality and logs trace information
      description: Tests the meter functionality and logs trace information
      operationId: Test
      security:
        - Bearer: []
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: object
  /api/v1/meter/scheduleJobList:
    get:
      tags:
        - Meter
      summary: Get CronWorkflow List on argo workflow server
      description: Get CronWorkflow List on argo workflow server
      operationId: ScheduleJobList
      security:
        - Bearer: []
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
  /api/v1/schedule/execute:
    post:
      tags:
        - Schedule
      summary: Execute Schedule Job
      description: Execute Schedule Job
      operationId: ExecuteJob
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: query
          description: Namespace
          required: true
          schema:
            type: string
        - name: scheduleJobName
          in: query
          description: Schedule Job Name
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfDictionaryOfStringAndString'
  /api/v1/schedule/suspend:
    post:
      tags:
        - Schedule
      summary: 暫停 Schedule Job
      description: 暫停 Schedule Job
      operationId: SuspendScheduleJob
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: query
          required: true
          schema:
            type: string
        - name: scheduleJobName
          in: query
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/schedule/resume:
    post:
      tags:
        - Schedule
      summary: 恢復 Schedule Job
      description: 恢復 Schedule Job
      operationId: ResumeScheduleJob
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: query
          description: Namespace
          required: true
          schema:
            type: string
        - name: scheduleJobName
          in: query
          description: Schedule Job Name
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/sensors/{namespace}:
    get:
      tags:
        - Sensor
      summary: 取得目前的 Sensor 清單
      description: 取得目前的 Sensor 清單
      operationId: GetSensors
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: path
          description: 團隊使用的 k8s 的空間名稱
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfListOfSensor'
  /api/v1/sensors/{namespace}/{sensorName}:
    delete:
      tags:
        - Sensor
      summary: 刪除 Sensor
      description: 刪除 Sensor
      operationId: DeleteSensor
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: path
          description: 團隊使用的 k8s 的空間名稱
          required: true
          schema:
            type: string
        - name: sensorName
          in: path
          description: Sensor 名稱
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/test/NonJsonResult:
    get:
      tags:
        - Test
      summary: Test non json result
      description: Test non json result
      operationId: NonJsonResult
      security:
        - Bearer: []
      responses:
        "200":
          description: Success
          content:
            text/plain:
              schema:
                type: string
  /api/v1/test/RequestLog:
    post:
      tags:
        - Test
      summary: Test request log
      description: Test request log
      operationId: RequestLog
      security:
        - Bearer: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestRequest'
        required: true
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: object
  /api/v1/test/ErrorResult:
    get:
      tags:
        - Test
      summary: Test error result
      description: Test error result
      operationId: ErrorResponse
      security:
        - Bearer: []
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
  /api/v1/workflow-templates/{namespace}:
    get:
      tags:
        - WorkflowTemplate
      summary: 取得目前的 Workflow Template 清單
      description: 取得目前的 Workflow Template 清單
      operationId: GetWorkflowTemplates
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: path
          description: 團隊使用的 k8s 的空間名稱
          required: true
          schema:
            type: string
        - name: labelSelector
          in: query
          description: Label filter
          schema:
            type: string
            default: ""
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfListOfWorkflowTemplate'
  /api/v1/workflow-templates/{namespace}/{templateName}:
    get:
      tags:
        - WorkflowTemplate
      summary: 取得 WorkflowTemplate
      description: 取得 WorkflowTemplate
      operationId: GetWorkflowTemplate
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: path
          description: 團隊使用的 k8s 的空間名稱
          required: true
          schema:
            type: string
        - name: templateName
          in: path
          description: Workflow template name
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfWorkflowTemplate'
    delete:
      tags:
        - WorkflowTemplate
      summary: 刪除 Workflow Template
      description: 刪除 Workflow Template
      operationId: DeleteWorkflowTemplate
      security:
        - Bearer: []
      parameters:
        - name: namespace
          in: path
          description: 團隊使用的 k8s 的空間名稱
          required: true
          schema:
            type: string
        - name: templateName
          in: path
          description: Workflow template name
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseModelOfObject'
components:
  securitySchemes:
    Bearer:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: Enter JWT Bearer token
  schemas:
    Eventsource:
      type: object
      properties:
        metadata:
          type: object
          properties:
            name:
              type: string
              description: Eventsource name
        spec:
          type: object
          description: Eventsource specification
    ResponseModelOfListOfEventsource:
      type: object
      properties:
        Result:
          type: string
          description: Result code
        Message:
          type: string
          description: System result message
        ResultObject:
          type: array
          items:
            $ref: '#/components/schemas/Eventsource'
          maxItems: 1000
          description: Result data
    CreateJob:
      type: object
      properties:
        priority:
          type: string
          description: Option, if trigger type is kafka, pls write
          enum:
            - Immediate
            - Priority
            - Normal
            - Schedule
        jobName:
          type: string
          description: Job name
          maxLength: 40
        workflowTemplateName:
          type: string
          description: Argo workflow name
        message:
          type: object
          additionalProperties:
            type: object
          description: Job need data
        serviceNamespace:
          type: string
          description: Which module does the request come from
      required:
        - priority
        - jobName
        - workflowTemplateName
        - message
        - serviceNamespace
    CreateScheduleJob:
      type: object
      properties:
        jobName:
          type: string
        workflowId:
          type: string
        status:
          type: string
        workflowName:
          type: string
        priority:
          type: string
          enum:
            - Immediate
            - Priority
            - Normal
            - Schedule
        workflowTemplateName:
          type: string
        team:
          type: string
    Sensor:
      type: object
      properties:
        metadata:
          type: object
          properties:
            name:
              type: string
              description: Sensor name
        spec:
          type: object
          description: Sensor specification
    ResponseModelOfListOfSensor:
      type: object
      properties:
        Result:
          type: string
          description: Result code
        Message:
          type: string
          description: System result message
        ResultObject:
          type: array
          items:
            $ref: '#/components/schemas/Sensor'
          maxItems: 1000
          description: Result data
    TestRequest:
      type: object
      properties:
        name:
          type: string
          description: Name
        age:
          type: integer
          format: int32
          description: Age
        token:
          type: string
          description: Token
    WorkflowTemplate:
      type: object
      properties:
        metadata:
          type: object
          properties:
            name:
              type: string
              description: Workflow template name
        spec:
          type: object
          description: Workflow template specification
    ResponseModelOfListOfWorkflowTemplate:
      type: object
      properties:
        Result:
          type: string
          description: Result code
        Message:
          type: string
          description: System result message
        ResultObject:
          type: array
          items:
            $ref: '#/components/schemas/WorkflowTemplate'
          maxItems: 1000
          description: Result data
    ResponseModelOfWorkflowTemplate:
      type: object
      properties:
        Result:
          type: string
          description: Result code
        Message:
          type: string
          description: System result message
        ResultObject:
          $ref: '#/components/schemas/WorkflowTemplate'
          description: Result data
    ResponseModelOfObject:
      type: object
      properties:
        Result:
          type: string
          description: Result code
        Message:
          type: string
          description: System result message
        ResultObject:
          type: object
          description: Result data
    ResponseModelOfDictionaryOfStringAndString:
      type: object
      properties:
        Result:
          type: string
          description: Result code
        Message:
          type: string
          description: System result message
        ResultObject:
          type: object
          additionalProperties:
            type: string
          description: Result data
