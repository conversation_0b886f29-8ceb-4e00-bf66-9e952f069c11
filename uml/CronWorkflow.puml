@startuml
title Schedule Job

box "Argo Workflow"
participant "CronWorkflow (DAG)" as E <<(E, #ADD1B2>>
'participant "Module Team WorkflowTemplate"
end box

E -> BatchSystemAPI: Create job

BatchSystemAPI -> DB: Add job record (Status: New)

E -> BatchSystemAPI: Update status
BatchSystemAPI -> DB: Update job record（Status: WorkflowStarted）

E -> BatchSystemAPI: Send workflow status
activate E

note left
Execute business team WorkflowTemplate.
Batch system console Start.
endnote

BatchSystemAPI -> DB: Update job record (Status: JobStarted)
E -> E: execute batch system console.
note left
BatchSystemConsole will send workflow status
updates at the start and end of the job, as well as
during events at the EntryPoint.
endnote


E -> BatchSystemAPI: Send workflow status

note left
Batch system console End
endnote
deactivate E
BatchSystemAPI -> DB: Update job record (Status: JobEnded)

E -> BatchSystemAPI: Send workflow status.

note left
Send Workflow status
at the final step.
endnote

BatchSystemAPI -> DB: Update job record (Status: WorkflowCompleted)
@enduml