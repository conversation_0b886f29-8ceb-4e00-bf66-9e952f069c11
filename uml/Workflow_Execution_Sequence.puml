@startuml
'https://plantuml.com/sequence-diagram
autonumber

box "Argo Workflow"
participant "General Workflow" as G <<(G, #ADD1B2>>
participant "DAG WorkflowTemplate" as D << (D,#ADD1B2)>>
participant "Business Team WorkflowTemplate" as B << (B, #ADD1B2)>>
end box

create D
G -> D : Template ref DAG WorkflowTemplate

activate D
D -> D: Update and check status

create B
D -> B: Template ref business WorkflowTemplate
B -> B: Execute business process


D -> D: Complete and update status
D -> G: Complete
deactivate D

@enduml