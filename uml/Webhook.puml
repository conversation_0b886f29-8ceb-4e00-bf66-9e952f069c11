@startuml
title Webhook

Merchant.Web -> BatchSystemAPI: 發送 JOB

group submit Workflow
BatchSystemAPI -> DB: Add JOB Record
BatchSystemAPI -> ArgoWorkflow: Submit Workflow
BatchSystemAPI -> DB: Update JOB Record
BatchSystemAPI -> Merchant.Web: Job Information
end

box "DAG Workflow (MW)"
participant "DAG Workflow" as W << (W,#ADD1B2)>>
end box

create W
ArgoWorkflow -> W: Workflow start
W -> BatchSystemAPI: Send workflow status and check workflow status
activate W
note right
send Workflow status at the first step.
endnote

BatchSystemAPI -> DB: Check status
alt Status is New or Created
BatchSystemAPI -> DB: Update job status from New(Created) to WorkflowStarted
BatchSystemAPI -> W: Return workflow continue
else Status is Cancelled
BatchSystemAPI -> W: Return workflow cancelled
end
deactivate W

alt Workflow continue
W -> BatchSystemAPI: Send workflow status

activate W
note right
BatchSystemConsole will send workflow status at the start and End,
during the event at the entry point
endnote

BatchSystemAPI -> DB: Update jOB Record
W -> BatchSystemAPI: Send workflow status
note right
Batch system console end
endnote
deactivate W
BatchSystemAPI -> DB: Update jOB record

W -> BatchSystemAPI: Send workflow status
activate W
note right
send workflow status at the final step
endnote

end
@enduml