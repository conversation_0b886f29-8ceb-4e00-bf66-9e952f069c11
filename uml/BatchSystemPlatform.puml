@startuml
title Schedule Job

box "Batch System Platform"
participant "Platform" as P <<(P, #ADD1B2>>
participant "Job" as J <<(J, #ADD1B2>>
participant "BaseJob" as B <<(B, #ADD1B2>>
end box

P -> DB: Update job record (Status: JobStarted)

P -> J: Execute job
activate J

J -> B: Set the number of data to process.
B -> DB: Update job the number of data to process

J -> J: Data process

J -> B: Update the current data processing progress.
B -> DB: Update job current data processing progress.

J -> P: Job completed
deactivate J
'note left
'Batch system console End
'endnote

P -> DB: Update job record (Status: JobEnded)

@enduml
