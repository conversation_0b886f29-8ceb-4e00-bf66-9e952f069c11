@startuml
title Ka<PERSON><PERSON>

Merchant.Web -> BatchSystemAPI: 發送 Job
BatchSystemAPI -> DB: Create Job Record
BatchSystemAPI -> Kafka: Send Message
BatchSystemAPI -> Merchant.Web: Job Information

box "Argo Workflow"
participant "Evnetsource" as E <<(E, #ADD1B2>>
participant "Trigger" as T << (T,#ADD1B2)>>
participant "Multi Step Workflow" as W << (W, #ADD1B2)>>
end box

create E
Kafka -> E: Notification has new data

create T
E -> T: Trigger

create W
T -> W: Create multi step workflow
deactivate W

W -> BatchSystemAPI: Send workflow status anc check status
activate W
note left
Send Workflow status at the first step.
endnote

BatchSystemAPI -> DB: Check status
alt Status is New or Created
BatchSystemAPI -> DB: Update job record from New(Created) to WorkflowStarted
BatchSystemAPI -> W: Return workflow continue
else Status is Cancelled
BatchSystemAPI -> W: Return workflow cancelled
end
deactivate W

alt Workflow is continue
W -> BatchSystemAPI: Send workflow status
activate W
note left
BatchSystemConsole will Send
workflow Status at the Start and End,
during the event at the EntryPoint
endnote
BatchSystemAPI -> DB: Update job record

W -> BatchSystemAPI: Send workflow status
note left
Batch system console end
endnote
deactivate W
BatchSystemAPI -> DB: Update job record

W -> BatchSystemAPI: Send workflow status
activate W
note left
Send Workflow status
at the final step.
endnote
deactivate W
BatchSystemAPI -> DB: Update job record
end

@enduml
