{{
    $global.Host = `${Host}`;
    console.info('Host: ' + $global.Host);
}}

###
POST {{$global.Host}}/v1/job/execute HTTP/1.1
Content-Type: application/json

{
     "Priority": "Immediate",
     "JobName": "BatchSystem-Test",
     "WorkflowTemplateName": "foundation-job-wt",
     "TemplateName": "main",
     "Message": {
         "jobData": {
             "Parameter": {
                "Contenet": "8"
             },
             "JobName": "slackjob"
         }
     },
     "ServiceNamespace": "foundation"
 }
###
POST {{$global.Host}}/v1/job/create/schedule HTTP/1.1
Content-Type: application/json

{
    "workflowId": "{{$uuid}}",
    "workflowName": "test-flow-{{$uuid}}",
    "jobName": "test-job-{{$uuid}}",
    "workflowTemplateName": "batchsystem-workflow-template",
    "team": "found",
    "Status": "New",
    "Priority": "schedule"
}
###
POST {{$global.Host}}/v1/job/schedule/update HTTP/1.1
Content-Type: application/json

{
    "JobId": 0,
    "CronWorkflowId": "6480a22f-6129-43a1-91b6-1c06b08aff05",
    "ScheduleJobName": "found-general-schedule-job",
    "LatestStatus": "none",
    "ExecutionCycle": "* */17 * * *",
    "WorkflowTemplateRef": "found-batchsystem-main-cron-workflow-template",
    "LatestWorkflowName": null,
    "LatestWorkflowId": null,
    "LatestStartedAt": null,
    "LatestFinishedAt": null,
    "IsDeleted": false,
    "IsEnable": true,
    "Team": "found",
    "Comments": null,
    "CreatedTime": "0001-01-01T00:00:00.0000000",
    "UpdatedTime": "0001-01-01T00:00:00.0000000"
}
###
POST {{$global.Host}}/v1/job/schedule/create HTTP/1.1
Content-Type: application/json

{
    "JobId": 0,
    "CronWorkflowId": "444edfb2-7fd6-40a0-8d6b-e12a0a58abf2",
    "ScheduleJobName": "found-general-schedule-job",
    "LatestStatus": "none",
    "ExecutionCycle": "* */17 * * *",
    "WorkflowTemplateRef": "found-batchsystem-main-cron-workflow-template",
    "LatestWorkflowName": null,
    "LatestWorkflowId": null,
    "LatestStartedAt": null,
    "LatestFinishedAt": null,
    "IsDeleted": false,
    "IsEnable": true,
    "Team": "found",
    "Comments": null,
    "CreatedTime": "0001-01-01T00:00:00.0000000",
    "UpdatedTime": "0001-01-01T00:00:00.0000000"
}
###
POST {{$global.Host}}/v1/job/schedule/delete HTTP/1.1
Content-Type: application/json

{
    "JobId": 0,
    "CronWorkflowId": "444edfb2-7fd6-40a0-8d6b-e12a0a58abf2",
    "ScheduleJobName": "found-general-schedule-job",
    "LatestStatus": "none",
    "ExecutionCycle": "* */17 * * *",
    "WorkflowTemplateRef": "found-batchsystem-main-cron-workflow-template",
    "LatestWorkflowName": null,
    "LatestWorkflowId": null,
    "LatestStartedAt": null,
    "LatestFinishedAt": null,
    "IsDeleted": false,
    "IsEnable": true,
    "Team": "found",
    "Comments": null,
    "CreatedTime": "0001-01-01T00:00:00.0000000",
    "UpdatedTime": "0001-01-01T00:00:00.0000000"
}
PUT {{$global.Host}}/v1/job/resubmit/found/4029d07d-0072-430b-8b69-9b4f766ceccf HTTP/1.1
###
POST {{$global.Host}}/v1/job/cancel/found/foundtest-workflow-6a9c19de528641acad3aaf6aea0f8dbc HTTP/1.1
###
POST {{$global.Host}}/v1/job/update/status HTTP/1.1
Content-Type: application/json

{
  "jobId": "",
  "workflowId": "692b9d2f-198d-4c16-9672-cd3540ca6588",
  "workflowName": "general-schedule-job-rnw7f",
  "Status": "WorkflowStarted",
  "Priority": "schedule"
}
```​⬤
###
POST {{$global.Host}}/v1/job/schedule/create HTTP/1.1
Content-Type: application/json

{
    "JobId": 0,
    "CronWorkflowId": "55d8e6e8-1fce-4f1e-8fe4-40f6e8528a2f",
    "ScheduleJobName": "general-schedule-job",
    "LatestStatus": "none",
    "ExecutionCycle": "* * */9 * *",
    "WorkflowTemplateRef": "dag-workflow-template",
    "LatestWorkflowName": null,
    "LatestWorkflowId": null,
    "LatestStartedAt": null,
    "LatestFinishedAt": null,
    "IsDeleted": false,
    "IsEnable": true,
    "Team": "found",
    "Comments": null
}

###
POSt {{$global.Host}}/v1/job/update/totalRecords?totalCount=100&workflowId=223cd6b4-ab3a-4a38-ad4c-d810327f37df HTTP/1.1
Content-Type: text/plain


###
GET {{$global.Host}}/v1/job/info/223cd6b4-ab3a-4a38-ad4c-d810327f37df HTTP/1.1

###
GET {{$global.Host}}/v1/job/list/found/1?count=100 HTTP/1.1

###
GET {{$global.Host}}/v1/job/adminpanel/223cd6b4-ab3a-4a38-ad4c-d810327f37df HTTP/1.1

###
GET {{$global.Host}}/v1/job/list/foundation/adminpanel?page=1&count=100 HTTP/1.1

###
GET {{$global.Host}}/v1/job/list/foundation/adminpanel?page=1&count=100 HTTP/1.1

###

GET http://localhost:5022/healthz HTTP/1.1

###
get https://batchsystem.jamis.app:8443/healthz HTTP/1.1