{{
    $global.Host = `${Host}`;
    console.info('Host: ' + $global.Host);
}}

###
GET {{$global.Host}}/v1/workflows/found HTTP/1.1

###
GET {{$global.Host}}/v1/workflows/argo?labelSelector=owner=found HTTP/1.1
###

POST {{$global.Host}}/v1/job/create HTTP/1.1
Content-Type: application/json

{
    "TriggerType": "Webhook",
    "JobName": "FoundTest",
    "TemplateName": "reusable-workflow-template",
    "Message": {
        "message" : "test by batch api"
    },
    "SourceModuleName": "Found"
}

###
{{
    let body = {
        JobId: "bef1f77a-fa26-4a7a-9bee-8c0d55e4c662",
        Status: "WorkflowStarted"
    };

    let jsonStr = JSON.stringify(body);
    exports.BODY = jsonStr;
    console.info(jsonStr);
}}

POST {{$global.Host}}/v1/job/update/status HTTP/1.1
Content-Type: application/json

{{BODY}}