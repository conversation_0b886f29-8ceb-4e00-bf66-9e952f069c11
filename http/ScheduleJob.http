{{
    $global.Host = `${Host}`;
    console.info('Host: ' + $global.Host);
}}

GET {{$global.Host}}/v1/schedule/list/foundation/adminpanel?currentPage=1&pageSize=100 HTTP/1.1

###
POST {{$global.Host}}/v1/schedule/update HTTP/1.1
Content-Type: application/json

{
  "JobId": 0,
  "CronWorkflowId": "f746950c-f3e2-465b-a2a3-91fdd5894ba9",
  "ScheduleJobName": "dev-foundation-test-sj",
  "LatestStatus": "none",
  "ExecutionCycle": "* */20 * * *",
  "WorkflowTemplateRef": "dev-foundation-batchsystem-main-cron-workflow-template",
  "LatestWorkflowName": null,
  "LatestWorkflowId": null,
  "LatestStartedAt": null,
  "LatestFinishedAt": null,
  "Team": "foundation",
}

###
POST {{$global.Host}}/v1/schedule/update/status HTTP/1.1
Content-Type: application/json

{
  "CronWorkflowId": "d8b1de4d-f1f7-4ada-8e48-460d80418a9c",
  "ScheduleJobName": "dev-foundation-scrape-schedule-job-status-job-sj",
  "LatestStatus": "Running",
  "LatestWorkflowName": "dev-foundation-scrape-schedule-job-status-job-sj-1746671400",
  "LatestWorkflowId": "5322f227-06b4-441e-b126-6e37f3eae026",
  "Team": "foundation",
  "LatestStartedAt": "2025/5/8 2:30:00\u202FAM",
  "LatestFinishedAt": ""
}
###
POST {{$global.Host}}/v1/schedule/execute?namespace=foundation&scheduleJobName=dev-foundation-test-sj HTTP/1.1
accept: text/plain
Content-Type: application/x-www-form-urlencoded
