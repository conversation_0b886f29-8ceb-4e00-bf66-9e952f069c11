{{
    $global.Host = `${Host}`;
    console.info('Host: ' + $global.Host);
}}

###
GET {{$global.Host}}/v1/workflow-templates/found HTTP/1.1
###
# get workflow template list error
GET {{$global.Host}}/v1/workflow-templates/argo?labelSelector=owner=filter,name=test" HTTP/1.1
###
# get workflow template wiht labelSelector
GET {{$global.Host}}/v1/workflow-templates/argo?labelSelector=owner=found HTTP/1.1
X-Correlation-ID: {{$uuid}}

###
DELETE {{$global.Host}}/v1/workflow-templates/found/omniscient-bear HTTP/1.1