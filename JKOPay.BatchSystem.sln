﻿
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JKOPay.BatchSystem.Api", "src\JKOPay.BatchSystem.Api\JKOPay.BatchSystem.Api.csproj", "{F5D091CC-4082-4466-9011-3B4A6F273552}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{B8BAA062-ACF6-4942-BB5B-171229F0B13F}"
	ProjectSection(SolutionItems) = preProject
		docker-compose.yml = docker-compose.yml
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JKOPay.BatchSystem.Core", "src\JKOPay.BatchSystem.Core\JKOPay.BatchSystem.Core.csproj", "{9711F336-3954-4B2C-A354-331513C0E6A3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JKOPay.BatchSystem.Core.Test", "test\JKOPay.BatchSystem.Core.Test\JKOPay.BatchSystem.Core.Test.csproj", "{8BC81B5E-5B66-4953-9782-D5B343ED7DFF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JKOPay.BatchSystem.Operator", "src\JKOPay.BatchSystem.Operator\JKOPay.BatchSystem.Operator.csproj", "{F91C3C79-FA62-475E-B1DC-D6EA02DC7511}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JKOPay.BatchSystem.Job", "src\JKOPay.BatchSystem.Job\JKOPay.BatchSystem.Job.csproj", "{7AACBA6B-4A8B-47FA-8BDC-84B80F78DCC3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JKOPay.BatchSystem.Operator.Test", "test\JKOPay.BatchSystem.Operator.Test\JKOPay.BatchSystem.Operator.Test.csproj", "{42965D7B-DF09-438D-BB30-BE5A7E4315F4}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F5D091CC-4082-4466-9011-3B4A6F273552}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F5D091CC-4082-4466-9011-3B4A6F273552}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F5D091CC-4082-4466-9011-3B4A6F273552}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F5D091CC-4082-4466-9011-3B4A6F273552}.Release|Any CPU.Build.0 = Release|Any CPU
		{9711F336-3954-4B2C-A354-331513C0E6A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9711F336-3954-4B2C-A354-331513C0E6A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9711F336-3954-4B2C-A354-331513C0E6A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9711F336-3954-4B2C-A354-331513C0E6A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BC81B5E-5B66-4953-9782-D5B343ED7DFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BC81B5E-5B66-4953-9782-D5B343ED7DFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BC81B5E-5B66-4953-9782-D5B343ED7DFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BC81B5E-5B66-4953-9782-D5B343ED7DFF}.Release|Any CPU.Build.0 = Release|Any CPU
		{F91C3C79-FA62-475E-B1DC-D6EA02DC7511}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F91C3C79-FA62-475E-B1DC-D6EA02DC7511}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F91C3C79-FA62-475E-B1DC-D6EA02DC7511}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F91C3C79-FA62-475E-B1DC-D6EA02DC7511}.Release|Any CPU.Build.0 = Release|Any CPU
		{7AACBA6B-4A8B-47FA-8BDC-84B80F78DCC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7AACBA6B-4A8B-47FA-8BDC-84B80F78DCC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7AACBA6B-4A8B-47FA-8BDC-84B80F78DCC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7AACBA6B-4A8B-47FA-8BDC-84B80F78DCC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{42965D7B-DF09-438D-BB30-BE5A7E4315F4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{42965D7B-DF09-438D-BB30-BE5A7E4315F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{42965D7B-DF09-438D-BB30-BE5A7E4315F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{42965D7B-DF09-438D-BB30-BE5A7E4315F4}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
