# 產品概述

JKOPay BatchSystem 是一個基於 Argo Workflows 的批次作業管理系統，專為 JKOPay 金融科技平台設計。

## 核心功能

- **批次作業管理**: 提供作業的建立、執行、監控和管理功能
- **排程作業**: 支援 Cron 表達式的定時作業排程
- **工作流程編排**: 基於 Argo Workflows 的複雜工作流程管理
- **事件驅動**: 透過 Kafka 和 Argo Events 實現事件驅動的作業觸發
- **多環境支援**: 支援 dev、sit、uat、prod 等多個部署環境

## 系統架構

系統採用微服務架構，包含以下主要組件：

- **API 服務**: 提供 RESTful API 介面
- **Operator**: Kubernetes 控制器，負責監控和管理工作流程狀態
- **Job**: 實際執行的批次作業邏輯
- **Core**: 共用的核心業務邏輯和模型

## 技術特色

- 雲原生設計，完全容器化部署
- 基於 Kubernetes 和 Argo 生態系統
- 支援 OpenTelemetry 可觀測性
- 採用 GitOps 部署流程
