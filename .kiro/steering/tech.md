# 技術堆疊

## 核心技術

- **.NET 9.0**: 主要開發框架，使用 C# 語言
- **ASP.NET Core**: Web API 框架
- **Entity Framework Core**: ORM 框架，搭配 MySQL 資料庫
- **Kubernetes**: 容器編排平台
- **Argo Workflows**: 工作流程引擎
- **Argo Events**: 事件驅動系統
- **Apache Kafka**: 訊息佇列系統

## 主要套件和函式庫

### 核心套件
- `JKOPay.Platform.*`: JKOPay 內部平台套件
- `Microsoft.EntityFrameworkCore`: 資料存取
- `Pomelo.EntityFrameworkCore.MySql`: MySQL 提供者
- `Confluent.Kafka`: Kafka 客戶端
- `KubernetesClient`: Kubernetes API 客戶端

### 可觀測性
- `OpenTelemetry.*`: 分散式追蹤和指標
- `Serilog.*`: 結構化日誌記錄
- `Prometheus`: 指標收集

### API 和文件
- `Swashbuckle.AspNetCore`: OpenAPI/Swagger 文件
- `Asp.Versioning.*`: API 版本控制
- `Newtonsoft.Json`: JSON 序列化

### 測試框架
- `xunit`: 單元測試框架
- `FluentAssertions`: 斷言函式庫
- `NSubstitute`: 模擬框架

## 建置系統

### 專案結構
- 使用 .NET Solution 檔案 (`JKOPay.BatchSystem.sln`)
- 採用中央套件管理 (`Directory.Packages.props`)
- 支援多目標框架 (net8.0/net9.0)

### 常用指令

#### 建置和執行
```bash
# 還原套件
dotnet restore

# 建置整個解決方案
dotnet build

# 執行 API 專案
dotnet run --project src/JKOPay.BatchSystem.Api

# 執行測試
dotnet test

# 建立 Docker 映像
docker build -f src/JKOPay.BatchSystem.Api/Dockerfile -t jkopay.batchsystem.api .
```

#### 資料庫管理
```bash
# 更新資料庫內容
./script/update_dbcontext.sh

# Entity Framework 遷移
dotnet ef migrations add <MigrationName> --project src/JKOPay.BatchSystem.Core
dotnet ef database update --project src/JKOPay.BatchSystem.Api
```

#### 本地開發
```bash
# 使用 Docker Compose 啟動相依服務
docker-compose up -d mysql

# 設定環境變數
export ASPNETCORE_ENVIRONMENT=local
```

## 配置管理

### 配置檔案格式
- 主要使用 YAML 格式 (`appsettings.yaml`)
- 支援環境特定配置 (`appsettings.{env}.yaml`)
- 使用 `NetEscapades.Configuration.Yaml` 套件

### 環境變數
- `ASPNETCORE_ENVIRONMENT`: 執行環境 (local/development/sit/uat/prod)
- `DB`: 資料庫連線字串
- `KAFKA_USERNAME`/`KAFKA_PASSWORD`: Kafka 認證資訊

## 部署和 CI/CD

### 容器化
- 使用 Docker 進行容器化
- 支援多平台建置 (`Dockerfile_MultiPlatform`)
- 基於 Google Artifact Registry

### GitLab CI/CD
- 使用 `.gitlab-ci.yml` 定義流水線
- 支援多環境部署 (sit/uat/prod)
- 採用 GitOps 部署模式

### Kubernetes 部署
- 使用 Kustomize 進行配置管理
- 支援多環境覆蓋配置
- 整合 Argo Workflows 和 Argo Events
