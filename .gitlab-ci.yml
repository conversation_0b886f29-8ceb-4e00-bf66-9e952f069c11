include:
  - project: "runner-configs/gitlab-shared-steps"
    ref: feat/gitops
    file: "/gitops/gitops-definition.gitlab-ci.yml"

variables:
  BATCHSYSTEMPLATFORM_PACKAGE_VERSION : 0.0.12
  BATCHSYSTEMPLATFORM_PROJECT_NAME : JKOPay.Platform.BatchSystem

  NAMESPACE: foundation
  TRIGGER_REPO: "devops/gitops"
  # GAR
  GOOGLE_ARTIFACT_REPOSITORY: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository
  DEPLOY_APP:
    description: "部署的應用程式 (api、operator、job、package)？"
    value: "api"
  DEPLOY_ENVIRONMENT:
    description: "部署的環境（sit、uat、loadtest、prod）？"
    value: "sit"

stages:
  - build
  - deploy
  - pack_publish

.build_application_image_definition:
  extends:
    - .build_node_application_multiple_roles_image_and_push

build-application-image:
  extends:
    - .build_application_image_definition
  stage: build
  variables:
    BUILD_ENVIRONMENT: $DEPLOY_ENVIRONMENT
    DEPLOY_SERVICE: "batchsystem"
    SERVICE_ROLE: jkopay-batchsystem-$DEPLOY_APP
  rules:
    - if: $DEPLOY_APP == "api"
      variables:
        DOCKERFILE_APPLICATION_IMAGE_MULTIPLE_ROLE: "./src/JKOPay.BatchSystem.Api/Dockerfile"
    - if: $DEPLOY_APP == "job"
      variables:
        DOCKERFILE_APPLICATION_IMAGE_MULTIPLE_ROLE: "./src/JKOPay.BatchSystem.Job/Dockerfile"
    - if: $DEPLOY_APP == "operator"
      variables:
        DOCKERFILE_APPLICATION_IMAGE_MULTIPLE_ROLE: "./src/JKOPay.BatchSystem.Operator/Dockerfile"
  after_script:
    - env

trigger-deploy:
  extends:
    - .trigger_deploy_pipeline_definition
  stage: deploy
  needs:
    - build-application-image
  variables:
    _NAMESPACE: "${NAMESPACE}"
    _IMAGE_TAG: "${CI_COMMIT_SHA}"
    _SERVICE: "batchsystem"
    _DEPLOY_ENVIRONMENT: $DEPLOY_ENVIRONMENT
    _TRIGGER_PIPELINE_JOB: trigger-job-$DEPLOY_ENVIRONMENT
    _MULTIPLE_ROLES: "false"
    _SERVICE_ROLE: jkopay-batchsystem-$DEPLOY_APP
  rules:
    - if: '$DEPLOY_APP != "package"'


batchsystemplatform:
  stage: pack_publish
  image: mcr.microsoft.com/dotnet/sdk:8.0
  script:
    - dotnet nuget add source https://gitlab.jkopay.app/api/v4/projects/593/packages/nuget/index.json -n JKOPay
    - dotnet restore ./src/${BATCHSYSTEMPLATFORM_PROJECT_NAME}/${BATCHSYSTEMPLATFORM_PROJECT_NAME}.csproj
    - dotnet build -c Release ./src/${BATCHSYSTEMPLATFORM_PROJECT_NAME}/${BATCHSYSTEMPLATFORM_PROJECT_NAME}.csproj
    - dotnet pack -c Release -p:PackageVersion=${BATCHSYSTEMPLATFORM_PACKAGE_VERSION} -p:AssemblyVersion=${BATCHSYSTEMPLATFORM_PACKAGE_VERSION}.${CI_PIPELINE_IID} -p:FileVersion=${BATCHSYSTEMPLATFORM_PACKAGE_VERSION}.${CI_PIPELINE_IID} -p:IncludeSymbols=true -p:SymbolPackageFormat=snupkg -o release ./src/${BATCHSYSTEMPLATFORM_PROJECT_NAME}/${BATCHSYSTEMPLATFORM_PROJECT_NAME}.csproj
    - dotnet nuget push -s "${CI_API_V4_URL}/projects/${NUGET_PROJECT_ID}/packages/nuget/index.json" -k $CI_JOB_TOKEN "release/*.nupkg"

  rules:
    - if: '$DEPLOY_APP == "package"'
      when: always
