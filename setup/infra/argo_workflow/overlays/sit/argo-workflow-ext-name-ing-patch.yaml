apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: argo-workflow-ing
  namespace: foundation
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "*.jamis.app"
      secretName: jamis-tls
  rules:
    - host: argo-workflow.jamis.app
      http:
        paths:
          - pathType: ImplementationSpecific
            path: /
            backend:
              service:
                name: argo-server-proxy
                port:
                  number: 2746
