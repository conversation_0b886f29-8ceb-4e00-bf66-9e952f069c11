apiVersion: v1
kind: ConfigMap
metadata:
  name: workflow-controller-configmap
data:
  parallelism: "4"
  resourceRateLimit: |
    limit: 5
    burst: 2
  workflowDefaults: |
    metadata:
      annotations:
        argo: workflows
    spec:
      ttlStrategy:
        secondsAfterSuccess: 300
        secondsAfterFailure: 14400
      parallelism: 5
  metricsConfig: "# Enabled controls metric emission. Default is true, set \"enabled: false\" to turn off\nenabled: true\n# Path is the path where metrics are emitted. Must start with a \"/\". Default is \"/metrics\"\npath: /metrics\n# Port is the port where metrics are emitted. Default is \"9090\"\n# port: \n# MetricsTTL sets how often custom metrics are cleared from memory. Default is \"0\", metrics are never cleared\nmetricsTTL: \"10m\"\n# IgnoreErrors is a flag that instructs prometheus to ignore metric emission errors. Default is \"false\"\nignoreErrors: false\n# Use a self-signed cert for TLS, default false\nsecure: false\n    #magic___^_^___line\n# DEPRECATED: Legacy metrics are now removed, this field is ignored\ndisableLegacy: false\n    #magic___^_^___line\n"
  # telemetryConfig controls the path and port for prometheus telemetry. Telemetry is enabled and emitted in the same endpoint
  # as metrics by default, but can be overridden using this config.
  # telemetryConfig: |
  #   enabled: true
  #   path: /telemetry
  #   # port: 9090
  #   secure: true  # Use a self-signed cert for TLS, default false
