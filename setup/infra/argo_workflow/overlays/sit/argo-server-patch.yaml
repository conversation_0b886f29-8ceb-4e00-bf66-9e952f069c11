---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: argo-server
  namespace: argo
spec:
  selector:
    matchLabels:
      app: argo-server
  template:
    metadata:
      labels:
        app: argo-server
    spec:
      containers:
        - args:
            - server
            - "--auth-mode=server"
          env: []
          image: quay.io/argoproj/argocli:v3.5.6
          name: argo-server
          ports:
            - containerPort: 2746
              name: web
          readinessProbe:
            httpGet:
              path: /
              port: 2746
              scheme: HTTPS
            initialDelaySeconds: 10
            periodSeconds: 20
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
          volumeMounts:
            - mountPath: /tmp
              name: tmp
      nodeSelector:
        kubernetes.io/os: linux
      securityContext:
        runAsNonRoot: true
      serviceAccountName: argo-server
      volumes:
        - emptyDir: {}
          name: tmp
---
apiVersion: v1
kind: Service
metadata:
  name: argo-server
  namespace: argo
spec:
  ports:
    - name: web
      port: 2746
      targetPort: 2746
  selector:
    app: argo-server
