apiVersion: v1
kind: ConfigMap
metadata:
  name: workflow-controller-configmap
data:
  parallelism: "50"
  namespaceParallelism: "15"
  resourceRateLimit: |
    limit: 10
    burst: 5

  # Columns are custom columns that will be exposed in the Workflow List View.
  # (available since Argo v3.5)
  columns: |
    # Adds a column to the Workflow List View
    - # The name of this column, e.g., "Workflow Completed".
      name: Workflow Completed
      # The type of this column, "label" or "annotation".
      type: label
      # The key of the label or annotation, e.g., "workflows.argoproj.io/completed".
      key: workflows.argoproj.io/completed
  mainContainer: |+
    imagePullPolicy: IfNotPresent
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      readOnlyRootFilesystem: false
      runAsNonRoot: true
      runAsUser: 1000

  # executor controls how the init and wait container should be customized
  # (available since Argo v2.3)
  executor: |
    imagePullPolicy: IfNotPresent
    # args & env allows command line arguments and environment variables to be appended to the
    # executor container and is mainly used for development/debugging purposes.
    args:
    - --loglevel
    - debug
    - --gloglevel
    - "6"
    env:
    # ARGO_TRACE enables some tracing information for debugging purposes. Currently it enables
    # logging of S3 request/response payloads (including auth headers)
    - name: ARGO_TRACE
      value: "1"

  # PodGCGracePeriodSeconds specifies the duration in seconds before a terminating pod is forcefully killed.
  # Value must be non-negative integer. A zero value indicates that the pod will be forcefully terminated immediately.
  # Defaults to the Kubernetes default of 30 seconds.
  podGCGracePeriodSeconds: "60"

  # PodGCDeleteDelayDuration specifies the duration before pods in the GC queue get deleted.
  # Value must be non-negative. A zero value indicates that the pods will be deleted immediately.
  # Defaults to 5 seconds.
  podGCDeleteDelayDuration: 30s

  workflowDefaults: |
    metadata:
      annotations:
        argo: workflows
    spec:
      ttlStrategy:
        secondsAfterSuccess: 180
        secondsAfterFailure: 600
      parallelism: 5
  # telemetryConfig controls the path and port for prometheus telemetry. Telemetry is enabled and emitted in the same endpoint
  # as metrics by default, but can be overridden using this config.
  # telemetryConfig: |
  #   enabled: true
  #   path: /telemetry
  #   # port: 9090
  #   secure: true  # Use a self-signed cert for TLS, default false
  metricsConfig: |
    # Enabled controls the prometheus metric. Default is true, set "enabled: false" to turn off
    enabled: true
    path: /metrics
    port: 9090
    ignoreErrors: false
    secure: false
    metricsTTL: "30m"