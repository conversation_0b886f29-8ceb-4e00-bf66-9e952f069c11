apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-controller
  namespace: argo
spec:
  selector:
    matchLabels:
      app: workflow-controller
  template:
    metadata:
      labels:
        app: workflow-controller
    spec:
      containers:
      - name: workflow-controller
        args:
        # - "--namespaced"
        # - "--managed-namespace"
        # - "--managed-namespace=found"
        - "--managed-namespace=argo"
        # - "--managed-namespace=rd1"
        # - "--managed-namespace=rd2"
        # - "--managed-namespace=rd3"
        # - "--managed-namespace=rd4"
        # - "--managed-namespace=rd5"
        # - "--managed-namespace=web"
        command:
        - workflow-controller
        env:
        - name: LEADER_ELECTION_IDENTITY
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: TZ
          value: "Asia/Taipei"
        image: quay.io/argoproj/workflow-controller:v3.5.6
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /healthz
            port: 6060
          initialDelaySeconds: 90
          periodSeconds: 60
          timeoutSeconds: 30
        ports:
        - containerPort: 9090
          name: metrics
        - containerPort: 6060
        resources:
          limits:
            cpu: "500m"
            memory: "256Mi"
          requests:
            cpu: "250m"
            memory: "128Mi"
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
      nodeSelector:
        kubernetes.io/os: linux
      priorityClassName: workflow-controller
      securityContext:
        runAsNonRoot: true
      serviceAccountName: argo
