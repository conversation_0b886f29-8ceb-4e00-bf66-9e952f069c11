apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: eventbus.argoproj.io
spec:
  group: argoproj.io
  names:
    kind: EventBus
    listKind: EventBusList
    plural: eventbus
    shortNames:
    - eb
    singular: eventbus
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            x-kubernetes-preserve-unknown-fields: true
          status:
            type: object
            x-kubernetes-preserve-unknown-fields: true
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: eventsources.argoproj.io
spec:
  group: argoproj.io
  names:
    kind: EventSource
    listKind: EventSourceList
    plural: eventsources
    shortNames:
    - es
    singular: eventsource
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            x-kubernetes-preserve-unknown-fields: true
          status:
            type: object
            x-kubernetes-preserve-unknown-fields: true
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: sensors.argoproj.io
spec:
  group: argoproj.io
  names:
    kind: Sensor
    listKind: SensorList
    plural: sensors
    shortNames:
    - sn
    singular: sensor
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            x-kubernetes-preserve-unknown-fields: true
          status:
            type: object
            x-kubernetes-preserve-unknown-fields: true
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: argo-events-sa
  namespace: argo-events
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
  name: argo-events-aggregate-to-admin
rules:
- apiGroups:
  - argoproj.io
  resources:
  - sensors
  - sensors/finalizers
  - sensors/status
  - eventsources
  - eventsources/finalizers
  - eventsources/status
  - eventbus
  - eventbus/finalizers
  - eventbus/status
  verbs:
  - create
  - delete
  - deletecollection
  - get
  - list
  - patch
  - update
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    rbac.authorization.k8s.io/aggregate-to-edit: "true"
  name: argo-events-aggregate-to-edit
rules:
- apiGroups:
  - argoproj.io
  resources:
  - sensors
  - sensors/finalizers
  - sensors/status
  - eventsources
  - eventsources/finalizers
  - eventsources/status
  - eventbus
  - eventbus/finalizers
  - eventbus/status
  verbs:
  - create
  - delete
  - deletecollection
  - get
  - list
  - patch
  - update
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    rbac.authorization.k8s.io/aggregate-to-view: "true"
  name: argo-events-aggregate-to-view
rules:
- apiGroups:
  - argoproj.io
  resources:
  - sensors
  - sensors/finalizers
  - sensors/status
  - eventsources
  - eventsources/finalizers
  - eventsources/status
  - eventbus
  - eventbus/finalizers
  - eventbus/status
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: argo-events-role
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - argoproj.io
  resources:
  - sensors
  - sensors/finalizers
  - sensors/status
  - eventsources
  - eventsources/finalizers
  - eventsources/status
  - eventbus
  - eventbus/finalizers
  - eventbus/status
  verbs:
  - create
  - delete
  - deletecollection
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  - pods/exec
  - configmaps
  - services
  - persistentvolumeclaims
  verbs:
  - create
  - get
  - list
  - watch
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - create
  - get
  - list
  - update
  - patch
  - delete
- apiGroups:
  - apps
  resources:
  - deployments
  - statefulsets
  verbs:
  - create
  - get
  - list
  - watch
  - update
  - patch
  - delete
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: argo-events-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: argo-events-role
subjects:
- kind: ServiceAccount
  name: argo-events-sa
  namespace: argo-events
---
apiVersion: v1
data:
  controller-config.yaml: |
    eventBus:
      nats:
        versions:
        - version: 0.22.1
          natsStreamingImage: nats-streaming:0.22.1
          metricsExporterImage: natsio/prometheus-nats-exporter:0.8.0
      jetstream:
        # Default JetStream settings, could be overridden by EventBus JetStream specs
        settings: |
          # https://docs.nats.io/running-a-nats-service/configuration#jetstream
          # Only configure "max_memory_store" or "max_file_store", do not set "store_dir" as it has been hardcoded.
          # e.g. 1G. -1 means no limit, up to 75% of available memory
          max_memory_store: -1
          # e.g. 20G. -1 means no limit, Up to 1TB if available
          max_file_store: 1TB
        streamConfig: |
          # The default properties of the streams to be created in this JetStream service
          maxMsgs: 50000
          maxAge: 168h
          maxBytes: -1
          replicas: 3
          duplicates: 300s
        versions:
        - version: latest
          natsImage: nats:2.9.16
          metricsExporterImage: natsio/prometheus-nats-exporter:0.9.1
          configReloaderImage: natsio/nats-server-config-reloader:0.7.0
          startCommand: /nats-server
        - version: 2.8.1
          natsImage: nats:2.8.1
          metricsExporterImage: natsio/prometheus-nats-exporter:0.9.1
          configReloaderImage: natsio/nats-server-config-reloader:0.7.0
          startCommand: /nats-server
        - version: 2.8.1-alpine
          natsImage: nats:2.8.1-alpine
          metricsExporterImage: natsio/prometheus-nats-exporter:0.9.1
          configReloaderImage: natsio/nats-server-config-reloader:0.7.0
          startCommand: nats-server
        - version: 2.8.2
          natsImage: nats:2.8.2
          metricsExporterImage: natsio/prometheus-nats-exporter:0.9.1
          configReloaderImage: natsio/nats-server-config-reloader:0.7.0
          startCommand: /nats-server
        - version: 2.8.2-alpine
          natsImage: nats:2.8.2-alpine
          metricsExporterImage: natsio/prometheus-nats-exporter:0.9.1
          configReloaderImage: natsio/nats-server-config-reloader:0.7.0
          startCommand: nats-server
        - version: 2.9.1
          natsImage: nats:2.9.1
          metricsExporterImage: natsio/prometheus-nats-exporter:0.9.1
          configReloaderImage: natsio/nats-server-config-reloader:0.7.0
          startCommand: /nats-server
        - version: 2.9.12
          natsImage: nats:2.9.12
          metricsExporterImage: natsio/prometheus-nats-exporter:0.9.1
          configReloaderImage: natsio/nats-server-config-reloader:0.7.0
          startCommand: /nats-server
        - version: 2.9.16
          natsImage: nats:2.9.16
          metricsExporterImage: natsio/prometheus-nats-exporter:0.9.1
          configReloaderImage: natsio/nats-server-config-reloader:0.7.0
          startCommand: /nats-server
kind: ConfigMap
metadata:
  name: argo-events-controller-config
  namespace: argo-events
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager
  namespace: argo-events
spec:
  replicas: 1
  selector:
    matchLabels:
      app: controller-manager
  template:
    metadata:
      labels:
        app: controller-manager
    spec:
      containers:
      - args:
        - controller
        env:
        - name: ARGO_EVENTS_IMAGE
          value: quay.io/argoproj/argo-events:v1.9.0
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        image: quay.io/argoproj/argo-events:v1.9.0
        imagePullPolicy: Always
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 3
          periodSeconds: 3
        name: controller-manager
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 3
          periodSeconds: 3
        volumeMounts:
        - mountPath: /etc/argo-events
          name: controller-config-volume
      securityContext:
        runAsNonRoot: true
        runAsUser: 9731
      serviceAccountName: argo-events-sa
      volumes:
      - configMap:
          name: argo-events-controller-config
        name: controller-config-volume