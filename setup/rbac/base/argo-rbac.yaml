apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: batchsystem-operate-workflow-role
rules:
  - apiGroups:
      - argoproj.io
    verbs:
      - "*"
    resources:
      - workflows
      - workflowtemplates
      - cronworkflows
      - clusterworkflowtemplates
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: batchsystem-operate-workflow-rb
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: batchsystem-operate-workflow-role
subjects:
  - kind: ServiceAccount
    name: batchsystem-sa
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: batchsystem-executor-role
rules:
  - apiGroups:
      - argoproj.io
    resources:
      - workflowtaskresults
    verbs:
      - create
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: batchsystem-executor-role-rb
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: batchsystem-executor-role
subjects:
  - kind: ServiceAccount
    name: batchsystem-sa
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: batchsystem-patch-workflowtasksets-role
  namespace: rd2
rules:
  - apiGroups:
      - argoproj.io
    resources:
      - workflowtasksets/status
      - workflowartifactgctasks
      - workflowartifactgctasks/status
    verbs:
      - get
      - list
      - watch
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: batchsystem-patch-workflowtasksets-rb
  namespace: rd2
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: batchsystem-patch-workflowtasksets-role
subjects:
  - kind: ServiceAccount
    name: batchsystem-sa
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: batchsystem-secret-reader-role
rules:
  - apiGroups:
      - ""
    resources:
      - secrets
    verbs:
      - get
      - list
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: batchsystem-secret-reader-rb
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: batchsystem-secret-reader
subjects:
  - kind: ServiceAccount
    name: batchsystem-sa
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: batchsystem-workflowtaskset-status-patcher-role
rules:
  - apiGroups:
      - argoproj.io
    resources:
      - workflowtasksets/status
    verbs:
      - patch
---
# 這一段應該沒有作用，先註解掉
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: batchsystem-workflowtaskset-status-patcher-role-rb
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: batchsystem-workflowtaskset-status-patcher-role
subjects:
  - kind: ServiceAccount
    name: argo
    namespace: argo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: batchsystem-admin-argo-sa-rb
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: argo-aggregate-to-admin
subjects:
  - kind: ServiceAccount
    name: batchsystem-sa
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: read-secrets
rules:
  - apiGroups: [""]
    resources: ["secrets"]
    resourceNames: ["regcred"]  # 這裡指定 Secret 名稱
    verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: read-secrets-binding
subjects:
  - kind: ServiceAccount
    name: argo  # 這裡填入你的 ServiceAccount 名稱
    namespace: argo  # 這裡填入你的 ServiceAccount 所在命名空間
roleRef:
  kind: ClusterRole
  name: read-secrets
  apiGroup: rbac.authorization.k8s.io
