resources:
  - ../base
namespace: rd2
namePrefix: rd2-
patches:
  - target:
      kind: Secret
      name: batchsystem-sa-token
    patch: |
      - op: replace
        path: /metadata/annotations/kubernetes.io~1service-account.name
        value: rd2-batchsystem-sa
  - target:
      kind: ServiceAccount
      name: batchsystem-sa
    patch: |
      - op: replace
        path: /secrets/0/name
        value: rd2-batchsystem-sa-token
