apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: batchsystem-read-secret-cr
rules:
  - apiGroups:
      - ""
    resources:
      - secrets
    verbs:
      - get
      - list
      - watch
    resourceNames:
      - argo-workflows-agent-ca-certificates
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: batchsystem-argo-read-secret
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: batchsystem-read-secret-cr
subjects:
  - kind: ServiceAccount
    name: argo
    namespace: argo
