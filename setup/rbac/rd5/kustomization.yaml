resources:
- namespace.yaml
- ../base
namespace: rd5
namePrefix: rd5-
patches:
- target:
    kind: Secret
    name: batchsystem-sa-token
  patch: |
    - op: replace
      path: /metadata/annotations/kubernetes.io~1service-account.name
      value: rd5-batchsystem-sa
- target:
    kind: ServiceAccount
    name: batchsystem-sa
  patch: |
    - op: replace
      path: /secrets/0/name
      value: rd5-batchsystem-sa-token
