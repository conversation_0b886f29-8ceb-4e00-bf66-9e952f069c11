resources:
  - ../base
namespace: foundation
namePrefix: foundation-
patches:
  - target:
      kind: Secret
      name: batchsystem-sa-token
    patch: |
      - op: replace
        path: /metadata/annotations/kubernetes.io~1service-account.name
        value: foundation-batchsystem-sa
  - target:
      kind: ServiceAccount
      name: batchsystem-sa
    patch: |
      - op: replace
        path: /secrets/0/name
        value: foundation-batchsystem-sa-token
