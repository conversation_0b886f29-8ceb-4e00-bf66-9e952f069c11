apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    kubernetes.io/enforce-mountable-secrets: "false"
  name: foundation-batchsystem-sa
  namespace: foundation
secrets:
- apiVersion: v1
  kind: Secret
  name: foundation-batchsystem-sa-token
---
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    kubernetes.io/enforce-mountable-secrets: "false"
  name: rd1-batchsystem-sa
  namespace: rd1
secrets:
- apiVersion: v1
  kind: Secret
  name: rd1-batchsystem-sa-token
---
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    kubernetes.io/enforce-mountable-secrets: "false"
  name: rd2-batchsystem-sa
  namespace: rd2
secrets:
- apiVersion: v1
  kind: Secret
  name: rd2-batchsystem-sa-token
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: foundation-batchsystem-executor-role
  namespace: foundation
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflowtaskresults
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: foundation-batchsystem-operate-workflow-role
  namespace: foundation
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflows
  - workflowtemplates
  - cronworkflows
  - clusterworkflowtemplates
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: foundation-batchsystem-patch-workflowtasksets-role
  namespace: foundation
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflowtasksets/status
  - workflowartifactgctasks
  - workflowartifactgctasks/status
  verbs:
  - get
  - list
  - watch
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: foundation-batchsystem-secret-reader-role
  namespace: foundation
rules:
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: foundation-batchsystem-workflowtaskset-status-patcher-role
  namespace: foundation
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflowtasksets/status
  verbs:
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rd1-batchsystem-executor-role
  namespace: rd1
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflowtaskresults
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rd1-batchsystem-operate-workflow-role
  namespace: rd1
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflows
  - workflowtemplates
  - cronworkflows
  - clusterworkflowtemplates
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rd1-batchsystem-patch-workflowtasksets-role
  namespace: rd1
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflowtasksets/status
  - workflowartifactgctasks
  - workflowartifactgctasks/status
  verbs:
  - get
  - list
  - watch
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rd1-batchsystem-secret-reader-role
  namespace: rd1
rules:
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rd1-batchsystem-workflowtaskset-status-patcher-role
  namespace: rd1
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflowtasksets/status
  verbs:
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rd2-batchsystem-executor-role
  namespace: rd2
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflowtaskresults
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rd2-batchsystem-operate-workflow-role
  namespace: rd2
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflows
  - workflowtemplates
  - cronworkflows
  - clusterworkflowtemplates
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rd2-batchsystem-patch-workflowtasksets-role
  namespace: rd2
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflowtasksets/status
  - workflowartifactgctasks
  - workflowartifactgctasks/status
  verbs:
  - get
  - list
  - watch
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rd2-batchsystem-secret-reader-role
  namespace: rd2
rules:
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rd2-batchsystem-workflowtaskset-status-patcher-role
  namespace: rd2
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflowtasksets/status
  verbs:
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: batchsystem-read-secret-cr
rules:
- apiGroups:
  - ""
  resourceNames:
  - argo-workflows-agent-ca-certificates
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: foundation-read-secrets
rules:
- apiGroups:
  - ""
  resourceNames:
  - regcred
  resources:
  - secrets
  verbs:
  - get
  - list
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: rd1-read-secrets
rules:
- apiGroups:
  - ""
  resourceNames:
  - regcred
  resources:
  - secrets
  verbs:
  - get
  - list
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: rd2-read-secrets
rules:
- apiGroups:
  - ""
  resourceNames:
  - regcred
  resources:
  - secrets
  verbs:
  - get
  - list
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: foundation-batchsystem-admin-argo-sa-rb
  namespace: foundation
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: argo-aggregate-to-admin
subjects:
- kind: ServiceAccount
  name: foundation-batchsystem-sa
  namespace: foundation
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: foundation-batchsystem-executor-role-rb
  namespace: foundation
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: foundation-batchsystem-executor-role
subjects:
- kind: ServiceAccount
  name: foundation-batchsystem-sa
  namespace: foundation
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: foundation-batchsystem-operate-workflow-rb
  namespace: foundation
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: foundation-batchsystem-operate-workflow-role
subjects:
- kind: ServiceAccount
  name: foundation-batchsystem-sa
  namespace: foundation
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: foundation-batchsystem-patch-workflowtasksets-rb
  namespace: foundation
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: foundation-batchsystem-patch-workflowtasksets-role
subjects:
- kind: ServiceAccount
  name: foundation-batchsystem-sa
  namespace: foundation
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: foundation-batchsystem-secret-reader-rb
  namespace: foundation
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: batchsystem-secret-reader
subjects:
- kind: ServiceAccount
  name: foundation-batchsystem-sa
  namespace: foundation
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: foundation-batchsystem-workflowtaskset-status-patcher-role-rb
  namespace: foundation
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: foundation-batchsystem-workflowtaskset-status-patcher-role
subjects:
- kind: ServiceAccount
  name: argo
  namespace: argo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd1-batchsystem-admin-argo-sa-rb
  namespace: rd1
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: argo-aggregate-to-admin
subjects:
- kind: ServiceAccount
  name: rd1-batchsystem-sa
  namespace: rd1
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd1-batchsystem-executor-role-rb
  namespace: rd1
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: rd1-batchsystem-executor-role
subjects:
- kind: ServiceAccount
  name: rd1-batchsystem-sa
  namespace: rd1
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd1-batchsystem-operate-workflow-rb
  namespace: rd1
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: rd1-batchsystem-operate-workflow-role
subjects:
- kind: ServiceAccount
  name: rd1-batchsystem-sa
  namespace: rd1
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd1-batchsystem-patch-workflowtasksets-rb
  namespace: rd1
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: rd1-batchsystem-patch-workflowtasksets-role
subjects:
- kind: ServiceAccount
  name: rd1-batchsystem-sa
  namespace: rd1
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd1-batchsystem-secret-reader-rb
  namespace: rd1
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: batchsystem-secret-reader
subjects:
- kind: ServiceAccount
  name: rd1-batchsystem-sa
  namespace: rd1
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd1-batchsystem-workflowtaskset-status-patcher-role-rb
  namespace: rd1
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: rd1-batchsystem-workflowtaskset-status-patcher-role
subjects:
- kind: ServiceAccount
  name: argo
  namespace: argo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd2-batchsystem-admin-argo-sa-rb
  namespace: rd2
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: argo-aggregate-to-admin
subjects:
- kind: ServiceAccount
  name: rd2-batchsystem-sa
  namespace: rd2
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd2-batchsystem-executor-role-rb
  namespace: rd2
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: rd2-batchsystem-executor-role
subjects:
- kind: ServiceAccount
  name: rd2-batchsystem-sa
  namespace: rd2
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd2-batchsystem-operate-workflow-rb
  namespace: rd2
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: rd2-batchsystem-operate-workflow-role
subjects:
- kind: ServiceAccount
  name: rd2-batchsystem-sa
  namespace: rd2
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd2-batchsystem-patch-workflowtasksets-rb
  namespace: rd2
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: rd2-batchsystem-patch-workflowtasksets-role
subjects:
- kind: ServiceAccount
  name: rd2-batchsystem-sa
  namespace: rd2
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd2-batchsystem-secret-reader-rb
  namespace: rd2
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: batchsystem-secret-reader
subjects:
- kind: ServiceAccount
  name: rd2-batchsystem-sa
  namespace: rd2
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rd2-batchsystem-workflowtaskset-status-patcher-role-rb
  namespace: rd2
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: rd2-batchsystem-workflowtaskset-status-patcher-role
subjects:
- kind: ServiceAccount
  name: argo
  namespace: argo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: batchsystem-argo-read-secret
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: batchsystem-read-secret-cr
subjects:
- kind: ServiceAccount
  name: argo
  namespace: argo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: foundation-read-secrets-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: foundation-read-secrets
subjects:
- kind: ServiceAccount
  name: argo
  namespace: argo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: rd1-read-secrets-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: rd1-read-secrets
subjects:
- kind: ServiceAccount
  name: argo
  namespace: argo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: rd2-read-secrets-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: rd2-read-secrets
subjects:
- kind: ServiceAccount
  name: argo
  namespace: argo
---
apiVersion: v1
kind: Secret
metadata:
  annotations:
    kubernetes.io/service-account.name: foundation-batchsystem-sa
  name: foundation-batchsystem-sa-token
  namespace: foundation
type: kubernetes.io/service-account-token
---
apiVersion: v1
kind: Secret
metadata:
  annotations:
    kubernetes.io/service-account.name: rd1-batchsystem-sa
  name: rd1-batchsystem-sa-token
  namespace: rd1
type: kubernetes.io/service-account-token
---
apiVersion: v1
kind: Secret
metadata:
  annotations:
    kubernetes.io/service-account.name: rd2-batchsystem-sa
  name: rd2-batchsystem-sa-token
  namespace: rd2
type: kubernetes.io/service-account-token
