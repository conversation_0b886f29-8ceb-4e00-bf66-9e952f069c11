apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: rd1-job-workflow-template
  namespace: rd1
spec:
  serviceAccountName: rd1-batchsystem-sa
  arguments:
    parameters:
      - name: jobData
        value: default
  templates:
    - name: main
      inputs:
        parameters:
          - name: jobData
      container:
        image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.job:0.0.0.27
        args:
          [
            "{{workflow.uid}}",
            "{{workflow.name}}",
            "{{workflow.namespace}}",
            "{{inputs.parameters.jobData}}",
          ]
        env: # 新增 env 部分
          - name: NETCORE_ENVIRONMENT
            value: development
          - name: IS_BASE64
            value: "true"
        # - name: KAFKA_USERNAME
        #   valueFrom:
        #     secretKeyRef:
        #       name: kafka-user
        #       key: user
        # - name: KAFKA_PASSWORD
        #   valueFrom:
        #     secretKeyRef:
        #       name: kafka-user
        #       key: password