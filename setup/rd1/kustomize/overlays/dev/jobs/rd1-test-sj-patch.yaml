apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  name: rd1-test-sj
  namespace: rd1
  annotations:
    workflows.argoproj.io/description: |
      Shared CronWorkflow Process
    workflows.argoproj.io/version: ">= 3.2.0"
spec:
  serviceAccountName: rd1-batchsystem-sa
  arguments:
    parameters:
      - name: templateName
        value: default
      - name: entryPoint
        value: default
  schedule: "* */20 * * *"
  workflowSpec:
    workflowTemplateRef:
      name: dev-rd1-batchsystem-main-cron-workflow-template
    arguments:
      parameters:
        #這邊要跟 metadata 的 name 一樣
        - name: jobName
          value: rd1-test-sj
        - name: templateName
          value: dev-rd1-job-workflow-template
        - name: jobData
          value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInRlc3Rqb2IiIH0=
        - name: priority
          value: schedule
        - name: team
          value: rd1