apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ./no-prefix
  - ./prefixed
  - ./batchsystem-secret.yaml
# patches:
  # 暫時禁用舊的 patches，需要根據新架構重新設計
  # - path: ./deployment-api-patch.yaml
  # - path: ./deployment-opa-patch.yaml
  # - path: ./foundation-general-schedule-job-patch.yaml
  # - path: ./foundation-update-job-status-template-patch.yaml

patches:
  - target:
      kind: EventSource
      name: kafka-eventsource
    patch: |
      - op: replace
        path: /spec/kafka/priority/sasl/passwordSecret/name
        value: sit-kafka-user
  - target:
      kind: EventSource
      name: kafka-eventsource
    patch: |
      - op: replace
        path: /spec/kafka/priority/sasl/userSecret/name
        value: sit-kafka-user
  - target:
      kind: Sensor
      name: kafka-sensor
    patch: |
      - op: replace
        path: /spec/dependencies/0/eventSourceName
        value: sit-kafka-eventsource
