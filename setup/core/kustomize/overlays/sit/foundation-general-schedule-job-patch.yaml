apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  name: foundation-general-schedule-job
  namespace: foundation
  annotations:
    workflows.argoproj.io/description: |
      Shared CronWorkflow Process
    workflows.argoproj.io/version: ">= 3.2.0"
spec:
  serviceAccountName: foundation-batchsystem-sa
  arguments:
    parameters:
      - name: templateName
        value: default
      - name: entryPoint
        value: default
  schedule: "* */20 * * *"
  workflowSpec:
    workflowTemplateRef:
      name: dev-foundation-batchsystem-main-cron-workflow-template
    arguments:
      parameters:
        - name: jobName
          value: foundation-general-schedule-job
        - name: templateName
          value: dev-foundation-workflow-template-update-job-status
        - name: jobData
          value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInVwZGF0ZXN0YXR1c2pvYiIgfQ==
        - name: priority
          value: schedule
        - name: team
          value: found
