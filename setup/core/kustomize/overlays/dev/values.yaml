apiVersion: v1
kind: Service
metadata:
  name: dev-foundation-jkopay-batchsystem-api-svc
  namespace: foundation
spec:
  ports:
    - name: http-port
      port: 9090
      protocol: TCP
      targetPort: 9090
    - name: metrics-port
      port: 9091
      protocol: TCP
      targetPort: 9091
  selector:
    app: foundation-jkopay-batchsystem-api
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dev-foundation-jkopay-batchsystem-api
  namespace: foundation
spec:
  replicas: 1
  selector:
    matchLabels:
      app: foundation-jkopay-batchsystem-api
  template:
    metadata:
      annotations:
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/agent-inject-command-env-config: cat /vault/secrets/env-config
        vault.hashicorp.com/agent-inject-secret-env-config: secrets/data/sit/foundation/app/jkopay-batchsystem-api
        vault.hashicorp.com/agent-inject-template-env-config: |
          {{- with secret "secrets/data/sit/foundation/app/jkopay-batchsystem-api" -}}
          #!/bin/sh
          {{- range $k, $v := .Data.data }}
          export {{ $k }}='{{ $v }}'
          {{- end }}
          exec "$@"
          {{- end }}
        vault.hashicorp.com/role: foundation
      labels:
        app: foundation-jkopay-batchsystem-api
    spec:
      containers:
        - command:
            - /bin/sh
            - -c
            - ' if [ -f "/vault/secrets/env-config" ]; then source "/vault/secrets/env-config";
              fi; env; dotnet JKOPay.BatchSystem.Api.dll {{workflow.uid}} {{workflow.name}}
              {{workflow.namespace}} {{inputs.parameters.jobData}}'
          env:
            - name: ASPNETCORE_ENVIRONMENT
              value: development
            - name: TEST
              value: test
          image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.api:0.0.0.160
          livenessProbe:
            httpGet:
              path: /healthz/liveness
              port: 9090
            initialDelaySeconds: 50
            periodSeconds: 10
          name: jkopay-batchsystem-api
          ports:
            - containerPort: 9090
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz/liveness
              port: 9090
            initialDelaySeconds: 15
            periodSeconds: 5
            successThreshold: 3
            timeoutSeconds: 3
      serviceAccountName: foundation-batchsystem-sa
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dev-foundation-jkopay-batchsystem-operator
  namespace: foundation
spec:
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: foundation-jkopay-batchsystem-operator
  template:
    metadata:
      labels:
        app: foundation-jkopay-batchsystem-operator
    spec:
      containers:
        - env:
            - name: NETCORE_ENVIRONMENT
              value: development
            - name: NAMESPACE
              value: foundation
          image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.operator:0.0.0.81
          livenessProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 20
          name: jkopay-batchsystem-operator
          readinessProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 10
      serviceAccountName: foundation-batchsystem-sa
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dev-rd1-jkopay-batchsystem-operator
  namespace: rd1
spec:
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: rd1-jkopay-batchsystem-operator
  template:
    metadata:
      labels:
        app: rd1-jkopay-batchsystem-operator
    spec:
      containers:
        - env:
            - name: NETCORE_ENVIRONMENT
              value: development
            - name: NAMESPACE
              value: rd1
          image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.operator:0.0.0.81
          livenessProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 20
          name: jkopay-batchsystem-operator
          readinessProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 10
      serviceAccountName: rd1-batchsystem-sa
---
apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  annotations:
    workflows.argoproj.io/description: |
      Schedule job to periodically call scrape-schedule-job-status-job-wt
    workflows.argoproj.io/version: ">= 3.2.0"
  labels:
    app: foundation-scrape-schedule-job-status-job-sj
    developer: foundation
    environment: dev
    service: schedule-job
  name: dev-foundation-scrape-schedule-job-status-job-sj
  namespace: foundation
spec:
  schedule: "*/5 * * * *"
  workflowSpec:
    arguments:
      parameters:
        - name: jobName
          value: foundation-scrape-schedule-job-status-job-sj
        - name: templateName
          value: dev-foundation-scrape-schedule-job-status-job-wt
        - name: jobData
          value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInNjcmFwZS1zY2hlZHVsZS1qb2Itc3RhdHVzLWpvYiIgfQ==
        - name: priority
          value: schedule
        - name: team
          value: foundation
    metrics:
      prometheus:
        - counter:
            value: "1"
          help: Count of execution by result status
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{status}}"
            - key: workflowId
              value: "{{workflow.uid}}"
            - key: scheduledTime
              value: "{{workflow.scheduledTime}}"
          name: cronwf_execute_result_counter
        - gauge:
            realtime: false
            value: "{{workflow.duration}}"
          help: Duration of cron workflow execution
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{status}}"
          name: cronwf_execute_duration
    workflowTemplateRef:
      name: dev-foundation-batchsystem-main-cron-workflow-template
---
apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  annotations:
    workflows.argoproj.io/description: |
      Shared CronWorkflow Process
    workflows.argoproj.io/version: ">= 3.2.0"
  labels:
    environment: dev
  name: dev-foundation-test-job-sj
  namespace: foundation
spec:
  schedule: "*/2 */5 * * *"
  workflowSpec:
    arguments:
      parameters:
        - name: templateName
          value: dev-foundation-job-wt
        - name: jobData
          value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInRlc3Rqb2IiIH0=
        - name: priority
          value: schedule
        - name: team
          value: "{{workflow.namespace}}"
    metrics:
      prometheus:
        - counter:
            value: "1"
          help: Count of execution by result status
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{workflow.status}}"
            - key: team
              value: "{{workflow.parameters.team}}"
          name: cronworkflow_result_counter
        - gauge:
            realtime: true
            value: "{{workflow.duration}}"
          help: Duration gauge by workflow name and status
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{status}}"
            - key: team
              value: "{{workflow.parameters.team}}"
          name: wf_exec_duration_gauge
    workflowTemplateRef:
      name: batchsystem-main-cron-workflow-template
---
apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  annotations:
    workflows.argoproj.io/description: |
      Shared CronWorkflow Process
    workflows.argoproj.io/version: ">= 3.2.0"
  labels:
    environment: dev
  name: dev-rd1-test-job-sj
  namespace: rd1
spec:
  schedule: "*/5 */5 * * *"
  workflowSpec:
    arguments:
      parameters:
        - name: jobName
          value: rd1-test-job-sj
        - name: templateName
          value: dev-rd1-job-wt
        - name: jobData
          value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInNsYWNram9iIiB9
        - name: priority
          value: schedule
        - name: team
          value: rd1
    metrics:
      prometheus:
        - counter:
            value: "1"
          help: Count of execution by result status
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{status}}"
            - key: workflowId
              value: "{{workflow.uid}}"
            - key: scheduledTime
              value: "{{workflow.scheduledTime}}"
          name: cronwf_execute_result_counter
        - gauge:
            realtime: false
            value: "{{workflow.duration}}"
          help: Duration of cron workflow execution
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{status}}"
          name: cronwf_execute_duration
    workflowTemplateRef:
      name: dev-rd1-batchsystem-main-cron-workflow-template
---
apiVersion: argoproj.io/v1alpha1
kind: EventSource
metadata:
  name: dev-kafka-eventsource
  namespace: argo
spec:
  eventBusName: default
  kafka:
    priority:
      connectionBackoff:
        duration: 10s
        factor: 2
        jitter: 0.2
        steps: 5
      jsonBody: false
      partition: "0"
      sasl:
        mechanism: PLAIN
        passwordSecret:
          key: KAFKA_PASSWORD
          name: kafka-user
        userSecret:
          key: KAFKA_USERNAME
          name: kafka-user
      topic: foundation_batchsystem_workflow_dev
      url: ka1.jkopay.app:9093,ka2.jkopay.app:9093,ka3.jkopay.app:9093
  serviceAccountName: argo
---
apiVersion: argoproj.io/v1alpha1
kind: Sensor
metadata:
  name: dev-kafka-sensor
  namespace: argo
spec:
  dependencies:
    - eventName: priority
      eventSourceName: dev-kafka-eventsource
      name: batchsystem-dep-kafka
  eventBusName: default
  template:
    serviceAccountName: argo
  triggers:
    - template:
        argoWorkflow:
          operation: submit
          parameters:
            - dest: spec.arguments.parameters.0.value
              src:
                dataKey: body
                dependencyName: batchsystem-dep-kafka
            - dest: spec.arguments.parameters.1.value
              src:
                dataKey: headers.jobId
                dependencyName: batchsystem-dep-kafka
            - dest: spec.arguments.parameters.2.value
              src:
                dataKey: headers.templateName
                dependencyName: batchsystem-dep-kafka
            - dest: spec.arguments.parameters.3.value
              src:
                dataKey: headers.priority
                dependencyName: batchsystem-dep-kafka
            - dest: spec.workflowTemplateRef.name
              src:
                dataKey: headers.entry
                dependencyName: batchsystem-dep-kafka
            - dest: spec.ServiceAccountName
              src:
                dataKey: headers.teamAccount
                dependencyName: batchsystem-dep-kafka
            - dest: metadata.namespace
              src:
                dataKey: headers.team
                dependencyName: batchsystem-dep-kafka
            - dest: metadata.name
              src:
                dataKey: headers.name
                dependencyName: batchsystem-dep-kafka
          resource: workflows
          source:
            resource:
              apiVersion: argoproj.io/v1alpha1
              kind: Workflow
              metadata:
                generateName: kafka-workflow-
              spec:
                arguments:
                  parameters:
                    - name: jobData
                    - name: jobId
                    - name: templateName
                    - name: priority
                entryPoint: main
        name: kafka-workflow-trigger
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    batchsystem.owner: batchsystem
  name: dev-foundation-batchsystem-workflow-template
  namespace: foundation
spec:
  arguments:
    parameters:
      - name: jobId
        value: default
      - name: templateName
        value: default
      - name: entryPoint
        value: main
      - name: jobData
        value: default
      - name: priority
        value: default
      - name: apiUrl
        value: dev-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  serviceAccountName: foundation-batchsystem-sa
  templates:
    - dag:
        tasks:
          - arguments:
              parameters:
                - name: jobId
                  value: "{{workflow.parameters.jobId}}"
                - name: status
                  value: WorkflowStarted
                - name: priority
                  value: "{{workflow.parameters.priority}}"
            name: started-status
            template: update-status
          - arguments:
              parameters:
                - name: jobData
                  value: "{{workflow.parameters.jobData}}"
            dependencies:
              - started-status
            name: team-job
            templateRef:
              name: "{{workflow.parameters.templateName}}"
              template: "{{workflow.parameters.entryPoint}}"
          - arguments:
              parameters:
                - name: jobId
                  value: "{{workflow.parameters.jobId}}"
                - name: status
                  value: WorkflowComplete
                - name: priority
                  value: "{{workflow.parameters.priority}}"
            dependencies:
              - team-job
            name: complete-workflow
            template: update-status
      metrics:
        prometheus:
          - counter:
              value: "1"
            help: Count of execution by result status
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
              - key: result
                value: "{{status}}"
              - key: crateTimestamp
                value: "{{workflow.creationTimestamp}}"
              - key: finishTimestamp
                value: "{{workflow.status.finishedAt}}"
            name: wf_execute_result_counter
          - gauge:
              realtime: true
              value: "{{workflow.duration}}"
            help: Time of execution by result status
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
            name: wf_execution_time
      name: main
    - http:
        body: |
          {
            "jobId": "{{inputs.parameters.jobId}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }
        headers:
          - name: Content-Type
            value: application/json
        method: POST
        successCondition: response.body contains "0001"
        timeoutSeconds: 10
        url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
      inputs:
        parameters:
          - name: jobId
          - name: status
          - name: priority
      name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: dev-foundation-job-wt
  namespace: foundation
spec:
  arguments:
    parameters:
      - name: jobData
        value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogImxpbmVqb2IiIH0=
  serviceAccountName: foundation-batchsystem-sa
  templates:
    - container:
        command:
          - /bin/sh
          - -c
          - ' if [ -f "/vault/secrets/env-config" ]; then source "/vault/secrets/env-config";
            fi; dotnet JKOPay.BatchSystem.Job.dll {{workflow.uid}} {{workflow.name}} {{workflow.namespace}}
            {{inputs.parameters.jobData}}'
        env:
          - name: NETCORE_ENVIRONMENT
            value: development
          - name: IS_BASE64
            value: "true"
        image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.job:0.0.0.57
      inputs:
        parameters:
          - name: jobData
      metadata:
        annotations:
          vault.hashicorp.com/agent-inject: "true"
          vault.hashicorp.com/agent-inject-command-env-config: cat /vault/secrets/env-config
          vault.hashicorp.com/agent-inject-secret-env-config: secrets/data/sit/foundation/app/jkopay-batchsystem-api
          vault.hashicorp.com/agent-inject-template-env-config: |
            {{- with secret "secrets/data/sit/foundation/app/jkopay-batchsystem-api" -}}
            #!/bin/sh
            {{- range $k, $v := .Data.data }}
            export {{ $k }}='{{ $v }}'
            {{- end }}
            exec "$@"
            {{- end }}
          vault.hashicorp.com/role: foundation
      metrics:
        prometheus:
          - counter:
              value: "1"
            help: Count of execution by result status
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: name
                value: "{{workflow.parameters.templateName}}"
              - key: status
                value: "{{status}}"
            name: job_execute_result_counter
          - gauge:
              realtime: false
              value: "{{duration}}"
            help: Duration of workflow execution
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: name
                value: "{{workflow.parameters.templateName}}"
              - key: status
                value: "{{status}}"
            name: job_execute_duration
      name: main
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app: foundation-scrape-schedule-job-status-job-wt
    developer: foundation
    environment: dev
  name: dev-foundation-scrape-schedule-job-status-job-wt
  namespace: foundation
spec:
  serviceAccountName: foundation-batchsystem-sa
  templates:
    - name: main
      steps:
        - - arguments:
              parameters:
                - name: url
                  value: http://dev-foundation-jkopay-batchsystem-api-svc.foundation:9090/api/v1/meter/scheduleJobList
            name: call-api
            template: http-request
    - http:
        method: GET
        successCondition: response.statusCode == 200
        url: "{{inputs.parameters.url}}"
      inputs:
        parameters:
          - name: url
      name: http-request
      outputs:
        parameters:
          - name: response-body
            valueFrom:
              jsonPath: "{$.result}"
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    batchsystem.owner: batchsystem
  name: dev-rd1-batchsystem-workflow-template
  namespace: rd1
spec:
  arguments:
    parameters:
      - name: jobId
        value: default
      - name: templateName
        value: default
      - name: entryPoint
        value: main
      - name: jobData
        value: default
      - name: priority
        value: default
      - name: apiUrl
        value: dev-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  serviceAccountName: rd1-batchsystem-sa
  templates:
    - dag:
        tasks:
          - arguments:
              parameters:
                - name: jobId
                  value: "{{workflow.parameters.jobId}}"
                - name: status
                  value: WorkflowStarted
                - name: priority
                  value: "{{workflow.parameters.priority}}"
            name: started-status
            template: update-status
          - arguments:
              parameters:
                - name: jobData
                  value: "{{workflow.parameters.jobData}}"
            dependencies:
              - started-status
            name: team-job
            templateRef:
              name: "{{workflow.parameters.templateName}}"
              template: "{{workflow.parameters.entryPoint}}"
          - arguments:
              parameters:
                - name: jobId
                  value: "{{workflow.parameters.jobId}}"
                - name: status
                  value: WorkflowComplete
                - name: priority
                  value: "{{workflow.parameters.priority}}"
            dependencies:
              - team-job
            name: complete-workflow
            template: update-status
      metrics:
        prometheus:
          - counter:
              value: "1"
            help: Count of execution by result status
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
              - key: result
                value: "{{status}}"
              - key: crateTimestamp
                value: "{{workflow.creationTimestamp}}"
              - key: finishTimestamp
                value: "{{workflow.status.finishedAt}}"
            name: wf_execute_result_counter
          - gauge:
              realtime: true
              value: "{{workflow.duration}}"
            help: Time of execution by result status
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
            name: wf_execution_time
      name: main
    - http:
        body: |
          {
            "jobId": "{{inputs.parameters.jobId}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }
        headers:
          - name: Content-Type
            value: application/json
        method: POST
        successCondition: response.body contains "0001"
        timeoutSeconds: 10
        url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
      inputs:
        parameters:
          - name: jobId
          - name: status
          - name: priority
      name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: dev-rd1-job-wt
  namespace: rd1
spec:
  arguments:
    parameters:
      - name: jobData
        value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogImxpbmVqb2IiIH0=
  serviceAccountName: rd1-batchsystem-sa
  templates:
    - container:
        command:
          - /bin/sh
          - -c
          - ' if [ -f "/vault/secrets/env-config" ]; then source "/vault/secrets/env-config";
            fi; dotnet JKOPay.BatchSystem.Job.dll {{workflow.uid}} {{workflow.name}} {{workflow.namespace}}
            {{inputs.parameters.jobData}}'
        env:
          - name: NETCORE_ENVIRONMENT
            value: development
          - name: IS_BASE64
            value: "true"
        image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.job:0.0.0.57
      inputs:
        parameters:
          - name: jobData
      metadata:
        annotations:
          vault.hashicorp.com/agent-inject: "true"
          vault.hashicorp.com/agent-inject-command-env-config: cat /vault/secrets/env-config
          vault.hashicorp.com/agent-inject-secret-env-config: secrets/data/sit/foundation/app/jkopay-batchsystem-api
          vault.hashicorp.com/agent-inject-template-env-config: |
            {{- with secret "secrets/data/sit/foundation/app/jkopay-batchsystem-api" -}}
            #!/bin/sh
            {{- range $k, $v := .Data.data }}
            export {{ $k }}='{{ $v }}'
            {{- end }}
            exec "$@"
            {{- end }}
          vault.hashicorp.com/role: foundation
      metrics:
        prometheus:
          - counter:
              value: "1"
            help: Count of execution by result status
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: name
                value: "{{workflow.parameters.templateName}}"
              - key: status
                value: "{{status}}"
            name: job_execute_result_counter
          - gauge:
              realtime: false
              value: "{{duration}}"
            help: Duration of workflow execution
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: name
                value: "{{workflow.parameters.templateName}}"
              - key: status
                value: "{{status}}"
            name: job_execute_duration
      name: main
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: dev-rd1-test-wt
  namespace: rd1
spec:
  serviceAccountName: rd1-batchsystem-sa
  templates:
    - container:
        command:
          - /bin/sh
          - -c
          - source /vault/secrets/env-config && env && while true; do sleep 3600; done
        image: byrnedo/alpine-curl
      metadata:
        annotations:
          vault.hashicorp.com/agent-inject: "true"
          vault.hashicorp.com/agent-inject-command-env-config: cat /vault/secrets/env-config
          vault.hashicorp.com/agent-inject-template-env-config: |
            {{- with secret "internal/data/database/config" -}}
            #!/bin/sh
            {{- range $k, $v := .Data.data }}
            export {{ $k }}='{{ $v }}'
            {{- end }}
            exec "$@"
            {{- end }}
          vault.hashicorp.com/role: internal-app
      name: main
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    batchsystem.owner: batchsystem
    environment: dev
  name: batchsystem-main-cron-workflow-template
spec:
  arguments:
    parameters:
      - name: jobName
        value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
      - name: templateName
        value: default
      - name: jobData
        value: default
      - name: priority
        value: default
      - name: team
        value: "{{workflow.namespace}}"
      - name: environment
        value: "{{workflow.labels.environment}}"
      - name: apiUrl
        value: "{{workflow.labels.environment}}-foundation-jkopay-batchsystem-api-svc.svc.cluster.local:9090"
  entrypoint: main
  serviceAccountName: batchsystem-sa
  templates:
    - dag:
        tasks:
          - arguments:
              parameters:
                - name: jobName
                  value: "{{workflow.parameters.jobName}}"
                - name: status
                  value: WorkflowStarted
                - name: priority
                  value: "{{workflow.parameters.priority}}"
                - name: templateName
                  value: "{{workflow.parameters.templateName}}"
                - name: team
                  value: "{{workflow.parameters.team}}"
            name: create-job-record
            template: create-job-record
          - arguments:
              parameters:
                - name: jobId
                  value: "{{tasks.create-job-record.outputs.parameters.jobId}}"
                - name: status
                  value: WorkflowStarted
                - name: priority
                  value: "{{workflow.parameters.priority}}"
            dependencies:
              - create-job-record
            name: started-status
            template: update-status
          - arguments:
              parameters:
                - name: jobData
                  value: "{{workflow.parameters.jobData}}"
            dependencies:
              - started-status
            name: team-job
            templateRef:
              name: "{{workflow.parameters.templateName}}"
              template: main
          - arguments:
              parameters:
                - name: jobId
                  value: "{{tasks.create-job-record.outputs.parameters.jobId}}"
                - name: status
                  value: WorkflowComplete
                - name: priority
                  value: "{{workflow.parameters.priority}}"
            dependencies:
              - team-job
            name: complete-workflow
            template: update-status
      metrics:
        prometheus:
          - counter:
              value: "1"
            help: Count of execution by result status
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
              - key: result
                value: "{{status}}"
              - key: crateTimestamp
                value: "{{workflow.creationTimestamp}}"
              - key: finishTimestamp
                value: "{{workflow.status.finishedAt}}"
            name: wf_execute_result_counter
          - gauge:
              realtime: true
              value: "{{workflow.duration}}"
            help: Time of execution by result status
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
            name: wf_execution_time
      name: main
    - container:
        args:
          - |
            response=$(curl --silent --request POST --url http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule \
            --header 'Accept: */*' \
            --header 'Content-Type: application/json' \
            --header 'User-Agent: httpyac' \
            --data '{
              "jobName": "{{inputs.parameters.jobName}}",
              "workflowId": "{{workflow.uid}}",
              "workflowName": "{{workflow.name}}",
              "workflowTemplateName": "{{inputs.parameters.templateName}}",
              "status": "{{inputs.parameters.status}}",
              "priority": "{{inputs.parameters.priority}}",
              "team": "{{inputs.parameters.team}}"
            }')
            echo "Full Response: $response"
            jobId=$(echo $response | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')
            if [ -z "$jobId" ]; then
              echo "Error: Job ID not found in the response. Response: $response" >&2
              exit 1
            fi
            echo "Extracted jobId: $jobId"
            echo $jobId > /tmp/jobId
        command:
          - sh
          - -c
        image: curlimages/curl:7.85.0
      inputs:
        parameters:
          - name: jobName
          - name: templateName
          - name: status
          - name: priority
          - name: team
      name: create-job-record
      outputs:
        parameters:
          - name: jobId
            valueFrom:
              path: /tmp/jobId
    - http:
        body: |
          {
            "jobId": "{{inputs.parameters.jobId}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }
        headers:
          - name: Content-Type
            value: application/json
        method: POST
        successCondition: response.body contains "0001"
        timeoutSeconds: 10
        url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
      inputs:
        parameters:
          - name: jobId
          - name: status
          - name: priority
      name: update-status
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
  name: foundation-batchsystem-ing
  namespace: foundation
spec:
  ingressClassName: nginx
  rules:
    - host: batchsystem.foundation.dev
      http:
        paths:
          - backend:
              service:
                name: dev-foundation-jkopay-batchsystem-api-svc
                port:
                  number: 9090
            path: /
            pathType: ImplementationSpecific
  tls:
    - hosts:
        - "*.foundation.dev"
      secretName: foundation-dev-tls
---
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultAuth
metadata:
  name: dev-vault-auth-for-argo
  namespace: argo
spec:
  kubernetes:
    audiences:
      - vault
    role: argo
    serviceAccount: argo
  method: kubernetes
  mount: kubernetes
  vaultConnectionRef: dev-vault-connection
---
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultConnection
metadata:
  name: dev-vault-connection
  namespace: argo
spec:
  address: http://vault.resource.svc.cluster.local:8200
  skipTLSVerify: false
---
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultStaticSecret
metadata:
  name: dev-vault-secret
  namespace: argo
spec:
  destination:
    create: true
    name: kafka-user
  mount: secrets
  path: sit/foundation/app/jkopay-batchsystem-api
  refreshAfter: 1h
  type: kv-v2
  vaultAuthRef: dev-vault-auth-for-argo
