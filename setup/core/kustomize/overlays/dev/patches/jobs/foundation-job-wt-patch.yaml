apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: foundation-job-wt
  namespace: foundation
spec:
  serviceAccountName: foundation-batchsystem-sa
  arguments:
    parameters:
      - name: jobData
        value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogImxpbmVqb2IiIH0=
  templates:
    - name: main
      metadata:
        annotations:
          vault.hashicorp.com/agent-inject: "true"
          vault.hashicorp.com/role: "foundation"
          vault.hashicorp.com/agent-inject-secret-env-config: "secrets/data/sit/foundation/app/jkopay-batchsystem-api"
          vault.hashicorp.com/agent-inject-template-env-config: |
            {{- with secret "secrets/data/sit/foundation/app/jkopay-batchsystem-api" -}}
            #!/bin/sh
            {{- range $k, $v := .Data.data }}
            export {{ $k }}='{{ $v }}'
            {{- end }}
            exec "$@"
            {{- end }}
          vault.hashicorp.com/agent-inject-command-env-config: "cat /vault/secrets/env-config"
      metrics:
        prometheus:
          - name: job_execute_result_counter
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: name
                value: "{{workflow.parameters.templateName}}"
              - key: status
                value: "{{status}}"
            help: Count of execution by result status
            counter:
              value: "1"
          - name: job_execute_duration
            help: Duration of workflow execution
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: name
                value: "{{workflow.parameters.templateName}}"
              - key: status
                value: "{{status}}"
            gauge:
              realtime: false
              value: "{{duration}}"
      inputs:
        parameters:
          - name: jobData
      container:
        image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.job:0.0.0.53
        command: [
            "/bin/sh",
            "-c",
            '
            if [ -f "/vault/secrets/env-config" ]; then
            source "/vault/secrets/env-config";
            fi;
            dotnet JKOPay.BatchSystem.Job.dll {{workflow.uid}} {{workflow.name}} {{workflow.namespace}} {{inputs.parameters.jobData}}',
          ]
        env:
          - name: NETCORE_ENVIRONMENT
            value: development
          - name: IS_BASE64
            value: "true"
