apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  # 可以自行決定命名 但請符合格式規範 => {team}-{your-job-name}-sj
  name: foundation-test-job-sj
  # 請使用團隊名稱
  namespace: foundation
  annotations:
    workflows.argoproj.io/description: |
      Shared CronWorkflow Process
    workflows.argoproj.io/version: ">= 3.2.0"
spec:
  schedule: "*/2 */5 * * *"
  workflowSpec:
    metrics:
      prometheus:
        - name: cronworkflow_result_counter
          help: "Count of execution by result status"
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{workflow.status}}"
            - key: team
              value: "{{workflow.parameters.team}}"
          counter:
            value: "1"
        - name: wf_exec_duration_gauge
          help: "Duration gauge by workflow name and status"
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{status}}"
            - key: team
              value: "{{workflow.parameters.team}}"
          gauge:
            value: "{{workflow.duration}}"
            realtime: true
    workflowTemplateRef:
      # 請加上環境前綴
      name: batchsystem-main-cron-workflow-template
      # name: dev-foundation-test-job-sj
    arguments:
      parameters:
        # jobName 將使用 WorkflowTemplate 中的預設值（動態獲取 CronWorkflow 名稱）
        # 要使用的模板名稱, 請加上環境前綴
        - name: templateName
          value: dev-foundation-job-wt
        # JOB 所需要的參數 請使用 base64 字串
        - name: jobData
          value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInRlc3Rqb2IiIH0=
        - name: priority
          value: schedule
        # 動態獲取 namespace 作為 team 名稱
        - name: team
          value: "{{workflow.namespace}}"
