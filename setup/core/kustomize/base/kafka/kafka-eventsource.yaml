apiVersion: argoproj.io/v1alpha1
kind: EventSource
metadata:
  name: kafka-eventsource
  namespace: argo
spec:
  serviceAccountName: argo
  eventBusName: default
  kafka:
    priority:
      url: ka1.jkopay.app:9093,ka2.jkopay.app:9093,ka3.jkopay.app:9093
      topic: foundation_batchsystem_workflow_dev
      jsonBody: false
      partition: "0"
      connectionBackoff:
        duration: 10s
        steps: 5
        factor: 2
        jitter: 0.2
      sasl:
        mechanism: PLAIN
        passwordSecret:
          key: KAFKA_PASSWORD
          name: kafka-user
        userSecret:
          key: KAFKA_USERNAME
          name: kafka-user