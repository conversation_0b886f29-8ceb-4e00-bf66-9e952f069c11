apiVersion: argoproj.io/v1alpha1
kind: Sensor
metadata:
  name: kafka-sensor
  namespace: argo
spec:
  eventBusName: default
  template:
    serviceAccountName: argo
  dependencies:
    - name: batchsystem-dep-kafka
      eventSourceName: kafka
      eventName: priority
  triggers:
    - template:
        name: kafka-workflow-trigger
        argoWorkflow:
          operation: submit
          resource: workflows
          source:
            resource:
              apiVersion: argoproj.io/v1alpha1
              kind: Workflow
              metadata:
                generateName: kafka-workflow-
              spec:
                entryPoint: main
                arguments:
                  parameters:
                    - name: jobData
                    - name: jobId
                    - name: templateName
                    - name: priority
                      # workflowTemplateRef:
                      #   name: rd5-batchsystem-workflow-template
          parameters:
            - src:
                dependencyName: batchsystem-dep-kafka
                dataKey: body
              dest: spec.arguments.parameters.0.value
            - src:
                dependencyName: batchsystem-dep-kafka
                dataKey: headers.jobId
              dest: spec.arguments.parameters.1.value
            - src:
                dependencyName: batchsystem-dep-kafka
                dataKey: headers.templateName
              dest: spec.arguments.parameters.2.value
            - src:
                dependencyName: batchsystem-dep-kafka
                dataKey: headers.priority
              dest: spec.arguments.parameters.3.value
            - src:
                dependencyName: batchsystem-dep-kafka
                dataKey: headers.entry
              dest: spec.workflowTemplateRef.name
            - src:
                dependencyName: batchsystem-dep-kafka
                dataKey: headers.teamAccount
              dest: spec.ServiceAccountName
            - src:
                dependencyName: batchsystem-dep-kafka
                dataKey: headers.team
              dest: metadata.namespace
            - src:
                dependencyName: batchsystem-dep-kafka
                dataKey: headers.name
              dest: metadata.name