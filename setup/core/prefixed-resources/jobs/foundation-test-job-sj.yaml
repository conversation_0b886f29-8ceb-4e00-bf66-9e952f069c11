apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  # 可以自行決定命名 但請符合格式規範 => {team}-{your-job-name}-sj
  name: foundation-test-job-sj
  # 請使用團隊名稱
  namespace: foundation
  annotations:
    workflows.argoproj.io/description: |
      Shared CronWorkflow Process
    workflows.argoproj.io/version: ">= 3.2.0"
  labels:
    environment: dev
spec:
  schedule: "*/2 */5 * * *"
  workflowSpec:
    metrics:
      prometheus:
        - name: cronwf_execute_result_counter
          help: "Count of execution by result status"
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{workflow.status}}"
            - key: workflowId
              value: "{{workflow.uid}}"
            - key: scheduledTime
              value: "{{workflow.scheduledTime}}"
          counter:
            value: "1"
        - name: wf_exec_duration_gauge
          help: "Duration gauge by workflow name and status"
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{workflow.status}}"
            - key: team
              value: "{{workflow.parameters.team}}"
          gauge:
            value: "{{workflow.duration}}"
            realtime: true
    workflowTemplateRef:
      # 請加上環境前綴
      name: dev-foundation-batchsystem-main-cron-workflow-template
    arguments:
      parameters:
        # 明確傳遞環境參數
        - name: environment
          value: dev
        # 動態組合模板名稱：環境 + namespace + job-wt
        - name: templateName
          value: "dev-foundation-job-wt"
        # JOB 所需要的參數 請使用 base64 字串
        - name: jobData
          value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInRlc3Rqb2IiIH0=
        - name: priority
          value: schedule
        # 動態獲取 namespace 作為 team 名稱
        - name: team
          value: "{{workflow.namespace}}"
