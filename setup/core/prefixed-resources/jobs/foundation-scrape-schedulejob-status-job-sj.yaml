apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  # 可以自行決定命名 但請符合格式規範 => {team}-{your-job-name}-sj
  name: foundation-scrape-schedule-job-status-job-sj
  # 請使用團隊名稱
  namespace: foundation
  labels:
    app: foundation-scrape-schedule-job-status-job-sj
    developer: foundation
    service: schedule-job
    environment: dev
  annotations:
    workflows.argoproj.io/description: |
      Schedule job to periodically call scrape-schedule-job-status-job-wt
    workflows.argoproj.io/version: ">= 3.2.0"
spec:
  # 每五分鐘執行一次
  schedule: "*/5 * * * *"
  workflowSpec:
    metrics:
      prometheus:
        - name: cronwf_execute_result_counter
          help: Count of execution by result status
          labels:
            - key: name
              value: '{{workflow.labels.workflows.argoproj.io/cron-workflow}}'
            - key: status
              value: '{{status}}'
            - key: workflowId
              value: '{{workflow.uid}}'
            - key: scheduledTime
              value: '{{workflow.scheduledTime}}'
          counter:
            value: '1'
        - name: cronwf_execute_duration
          help: Duration of cron workflow execution
          labels:
            - key: name
              value: '{{workflow.labels.workflows.argoproj.io/cron-workflow}}'
            - key: status
              value: '{{status}}'
          gauge:
            realtime: false
            value: '{{workflow.duration}}'
    workflowTemplateRef:
      # 請加上環境前綴，例如：dev-foundation-batchsystem-main-cron-workflow-template
      name: dev-foundation-batchsystem-main-cron-workflow-template
    arguments:
      parameters:
        # 使請用和 metadata.name 一致
        - name: jobName
          value: foundation-scrape-schedule-job-status-sj
        # 要使用的模板名稱, 請加上環境前綴
        - name: templateName
          value: dev-foundation-scrape-schedule-job-status-job-wt
        # JOB 所需要的參數 請使用 base64 字串
        - name: jobData
          value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInNjcmFwZS1zY2hlZHVsZS1qb2Itc3RhdHVzLWpvYiIgfQ==
        - name: priority
          value: schedule
        # 請和上方的 metadata.namespace 一致
        - name: team
          value: foundation