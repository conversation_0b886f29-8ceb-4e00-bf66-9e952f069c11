apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: rd1-test-wt
  namespace: rd1
spec:
  serviceAccountName: rd1-batchsystem-sa
  templates:
    - name: main
      metadata:
        annotations:
          vault.hashicorp.com/agent-inject: 'true'
          vault.hashicorp.com/role: 'internal-app'
          vault.hashicorp.com/agent-inject-template-env-config: |
            {{- with secret "internal/data/database/config" -}}
            #!/bin/sh
            {{- range $k, $v := .Data.data }}
            export {{ $k }}='{{ $v }}'
            {{- end }}
            exec "$@"
            {{- end }}
          vault.hashicorp.com/agent-inject-command-env-config: "cat /vault/secrets/env-config"
      container:
        image: byrnedo/alpine-curl
        command: ["/bin/sh", "-c", "source /vault/secrets/env-config && env && while true; do sleep 3600; done"]
