apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    environment: dev
  name: jkopay-batchsystem-operator
  namespace: foundation
spec:
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: foundation-jkopay-batchsystem-operator
      environment: dev
  template:
    metadata:
      labels:
        app: foundation-jkopay-batchsystem-operator
        environment: dev
    spec:
      containers:
      - env:
        - name: NETCORE_ENVIRONMENT
          value: development
        - name: NAMESPACE
          value: foundation
        image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.operator:0.0.0.64
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        name: jkopay-batchsystem-operator
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
      serviceAccountName: foundation-batchsystem-sa
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    environment: dev
  name: jkopay-batchsystem-operator
  namespace: rd1
spec:
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: rd1-jkopay-batchsystem-operator
      environment: dev
  template:
    metadata:
      labels:
        app: rd1-jkopay-batchsystem-operator
        environment: dev
    spec:
      containers:
      - env:
        - name: NETCORE_ENVIRONMENT
          value: development
        - name: NAMESPACE
          value: rd1
        image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.operator:0.0.0.64
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        name: jkopay-batchsystem-operator
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
      serviceAccountName: rd1-batchsystem-sa
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    batchsystem.owner: batchsystem
    environment: dev
  name: batchsystem-main-cron-workflow-template
  namespace: foundation
spec:
  arguments:
    parameters:
    - name: jobName
      value: '{{workflow.labels.workflows.argoproj.io/cron-workflow}}'
    - name: templateName
      value: default
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: team
      value: '{{workflow.namespace}}'
    - name: environment
      value: dev-foundation-jkopay-batchsystem-api-svc.foundation:9090
    - name: apiUrl
      value: '{{workflow.labels.environment}}-foundation-jkopay-batchsystem-api-svc.svc.cluster.local:9090'
  entrypoint: main
  serviceAccountName: batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobName
            value: '{{workflow.parameters.jobName}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
          - name: templateName
            value: '{{workflow.parameters.templateName}}'
          - name: team
            value: '{{workflow.parameters.team}}'
        name: create-job-record
        template: create-job-record
      - arguments:
          parameters:
          - name: jobId
            value: '{{tasks.create-job-record.outputs.parameters.jobId}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - create-job-record
        name: started-status
        template: update-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - started-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: main
      - arguments:
          parameters:
          - name: jobId
            value: '{{tasks.create-job-record.outputs.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        - key: result
          value: '{{status}}'
        - key: crateTimestamp
          value: '{{workflow.creationTimestamp}}'
        - key: finishTimestamp
          value: '{{workflow.status.finishedAt}}'
        name: wf_execute_result_counter
      - gauge:
          realtime: true
          value: '{{workflow.duration}}'
        help: Time of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        name: wf_execution_time
    name: main
  - container:
      args:
      - |
        response=$(curl --silent --request POST --url http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule \
        --header 'Accept: */*' \
        --header 'Content-Type: application/json' \
        --header 'User-Agent: httpyac' \
        --data '{
          "jobName": "{{inputs.parameters.jobName}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "workflowTemplateName": "{{inputs.parameters.templateName}}",
          "status": "{{inputs.parameters.status}}",
          "priority": "{{inputs.parameters.priority}}",
          "team": "{{inputs.parameters.team}}"
        }')
        echo "Full Response: $response"
        jobId=$(echo $response | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')
        if [ -z "$jobId" ]; then
          echo "Error: Job ID not found in the response. Response: $response" >&2
          exit 1
        fi
        echo "Extracted jobId: $jobId"
        echo $jobId > /tmp/jobId
      command:
      - sh
      - -c
      image: curlimages/curl:7.85.0
    inputs:
      parameters:
      - name: jobName
      - name: templateName
      - name: status
      - name: priority
      - name: team
    name: create-job-record
    outputs:
      parameters:
      - name: jobId
        valueFrom:
          path: /tmp/jobId
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    batchsystem.owner: batchsystem
    environment: dev
  name: batchsystem-workflow-template
  namespace: foundation
spec:
  arguments:
    parameters:
    - name: jobId
      value: default
    - name: templateName
      value: default
    - name: entryPoint
      value: main
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: apiUrl
      value: dev-foundation-jkopay-batchsystem-api-ext-svc.foundation:9090
  entrypoint: main
  serviceAccountName: batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
        name: started-status
        template: update-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - started-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: '{{workflow.parameters.entryPoint}}'
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        - key: result
          value: '{{status}}'
        - key: crateTimestamp
          value: '{{workflow.creationTimestamp}}'
        - key: finishTimestamp
          value: '{{workflow.status.finishedAt}}'
        name: wf_execute_result_counter
      - gauge:
          realtime: true
          value: '{{workflow.duration}}'
        help: Time of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        name: wf_execution_time
    name: main
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    batchsystem.owner: batchsystem
    environment: dev
  name: batchsystem-main-cron-workflow-template
  namespace: rd1
spec:
  arguments:
    parameters:
    - name: jobName
      value: '{{workflow.labels.workflows.argoproj.io/cron-workflow}}'
    - name: templateName
      value: default
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: team
      value: '{{workflow.namespace}}'
    - name: environment
      value: dev-foundation-jkopay-batchsystem-api-svc.foundation:9090
    - name: apiUrl
      value: '{{workflow.labels.environment}}-foundation-jkopay-batchsystem-api-svc.svc.cluster.local:9090'
  entrypoint: main
  serviceAccountName: batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobName
            value: '{{workflow.parameters.jobName}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
          - name: templateName
            value: '{{workflow.parameters.templateName}}'
          - name: team
            value: '{{workflow.parameters.team}}'
        name: create-job-record
        template: create-job-record
      - arguments:
          parameters:
          - name: jobId
            value: '{{tasks.create-job-record.outputs.parameters.jobId}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - create-job-record
        name: started-status
        template: update-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - started-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: main
      - arguments:
          parameters:
          - name: jobId
            value: '{{tasks.create-job-record.outputs.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        - key: result
          value: '{{status}}'
        - key: crateTimestamp
          value: '{{workflow.creationTimestamp}}'
        - key: finishTimestamp
          value: '{{workflow.status.finishedAt}}'
        name: wf_execute_result_counter
      - gauge:
          realtime: true
          value: '{{workflow.duration}}'
        help: Time of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        name: wf_execution_time
    name: main
  - container:
      args:
      - |
        response=$(curl --silent --request POST --url http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule \
        --header 'Accept: */*' \
        --header 'Content-Type: application/json' \
        --header 'User-Agent: httpyac' \
        --data '{
          "jobName": "{{inputs.parameters.jobName}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "workflowTemplateName": "{{inputs.parameters.templateName}}",
          "status": "{{inputs.parameters.status}}",
          "priority": "{{inputs.parameters.priority}}",
          "team": "{{inputs.parameters.team}}"
        }')
        echo "Full Response: $response"
        jobId=$(echo $response | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')
        if [ -z "$jobId" ]; then
          echo "Error: Job ID not found in the response. Response: $response" >&2
          exit 1
        fi
        echo "Extracted jobId: $jobId"
        echo $jobId > /tmp/jobId
      command:
      - sh
      - -c
      image: curlimages/curl:7.85.0
    inputs:
      parameters:
      - name: jobName
      - name: templateName
      - name: status
      - name: priority
      - name: team
    name: create-job-record
    outputs:
      parameters:
      - name: jobId
        valueFrom:
          path: /tmp/jobId
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    batchsystem.owner: batchsystem
    environment: dev
  name: batchsystem-workflow-template
  namespace: rd1
spec:
  arguments:
    parameters:
    - name: jobId
      value: default
    - name: templateName
      value: default
    - name: entryPoint
      value: main
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: apiUrl
      value: dev-foundation-jkopay-batchsystem-api-ext-svc.foundation:9090
  entrypoint: main
  serviceAccountName: rd1-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
        name: started-status
        template: update-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - started-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: '{{workflow.parameters.entryPoint}}'
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        - key: result
          value: '{{status}}'
        - key: crateTimestamp
          value: '{{workflow.creationTimestamp}}'
        - key: finishTimestamp
          value: '{{workflow.status.finishedAt}}'
        name: wf_execute_result_counter
      - gauge:
          realtime: true
          value: '{{workflow.duration}}'
        help: Time of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        name: wf_execution_time
    name: main
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
