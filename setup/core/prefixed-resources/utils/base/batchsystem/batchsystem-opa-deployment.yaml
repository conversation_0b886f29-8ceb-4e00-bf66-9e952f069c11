apiVersion: apps/v1
kind: Deployment
metadata:
  name: jkopay-batchsystem-operator
  namespace: foundation # 可以根据实际情况修改命名空间
spec:
  replicas: 1 # 您可以根据需要调整副本数量
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: foundation-jkopay-batchsystem-operator
  template:
    metadata:
      labels:
        app: foundation-jkopay-batchsystem-operator
    spec:
      serviceAccountName: foundation-batchsystem-sa
      containers:
        - name: jkopay-batchsystem-operator
          image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.operator:0.0.0.64
          env:
            - name: NETCORE_ENVIRONMENT
              value: "development"
            - name: NAMESPACE
              value: "foundation"
          readinessProbe:
            httpGet:
              path: / # 根据您的API健康检查路径
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: / # 根据您的API健康检查路径
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 20
