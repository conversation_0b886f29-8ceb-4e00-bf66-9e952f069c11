apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: batchsystem-workflow-template
  labels:
    batchsystem.owner: batchsystem
spec:
  entrypoint: main
  serviceAccountName: batchsystem-sa
  arguments:
    parameters:
      - name: jobId
        value: default
      - name: templateName
        value: default
      - name: entryPoint
        value: main
      - name: jobData
        value: default
      - name: priority
        value: default
      - name: apiUrl
        value: dev-foundation-jkopay-batchsystem-api-ext-svc.foundation:9090
  templates:
    - name: main
      metrics:
        prometheus:
          - name: wf_execute_result_counter
            help: "Count of execution by result status"
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
              - key: result
                value: "{{status}}"
              - key: crateTimestamp
                value: "{{workflow.creationTimestamp}}"
              - key: finishTimestamp
                value: "{{workflow.status.finishedAt}}"
            counter:
              value: "1"
          - name: wf_execution_time
            help: "Time of execution by result status"
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
            gauge:
              realtime: true
              value: "{{workflow.duration}}"
      dag:
        tasks:
          - name: started-status
            template: update-status
            arguments:
              parameters:
                - name: jobId
                  value: "{{workflow.parameters.jobId}}"
                - name: status
                  value: "WorkflowStarted"
                - name: priority
                  value: "{{workflow.parameters.priority}}"
          - name: team-job
            dependencies: [started-status]
            templateRef:
              name: "{{workflow.parameters.templateName}}"
              template: "{{workflow.parameters.entryPoint}}"
            arguments:
              parameters:
                - name: jobData
                  value: "{{workflow.parameters.jobData}}"
          - name: complete-workflow
            dependencies: [team-job]
            template: update-status
            arguments:
              parameters:
                - name: jobId
                  value: "{{workflow.parameters.jobId}}"
                - name: status
                  value: "WorkflowComplete"
                - name: priority
                  value: "{{workflow.parameters.priority}}"
    - name: update-status
      inputs:
        parameters:
          - name: jobId
          - name: status
          - name: priority
      http:
        url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
        method: POST
        timeoutSeconds: 10
        successCondition: 'response.body contains "0001"'
        headers:
          - name: Content-Type
            value: application/json
        body: |
          {
            "jobId": "{{inputs.parameters.jobId}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }
