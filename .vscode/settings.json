{
  // SonarQube for IDE 分析設定
  "sonarlint.analyzerProperties": {
    // 設定分析的時間點
    "sonar.analysis.mode": "issues"
  },
  
  // 控制 SonarLint 何時分析檔案
  "sonarlint.disableTelemetry": true,
  
  // 設定自動分析的觸發條件
  "sonarlint.analysisExclusionPatterns": [
    "**/*.min.js",
    "**/node_modules/**",
    "**/bin/**",
    "**/obj/**",
    "**/.git/**"
  ],
  
  // 分析檔案的時間點控制
  "sonarlint.output.showAnalyzerLogs": false,
  "sonarlint.output.showVerboseLogs": false,
  
  // C# 專案特定設定
  "sonarlint.pathToCompileCommands": "",
  
  // 設定檔案儲存時是否自動分析（預設為 true）
  "files.autoSave": "onFocusChange",
  
  // 控制何時顯示問題
  "problems.autoReveal": true,
  
  // 設定編輯器的自動格式化
  "editor.formatOnSave": true,
  "editor.formatOnType": false,
  "editor.formatOnPaste": false,
  
  // C# 檔案特定設定
  "[csharp]": {
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    }
  },
  
  // 設定儲存時的動作（可觸發 SonarLint 分析）
  "editor.codeActionsOnSave": {
    "source.fixAll": "never"
  }
}