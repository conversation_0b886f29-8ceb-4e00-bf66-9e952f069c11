# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true

# C# files
[*.cs]
indent_style = space
indent_size = 4
tab_width = 4

# Code style
dotnet_style_qualification_for_field = false
dotnet_style_qualification_for_property = false
dotnet_style_qualification_for_method = false
dotnet_style_qualification_for_event = false
dotnet_style_predefined_type_for_locals_parameters_members = true
dotnet_style_predefined_type_for_member_access = true
dotnet_style_require_accessibility_modifiers = always
csharp_style_var_for_built_in_types = true
csharp_style_var_when_type_is_apparent = true
csharp_style_var_elsewhere = false
csharp_style_expression_bodied_methods = when_on_single_line
csharp_style_expression_bodied_constructors = when_on_single_line
csharp_style_expression_bodied_operators = when_on_single_line
csharp_style_expression_bodied_properties = true
csharp_style_expression_bodied_indexers = true
csharp_style_expression_bodied_accessors = true
csharp_style_expression_bodied_lambdas = true
csharp_style_expression_bodied_local_functions = true

# usings
csharp_using_directive_placement = outside_namespace