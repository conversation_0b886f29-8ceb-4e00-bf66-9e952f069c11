providerNamespace: JKOPay.BatchSystem.Api
name: BatchSystem.Metrics
metrics:
  - name: RequestErrorCounter
    metricsName: request_error_counter
    type: Counter
    dataType: int
    unit: request
    description: "Counter for request error"
    labels:
      - name: path
        value: value1
      - name: method
        value: value2
      - name: resultCode
        value: value3
  - name: ScheduledJobsList
    metricsName: schedule_job_list
    type: Gauge
    dataType: int
    unit: job
    description: "Status of schedule job"
  - name: RequestDurationHistogram
    metricsName: request_duration_histogram
    type: Histogram
    unit: seconds
    description: "Histogram of HTTP request durations"
    buckets: [0.1, 0.5, 1.0, 2.5, 5.0, 10.0]
    labels:
      - name: tag1
        value: value1
      - name: tag2
        value: value2
  - name: RequestLatencySummary
    metricsName: request_latency_summary
    type: Summary
    unit: seconds
    description: "Summary of request latencies"
    quantiles: [0.5, 0.95, 0.99]
    allowKeys:
      - tag1
      - tag2
  - name: RequestLatencyGauge
    metricsName: request_latency_gauge
    type: Gauge
    unit: seconds
    description: "Gauge for request latencies"
    labels:
      - name: tag1
        value: value1
      - name: tag2
        value: value2
