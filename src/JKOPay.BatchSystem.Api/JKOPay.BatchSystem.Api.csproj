<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <InvariantGlobalization>true</InvariantGlobalization>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <ReadyToRun>false</ReadyToRun>
    </PropertyGroup>
    <PropertyGroup>
      <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
      <CompilerGeneratedFilesOutputPath>$(BaseIntermediateOutputPath)Generated</CompilerGeneratedFilesOutputPath>

      <!-- 確保使用中央套件管理 -->
      <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
      <!-- 指定要使用的 NuGet 配置檔案位置 -->
      <RestoreConfigFile>$(MSBuildThisFileDirectory)..\..\nuget.config</RestoreConfigFile>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" />
        <PackageReference Include="Confluent.Kafka" />
        <PackageReference Include="JKOPay.Platform.HttpClientExtension" />
        <PackageReference Include="JKOPay.Platform.LoggingExtension" />
        <PackageReference Include="JKOPay.Platform.OpenTelemetry" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" >
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" />
        <PackageReference Include="Microsoft.Net.Compilers.Toolset" >
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="NetEscapades.Configuration.Yaml" />
        <PackageReference Include="Newtonsoft.Json" />
        <PackageReference Include="OpenTelemetry.Exporter.Console" />
        <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" />
        <PackageReference Include="OpenTelemetry.Instrumentation.EntityFrameworkCore" />
        <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" />
        <PackageReference Include="Serilog" />
        <PackageReference Include="Serilog.Exceptions" />
        <PackageReference Include="Serilog.Sinks.Async" />
        <PackageReference Include="Serilog.Sinks.Console" />
        <PackageReference Include="Serilog.Sinks.OpenTelemetry" />
        <PackageReference Include="Swashbuckle.AspNetCore" />
        <PackageReference Include="Swashbuckle.AspNetCore.Filters" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
      <Content Remove="Models\Metrics\MetricBaseModel\**" />
    </ItemGroup>

    <ItemGroup>
      <None Update="appsettings.yaml">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.development.yaml">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        <DependentUpon>appsettings.yaml</DependentUpon>
      </None>
      <None Update="appsettings.local.yaml">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        <DependentUpon>appsettings.yaml</DependentUpon>
      </None>
      <None Update="appsettings.sit.yaml">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        <DependentUpon>appsettings.yaml</DependentUpon>
      </None>
      <None Remove="Models\Metrics\MetricBaseModel\**" />
      <AdditionalFiles Include="metrics.yaml" />
      <None Update="appsettings.prod.yaml">
        <DependentUpon>appsettings.yaml</DependentUpon>
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
<!--      <AdditionalFiles Include="metrics.json" />-->
    </ItemGroup>

    <ItemGroup>
<!--      <ProjectReference Include="..\..\..\jkopay.platform\src\JKOPay.Platform.OpenTelemetry.SourceGenerator\JKOPay.Platform.OpenTelemetry.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />-->
      <ProjectReference Include="..\JKOPay.BatchSystem.Core\JKOPay.BatchSystem.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Models\Metrics\MetricBaseModel\**" />
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Remove="Models\Metrics\MetricBaseModel\**" />
    </ItemGroup>
</Project>
