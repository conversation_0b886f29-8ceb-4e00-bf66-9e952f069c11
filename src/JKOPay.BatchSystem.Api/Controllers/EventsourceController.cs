using Asp.Versioning;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Models.Argo.Eventsource;
using JKOPay.BatchSystem.Core.Services;
using Microsoft.AspNetCore.Mvc;

namespace JKOPay.BatchSystem.Api.Controllers;

/// <summary>
/// All about eventsource action
/// </summary>
/// <param name="argoWorkflowService"></param>
/// <param name="logger"></param>
[ApiController]
[ApiVersion(1)]
[Route("api/v{v:apiVersion}/event-sources")]
public class EventsourceController(IArgoWorkflowService argoWorkflowService, ILogger<EventsourceController> logger)
{
    /// <summary>
    /// 取得目前的 Eventsource 清單
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <returns></returns>
    [HttpGet("{namespace}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<List<Eventsource>>>(200)]
    public async Task<ResponseModel<List<Eventsource>>> GetEventsource(string @namespace)
    {
        logger.LogInformation(@namespace);
        var response = await argoWorkflowService.GetEventsource(@namespace);
        return response;
    }

    /// <summary>
    /// 刪除 Eventsource
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <param name="eventsourceName">Eventsource 名稱</param>
    /// <returns></returns>
    [HttpDelete("{namespace}/{eventsourceName}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<object>>(200)]
    public async Task<ResponseModel<object>> DeleteWorkflowTemplate(string @namespace, string eventsourceName)
    {
        logger.LogInformation("namespace: {Namespace}, template name: {TemplateName}", @namespace, eventsourceName);
        var response = await argoWorkflowService.DeleteEventsource(@namespace, eventsourceName);
        return response;
    }
}