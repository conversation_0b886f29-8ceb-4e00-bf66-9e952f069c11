// <PERSON><PERSON><PERSON>per disable once ConvertToPrimaryConstructor
// <PERSON><PERSON><PERSON><PERSON> disable once ConvertToPrimaryConstructor
using Asp.Versioning;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Api.Controllers;

/// <summary>
/// All about job action
/// </summary>
[ApiController]
[ApiVersion(1)]
[Route("api/v{v:apiVersion}/job")]
public class JobController
{
    private readonly IJobService _jobService;

    private readonly ILogger<JobController> _logger;

    /// <summary>
    /// Job Controller
    /// </summary>
    /// <param name="jobService"></param>
    /// <param name="logger"></param>
    public JobController(IJobService jobService, ILogger<JobController> logger)
    {
        _jobService = jobService;
        _logger = logger;
    }

    /// <summary>
    /// 取得 Job 資訊
    /// </summary>
    /// <param name="jobId">Job Id</param>
    /// <param name="workflowId">Workflow Id</param>
    /// <returns></returns>
    [HttpGet()]
    public async Task<ResponseModel<object>> GetJob([FromQuery] string? jobId, [FromQuery] string workflowId)
    {
        if (jobId != default)
        {
            var response = await _jobService.GetJobAsyncUseJobId(jobId);
            return response;
        }
        else
        {
            var response = await _jobService.GetJobAsyncUseWorkflowId(workflowId);
            return response;
        }
    }

    /// <summary>
    /// 取得 Job 資訊
    /// </summary>
    /// <param name="jobId">Job Id</param>
    /// <returns></returns>
    [HttpGet("info/{jobId}")]
    public async Task<ResponseModel<object>> GetJobInfo(string jobId)
    {
        var response = await _jobService.GetJobInfoAsync(jobId);
        return response;
    }

    /// <summary>
    /// 取得 Job 列表
    /// </summary>
    /// <param name="team">團隊名稱</param>
    /// <param name="page">Page</param>
    /// <param name="count">單頁顯示筆數</param>
    /// <returns></returns>
    [HttpGet("list/{team}/{page}")]
    public async Task<object> GetList(string team, int page, [FromQuery] int count)
    {
        var response = await _jobService.GetJobsAsync(team, page, count);
        return response;
    }

    /// <summary>
    /// 取得 Job 列表 (管理後台使用)
    /// </summary>
    /// <param name="team">團隊名稱</param>
    /// <param name="jobId">JobId</param>
    /// <param name="currentPage">Page</param>
    /// <param name="pageSize">單頁顯示筆數</param>
    /// <returns></returns>
    [HttpGet("list/{team}/adminpanel")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<object> GetList(string team, [FromQuery] string? jobId, [FromQuery] int currentPage, [FromQuery] int pageSize)
    {
        if (jobId != default)
        {
            var response = await _jobService.GetJobAsyncUseJobId(jobId);
            return new ResponseModel<object>
            {
                Result = "0001",
                ResultObject = new[] { response.ResultObject },
                Message = "Success"
            };
        }
        else
        {
            var response = await _jobService.GetJobsAsync(team, currentPage, pageSize);
            return response;
        }
    }

    /// <summary>
    /// 建立 Job
    /// </summary>
    /// <param name="request">Job 相關資訊</param>
    /// <remarks>
    /// Sample request:
    ///
    ///     POST /create
    ///     {
    ///         "Priority": "[Immediate, Priority, Normal]",
    ///         "JobName": "{Your Job Name}",
    ///         "WorkflowTemplateName": "{Your Workflow Template Name}",
    ///         "TemplateName": "main",
    ///         "KafkaHeader": { },
    ///         "Message": { "jobData" : {Your a parameter object} },
    ///         "ServiceNamespace": "{Your team name}"
    ///     }
    ///
    /// </remarks>
    /// <returns></returns>
    [HttpPost("execute")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<Dictionary<string, string>>>(200)]
    public async Task<ResponseModel<Dictionary<string, string>>> CreateJob(CreateJob request)
    {
        var response = await _jobService.CreateJobAsync(request);
        return response;
    }

    /// <summary>
    /// Get Job Info Template
    /// </summary>
    /// <param name="namespace"></param>
    /// <param name="templateName"></param>
    /// <param name="jobId"></param>
    /// <param name="source"></param>
    /// <returns></returns>
    [HttpGet("info/template/{namespace}/{templateName}/{jobId}/{source}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<Dictionary<string, string>>>(200)]
    public async Task<ResponseModel<object>> GetInfoTemplate(string @namespace, string templateName, string jobId, string source)
    {
        var response = await _jobService.GetJobInfoTemplateAsync(@namespace, jobId, templateName, source);
        return response;
    }

    /// <summary>
    /// 重新執行 Job
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <param name="jobId">Job id</param>
    /// <returns></returns>
    [HttpPut("resubmit/{namespace}/{jobId}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<object>>(200)]
    public async Task<ResponseModel<object>> ResubmitJob(string @namespace, string jobId)
    {
        var response = await _jobService.UpdateJobAsync(@namespace, jobId);
        return response;
    }

    /// <summary>
    /// 建立 Schedule job 的 job record
    /// </summary>
    /// <param name="request">Schedule workflow 相關資訊</param>
    /// <remarks>建立 schedule job 的 job record</remarks>
    /// <returns></returns>
    [HttpPost("create/schedule")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<object>>(200)]
    public async Task<ResponseModel<object>> CreateJob(CreateScheduleJob request)
    {
        var response = await _jobService.CreateJobAsync(request);
        return response;
    }

    /// <summary>
    /// 取消 Job
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <param name="jobId">建立 Job 時回傳的 Job ID</param>
    /// <returns></returns>
    [HttpPost("cancel/{namespace}/{jobId}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<object>>(200)]
    public async Task<ResponseModel<object>> CancelJob(string @namespace, string jobId)
    {
        var response = await _jobService.CanceledJobAsync(@namespace, jobId);
        return response;
    }

    /// <summary>
    /// 更新 Job Record 中的狀態
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("update/status")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<Dictionary<string, string>>> UpdateJobStatus(UpdateJob request)
    {
        _logger.LogDebug("Update data: {Data}", JsonConvert.SerializeObject(request));
        var response = await _jobService.UpdateJobStatusAsync(request);
        return response;
    }

    /// <summary>
    /// 更新 Job Record 中的需要處理資料筆數
    /// </summary>
    /// <param name="totalCount"></param>
    /// <param name="workflowId"></param>
    /// <returns></returns>
    [HttpPost("update/totalRecords")]
    // [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<object>> UpdateTotalRecords(int totalCount, string workflowId)
    {
        var response = await _jobService.UpdateTotalRecordsAsync(totalCount, workflowId);
        return response;
    }

    /// <summary>
    /// 更新 Job Record 中的以處理資料筆數
    /// </summary>
    /// <param name="processedRecords"></param>
    /// <param name="workflowId"></param>
    /// <returns></returns>
    [HttpPost("update/processedRecords")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<object>> UpdateProcessRecords(int processedRecords, string workflowId)
    {
        var response = await _jobService.UpdateProcessRecordsAsync(processedRecords, workflowId);
        return response;
    }
}
