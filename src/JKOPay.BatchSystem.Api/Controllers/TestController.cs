using System.Diagnostics;
using Asp.Versioning;
using JKOPay.BatchSystem.Api.Models;
using JKOPay.BatchSystem.Core.Models;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace JKOPay.BatchSystem.Api.Controllers;

/// <summary>
/// For Api test.
/// </summary>
[ApiController]
[ApiVersion(1)]
[Route("api/v{v:apiVersion}/test")]
public class TestController
{
    private readonly ILogger<TestController> _logger;

    private readonly BatchDbContext _dbContext;

    /// <summary>
    /// TestController
    /// </summary>
    /// <param name="logger"></param>
    public TestController(BatchDbContext dbContext, ILogger<TestController> logger)
    {
        _logger = logger;
        _dbContext = dbContext;
    }

    /// <summary>
    /// Test non json result
    /// </summary>
    /// <returns></returns>
    [HttpGet("NonJsonResult")]
    public async Task<string> NonJsonResult()
    {
        return await Task.FromResult<string>("username: jamis");
    }

    [HttpGet("testlog")]
    public async Task<object> TestLog()
    {
        var job = await _dbContext.JobRecords.Take(2).ToListAsync();
        return job;
    }

    /// <summary>
    /// Test request log
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("RequestLog")]
    public async Task<object> RequestLog(TestRequest request)
    {
        _logger.LogInformation("In Test Controller RequestLog, {Name}", request.Name);
        return await Task.FromResult<object>(new Dictionary<string, object>
        {
            { "Name", request.Name },
            { "Age", request.Age },
            { "Token", request.Token }
        });
    }

    /// <summary>
    /// Test error result
    /// </summary>
    /// <returns></returns>
    [HttpGet("ErrorResult")]
    public Task<ResponseModel<object>> ErrorResponse()
    {
        return Task.FromResult(new ResponseModel<object>()
        {
            ResultCode = ResultCode.ScheduleJobEx0002,
            ResultObject = new Dictionary<string, object?>
            {
                { "Name", "Jamis" },
                { "Age", 18 }
            }
        });
    }

    /// <summary>
    /// Retrieves the trace ID and span ID from the current activity.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains an object with trace ID and span ID details.</returns>
    [HttpGet("GetTraceId")]
    public Task<object> GetTraceId()
    {
        var activity = Activity.Current;
        var tmp = new
        {
            TraceId = activity?.TraceId.ToString(),
            SpanId = activity?.SpanId.ToString()
        };

        return Task.FromResult<object>(tmp);
    }
}
