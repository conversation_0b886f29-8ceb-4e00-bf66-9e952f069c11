using Asp.Versioning;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Models.Argo.Sensor;
using JKOPay.BatchSystem.Core.Services;
using Microsoft.AspNetCore.Mvc;

namespace JKOPay.BatchSystem.Api.Controllers;

/// <summary>
/// All about sensor action
/// </summary>
/// <param name="argoWorkflowService">ArgoWorkflow Service</param>
/// <param name="logger">Logger</param>
[ApiController]
[ApiVersion(1)]
[Route("api/v{v:apiVersion}/sensors")]
public class SensorController(IArgoWorkflowService argoWorkflowService, ILogger<SensorController> logger)
{
    /// <summary>
    /// 取得目前的 Sensor 清單
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <returns></returns>
    [HttpGet("{namespace}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<List<Sensor>>>(200)]
    public async Task<ResponseModel<List<Sensor>>> GetSensors(string @namespace)
    {
        logger.LogInformation(@namespace);
        var response = await argoWorkflowService.GetSensors(@namespace);
        return response;
    }

    /// <summary>
    /// 刪除 Sensor
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <param name="sensorName">Sensor 名稱</param>
    /// <returns></returns>
    [HttpDelete("{namespace}/{sensorName}")]
    public async Task<ResponseModel<object>> DeleteWorkflowTemplate(string @namespace, string sensorName)
    {
        logger.LogInformation("namespace: {Namespace}, template name: {TemplateName}", @namespace, sensorName);
        var response = await argoWorkflowService.DeleteSensor(@namespace, sensorName);
        return response;
    }
}