using Asp.Versioning;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Models.Argo.Workflow;
using JKOPay.BatchSystem.Core.Services;
using Microsoft.AspNetCore.Mvc;

namespace JKOPay.BatchSystem.Api.Controllers;

/// <summary>
/// All about workflow action
/// </summary>
/// <param name="argoWorkflowService">Argo Workflow Service</param>
/// <param name="workflowService">Workflow service</param>
/// <param name="logger">Logger</param>
[ApiController]
[ApiVersion(1)]
[Route("api/v{v:apiVersion}/workflows")]
public class WorkflowControllers(IArgoWorkflowService argoWorkflowService,IWorkflowService workflowService, ILogger<WorkflowTemplateController> logger)
{
    /// <summary>
    /// 取得目前的 Workflow 清單
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <param name="labelSelector">Label filter</param>
    /// <returns></returns>
    [HttpGet("argo/{namespace}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<List<Workflow>>>(200)]
    public async Task<ResponseModel<List<Workflow>>> GetWorkflowsOnArgo(string @namespace, string labelSelector="")
    {
        logger.LogInformation(@namespace);
        var response = await argoWorkflowService.GetWorkflows(@namespace, labelSelector);
        return response;
    }

    /// <summary>
    /// 取得 Argo Workflow Server 上的 Workflow Log
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <param name="jobName">建立 Job 時回傳的 Job Name</param>
    /// <remarks>Workflow Log 有機會為空</remarks>
    /// <returns></returns>
    [HttpGet("argo/{namespace}/log/{jobName}")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<List<WorkflowLogResponse>>> GetWorkflowLogOnArgo(string @namespace, string jobName)
    {
        logger.LogInformation(@namespace);
        var response = await argoWorkflowService.GetWorkflowLog(@namespace, jobName);
        return response;
    }

    /// <summary>
    /// Get Workflow Detail
    /// </summary>
    /// <param name="workflowName">Workflow Name</param>
    /// <returns></returns>
    [HttpGet("detail/{workflowName}")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<Platform.BatchSystem.Models.Database.Workflow>> GetWorkflowDetail(string workflowName)
    {
        var response = await workflowService.GetWorkflowAsync(workflowName);
        return response;
    }

    /// <summary>
    /// Update Workflow Status
    /// </summary>
    /// <param name="wf"></param>
    /// <returns></returns>
    [HttpPost("update/status")]
    public async Task<ResponseModel<object>> UpdateWorkflowStatus(UpdateWorkflow wf)
    {
        var response = await workflowService.UpdateWorkflowStatusAsync(wf);
        return response;
    }
}