using Asp.Versioning;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Services;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.AspNetCore.Mvc;

namespace JKOPay.BatchSystem.Api.Controllers;

/// <summary>
/// All about job action
/// </summary>
[ApiController]
[ApiVersion(1)]
[Route("api/v{v:apiVersion}/schedule")]

public class ScheduleController
{
    private readonly IScheduleJobService _scheduleJobService;

    private readonly ILogger<ScheduleController> _logger;

    /// <summary>
    ///
    /// </summary>
    /// <param name="scheduleJobService"></param>
    /// <param name="logger"></param>
    public ScheduleController(IScheduleJobService scheduleJobService, ILogger<ScheduleController> logger)
    {
        _scheduleJobService = scheduleJobService;
        _logger = logger;
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="team"></param>
    /// <param name="jobId"></param>
    /// <param name="currentPage"></param>
    /// <param name="pageSize"></param>
    /// <returns></returns>
    [HttpGet("list/{team}/adminpanel")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<object> GetList(string team, [FromQuery]Guid jobId, [FromQuery]int currentPage, [FromQuery]int pageSize)
    {
        var response = await _scheduleJobService.GetScheduleJobsAsync(team, currentPage, pageSize);
        return response;
    }

    /// <summary>
    /// 更新系統中的 Schedule job 相關資訊
    /// </summary>
    /// <param name="job">異動後的 Schedule Job 資訊</param>
    /// <returns></returns>
    [HttpPost("update")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<object>> UpdateJob(ScheduleJob job)
    {
        var response = await _scheduleJobService.UpdateScheduleJobAsync(job);
        return response;
    }

    /// <summary>
    /// 更新系統中的 Schedule job 相關資訊
    /// </summary>
    /// <param name="job">異動後的 Schedule Job 資訊</param>
    /// <returns></returns>
    [HttpPost("update/status")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<object>> UpdateJobStatus(ScheduleJob job)
    {
        var response = await _scheduleJobService.UpdateScheduleJobStatusAsync(job);
        return response;
    }

    /// <summary>
    /// Execute Schedule Job
    /// </summary>
    /// <param name="namespace">Namespace</param>
    /// <param name="scheduleJobName">Schedule Job Name</param>
    /// <returns></returns>
    [HttpPost("execute")]
    // [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<Dictionary<string, string>>> ExecuteJob(string @namespace, string scheduleJobName)
    {
        var response = await _scheduleJobService.ExecuteScheduleJobAsync(@namespace, scheduleJobName);
        return response;
    }

    /// <summary>
    /// 建立系統中的 Schedule Job 資訊
    /// </summary>
    /// <param name="request">Schedule Job 相關資訊</param>
    /// <remarks>紀錄 Schedule Job 相關資訊到 Batch system，此 API 只紀錄相關資訊到 Batch System 不會建立真的 Schedule Job
    /// Sample request:
    ///
    ///     POST /create/scheudle
    ///     {
    ///     }
    ///
    /// </remarks>
    /// <returns></returns>
    [HttpPost("create")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<object>> CreateScheduleJob(ScheduleJob request)
    {
        var response = await _scheduleJobService.CreateScheduleJobAsync(request);
        return response;
    }

    /// <summary>
    /// 刪除系統中的 Schedule Job 資訊
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("delete")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<object>> DeleteScheduleJob(ScheduleJob request)
    {
        var response = await _scheduleJobService.DeleteScheduleJobAsync(request);
        return response;
    }

    /// <summary>
    /// 暫停 Schedule Job
    /// </summary>
    /// <returns></returns>
    [HttpPost("suspend")]
    // [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<object>> SuspendScheduleJob(string @namespace, string scheduleJobName)
    {
        var response = await _scheduleJobService.SuspendScheduleJobAsync(@namespace, scheduleJobName);
        return response;
    }

    /// <summary>
    /// 恢復 Schedule Job
    /// </summary>
    /// <param name="namespace"></param>
    /// <param name="scheduleJobName"></param>
    /// <returns></returns>
    [HttpPost("resume")]
    // [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<object>> ResumeScheduleJob(string @namespace, string scheduleJobName)
    {
        var response = await _scheduleJobService.ResumeScheduleJobAsync(@namespace, scheduleJobName);
        return response;
    }
}