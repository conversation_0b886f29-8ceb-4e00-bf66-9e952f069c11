using Asp.Versioning;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;
using JKOPay.BatchSystem.Core.Services;
using Microsoft.AspNetCore.Mvc;

namespace JKOPay.BatchSystem.Api.Controllers;

/// <summary>
/// All about workflow template action
/// </summary>
/// <param name="argoWorkflowService">Argo Workflow Service</param>
/// <param name="logger">Logger</param>
/// <param name="env">env</param>
[ApiController]
[ApiVersion(1)]
[Route("api/v{v:apiVersion}/workflow-templates")]
public class WorkflowTemplateController(IArgoWorkflowService argoWorkflowService, ILogger<WorkflowTemplateController> logger, [FromKeyedServices("ENV")] string env)
{
    /// <summary>
    /// 取得目前的 Workflow Template 清單
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <param name="labelSelector">Label filter</param>
    /// <returns></returns>
    [HttpGet("{namespace}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<List<WorkflowTemplate>>>(200)]
    public async Task<ResponseModel<List<WorkflowTemplate>>> GetWorkflowTemplates(string @namespace, [FromQuery] string labelSelector="")
    {
        logger.LogInformation("namespace: {Namespace}, filter: {Filter}", @namespace, labelSelector);
        var response = await argoWorkflowService.GetWorkflowTemplatesAsync(@namespace, labelSelector);
        return response;
    }

    /// <summary>
    /// 取得 WorkflowTemplate
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <param name="templateName">Workflow template name</param>
    /// <returns></returns>
    [HttpGet("{namespace}/{templateName}")]
    public async Task<ResponseModel<WorkflowTemplate>> GetWorkflowTemplate(string @namespace, string templateName)
    {
        logger.LogInformation("namespace: {Namespace}, template name: {TemplateName}", @namespace, templateName);
        var response = await argoWorkflowService.GetWorkflowTemplateAsync(@namespace, templateName);
        return response;
    }

    /// <summary>
    /// 刪除 Workflow Template
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <param name="templateName">Workflow template name</param>
    /// <returns></returns>
    [HttpDelete("{namespace}/{templateName}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<object>>(200)]
    public async Task<ResponseModel<object>> DeleteWorkflowTemplate(string @namespace, string templateName)
    {
        logger.LogInformation("namespace: {Namespace}, template name: {TemplateName}", @namespace, templateName);
        var response = await argoWorkflowService.DeleteWorkflowTemplate(@namespace, templateName);
        return response;
    }
}