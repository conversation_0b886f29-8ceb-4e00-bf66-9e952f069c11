using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.Extensions.Logging;

namespace JKOPay.BatchSystem.Api.Controllers
{
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class LogTestController : ControllerBase
    {
        private readonly BatchDbContext _context;
        private readonly ILogger<LogTestController> _logger;

        public LogTestController(BatchDbContext context, ILogger<LogTestController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 測試 Information 等級的 EF Core 日誌（應該被過濾）
        /// </summary>
        [HttpGet("test-info-logs")]
        public async Task<IActionResult> TestInfoLogs()
        {
            _logger.LogInformation("開始測試 Information 等級的 EF Core 日誌");

            // 這會產生 Information 等級的 SQL 執行日誌
            var jobs = await _context.JobRecords.Take(1).ToListAsync();

            _logger.LogInformation("完成測試，查詢到 {Count} 筆資料", jobs.Count);
            return Ok(new { message = "Information 等級測試完成", count = jobs.Count });
        }

        /// <summary>
        /// 測試 Warning 等級的 EF Core 日誌（應該顯示）
        /// </summary>
        [HttpGet("test-warning-logs")]
        public async Task<IActionResult> TestWarningLogs()
        {
            _logger.LogInformation("開始測試 Warning 等級的 EF Core 日誌");

            try
            {
                // 執行一個可能產生 Warning 的查詢（例如沒有索引的複雜查詢）
                var result = await _context.JobRecords
                    .Where(j => j.JobContent.Contains("test")) // 這可能產生警告
                    .Take(1)
                    .ToListAsync();

                _logger.LogInformation("完成測試，查詢到 {Count} 筆資料", result.Count);
                return Ok(new { message = "Warning 等級測試完成", count = result.Count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "測試過程中發生錯誤");
                return StatusCode(500, new { message = "測試失敗", error = ex.Message });
            }
        }

        /// <summary>
        /// 測試 Error 等級的 EF Core 日誌（應該顯示）
        /// </summary>
        [HttpGet("test-error-logs")]
        public async Task<IActionResult> TestErrorLogs()
        {
            _logger.LogInformation("開始測試 Error 等級的 EF Core 日誌");

            try
            {
                // 故意執行一個會產生錯誤的查詢
                await _context.Database.ExecuteSqlRawAsync("SELECT * FROM non_existent_table");

                return Ok(new { message = "不應該到達這裡" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "預期的錯誤發生了");
                return Ok(new { message = "Error 等級測試完成", error = "故意觸發的錯誤" });
            }
        }

        /// <summary>
        /// 手動記錄不同等級的日誌來測試過濾器
        /// </summary>
        [HttpGet("test-manual-logs")]
        public IActionResult TestManualLogs()
        {
            var efLogger = HttpContext.RequestServices
                .GetRequiredService<ILoggerFactory>()
                .CreateLogger("Microsoft.EntityFrameworkCore.Database.Command");

            efLogger.LogInformation("這是手動記錄的 Information 等級日誌 - 應該被過濾");
            efLogger.LogWarning("這是手動記錄的 Warning 等級日誌 - 應該顯示");
            efLogger.LogError("這是手動記錄的 Error 等級日誌 - 應該顯示");

            return Ok(new { message = "手動日誌測試完成" });
        }
    }
}
