using System.Diagnostics;
using Asp.Versioning;
using JKOPay.BatchSystem.Api.Models;
using JKOPay.BatchSystem.Core.Models.Argo.Cron;
using JKOPay.BatchSystem.Core.Utilities;
using Microsoft.AspNetCore.Mvc;

namespace JKOPay.BatchSystem.Api.Controllers;

/// <summary>
/// Controller for handling meter-related operations.
/// </summary>
[ApiController]
[ApiVersion(1)]
[Route("api/v{v:apiVersion}/meter")]

public class MeterController
{
    private IArgoWorkflowUtility _argoWorkflowUtility;
    private CustomMetricsProvider _customMetricsProvider;

    private readonly ILogger<MeterController> _logger;

    private readonly HttpClient _httpClient;

    /// <summary>
    /// Initializes a new instance of the <see cref="MeterController"/> class.
    /// </summary>
    /// <param name="customMetricsProvider">The test metrics instance.</param>
    /// <param name="httpClientFactory">The HTTP client factory.</param>
    /// <param name="argoWorkflowUtility">The Argo Workflow Servcer Utility</param>
    /// <param name="logger">The logger instance.</param>
    public MeterController(
        IHttpClientFactory httpClientFactory,
        CustomMetricsProvider customMetricsProvider,
        IArgoWorkflowUtility argoWorkflowUtility,
        ILogger<MeterController> logger)
    {
        _logger = logger;
        _httpClient = httpClientFactory.CreateClient();
        _customMetricsProvider = customMetricsProvider;
        _argoWorkflowUtility = argoWorkflowUtility;
    }

    /// <summary>
    /// Increments the BooksAddedCounter metric.
    /// </summary>
    /// <returns>An empty object.</returns>
    [HttpGet("add")]
    public Task<object> Add()
    {
        _logger.LogInformation("In Meter Controller Add, {User}", "Jamis");
        // _customMetricsProvider.BooksAddedCounter.Inc();
        return Task.FromResult<object>(new { });
    }

    /// <summary>
    /// Tests the meter functionality and logs trace information.
    /// </summary>
    /// <returns>A dictionary containing trace and response information.</returns>
    [HttpGet("test")]
    public async Task<object> Test()
    {
        var result = new Dictionary<string, object>();

        var activity = Activity.Current;
        activity?.SetTag("Test", "TagTest");
        activity?.AddEvent(new ActivityEvent("TestEvent"));

        if (activity != null)
        {
            _logger.LogInformation("TraceId: {TraceId}, Span Id: {SpanId}", activity.TraceId.ToString(), activity.SpanId.ToString());
            result.Add("traceId", activity.TraceId.ToString());
            result.Add("spanId", activity.SpanId.ToString());
        }

        _logger.LogInformation("In Meter Controller Test, {User}", "Jamis");

        // // Counter 加 1
        var tags = new KeyValuePair<string, object?>[] { new("path", "test"), new("method", "GET"), new("resultCode", "0001") };
        _customMetricsProvider.RequestErrorCounter.Add(1, tags);

        tags = [new KeyValuePair<string, object?>("path", "test"), new KeyValuePair<string, object?>("method", "GET"), new KeyValuePair<string, object?>("resultCode", "0002")];
        _customMetricsProvider.RequestErrorCounter.Add(2, tags);

        // // Counter 加任意數
        // _customMetricsProvider.BooksAddedCounter.Inc(5);
        //
        // // Counter 減一
        // _customMetricsProvider.BooksAddedCounter.Dec();
        //
        // // Counter 減任意數
        // _customMetricsProvider.BooksAddedCounter.Dec(5);

        // Gauge 加任意數，預設加1
        _customMetricsProvider.RequestLatencyGauge.Increment();

        // Gauge 減任意數，預設減1
        // _customMetricsProvider.RequestLatencyGauge.Decrement();

        // Gauge 設定值
        // _customMetricsProvider.RequestLatencyGauge.Set(10);

        // Histogram 記錄數值
        _customMetricsProvider.RequestDurationHistogram.Record(0.3);
        _customMetricsProvider.RequestDurationHistogram.Record(1.2);
        _customMetricsProvider.RequestDurationHistogram.Record(5.5);


        try
        {
            // 不使用標籤
            _customMetricsProvider.RequestLatencySummary.Record(3.0);

            // 使用自訂標籤
            _customMetricsProvider.RequestLatencySummary.Record(0.1, new KeyValuePair<string, object?>("tag1", "value"));
            _customMetricsProvider.RequestLatencySummary.Record(2.5, new KeyValuePair<string, object?>("tag1", "value"), new KeyValuePair<string, object?>("tag2", "test1"));
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }

        var response = await _httpClient.GetAsync("http://localhost:8880/somePathOne").ConfigureAwait(false);
        result.Add("somePathOne", await response.Content.ReadAsStringAsync());
        return result;
    }

    /// <summary>
    /// Get CronWorkflow List on argo workflow server
    /// </summary>
    /// <returns></returns>
    ///
    [HttpGet("scheduleJobList")]
    public async Task<object> ScheduleJobList()
    {
        var obj = new List<CronWorkflow>();
        var namespaces = new[] { "rd1", "rd2", "rd3", "rd4", "rd5", "foundation", "f2e" };
        foreach (var ns in namespaces)
        {
            var cronWorkflows = await _argoWorkflowUtility.GetCronWorkflowsAsync(ns);
            if (!(cronWorkflows.Data?.Count > 0)) continue;
            foreach (var cwf in cronWorkflows.Data)
            {
                var value = 0;
                if (cwf.Spec.Suspend.HasValue)
                {
                    // Suspend false (sj 開啟) -> 1, true (sj 關閉) -> 0
                    value = cwf.Spec.Suspend.Value ? 0 : 1;
                }

                _customMetricsProvider.ScheduledJobsList.Set(
                    value,
                   new KeyValuePair<string, object?>("jobName", cwf.Metadata.Name), new KeyValuePair<string, object?>("namespace", ns));
            }

            obj.AddRange(cronWorkflows.Data);
        }

        return obj;
    }
}
