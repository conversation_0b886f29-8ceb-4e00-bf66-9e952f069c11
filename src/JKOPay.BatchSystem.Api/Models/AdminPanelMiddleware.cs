using System.Text;
using System.Text.Json;

namespace JKOPay.BatchSystem.Api.Models;

/// <summary>
///
/// </summary>
public class AdminPanelMiddleware
{
    private readonly RequestDelegate _next;

    /// <summary>
    ///
    /// </summary>
    /// <param name="next"></param>
    public AdminPanelMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    /// <summary>
    /// InvokeAsync
    /// </summary>
    /// <param name="context"></param>
    public async Task InvokeAsync(HttpContext context)
    {
        // 將原始 request body 讀取為字符串
        if (context.Request.Headers.ContainsKey("adminpanel"))
        {
            context.Request.EnableBuffering(); // 允許多次讀取 body
            var originalBody = await new StreamReader(context.Request.Body).ReadToEndAsync();
            context.Request.Body.Position = 0; // 重置位置，以便後續 middleware 讀取

            // 解析 JSON 來獲取 data 的 value
            using JsonDocument doc = JsonDocument.Parse(originalBody);
            if (doc.RootElement.TryGetProperty("data", out JsonElement dataElement))
            {
                // 取出 data 的 value 並替換 request body
                var newDataValue = dataElement.ToString();

                // 用新的 body 替換原本的 request body
                var newBody = new MemoryStream(Encoding.UTF8.GetBytes(newDataValue));
                context.Request.Body = newBody;
                context.Request.ContentLength = newBody.Length;
            }
        }

        // 繼續處理 pipeline
        await _next(context);
    }
}