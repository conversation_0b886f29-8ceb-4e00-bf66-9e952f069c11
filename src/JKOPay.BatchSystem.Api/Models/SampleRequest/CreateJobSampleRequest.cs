using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Models.Core;
using Swashbuckle.AspNetCore.Filters;

namespace JKOPay.BatchSystem.Api.Models.SampleRequest;

/// <summary>
/// Create job sample request
/// </summary>
public class CreateJobSampleRequest : IExamplesProvider<CreateJob>
{
    /// <inheritdoc />
    public CreateJob GetExamples()
    {
        return new CreateJob
        {
            Priority = JobPriorityEnum.Immediate,
            JobName = "SampleJob",
            WorkflowTemplateName = "sleep-workflow-template",
            Message = new Dictionary<string, object>
            {
                {
                    "jobData", new
                    {
                        Parameter = new { },
                        JobName = "SampleJobName"
                    }
                }
            },
            ServiceNamespace = "foundation"
        };
    }
}