using JKOPay.BatchSystem.Core.Models.Api;
using Swashbuckle.AspNetCore.Filters;

namespace JKOPay.BatchSystem.Api.Models.SampleRequest;

/// <summary />
public class CreateScheduleJobSampleRequest : IExamplesProvider<CreateScheduleJob>
{
    /// <summary>
    ///
    /// </summary>
    /// <returns></returns>
    public CreateScheduleJob GetExamples()
    {
        return new CreateScheduleJob
        {
        };
    }
}