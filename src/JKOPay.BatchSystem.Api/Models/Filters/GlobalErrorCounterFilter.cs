using JKOPay.BatchSystem.Core.Models.Api;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace JKOPay.BatchSystem.Api.Models.Filters;

/// <summary>
///
/// </summary>
public class GlobalErrorCounterFilter : IActionFilter
{
    private readonly ILogger<GlobalErrorCounterFilter> _logger;

    private readonly CustomMetricsProvider _customMetricsProvider;

    /// <summary>
    ///
    /// </summary>
    /// <param name="customMetricsProvider"></param>
    /// <param name="logger"></param>
    public GlobalErrorCounterFilter(CustomMetricsProvider customMetricsProvider, ILogger<GlobalErrorCounterFilter> logger)
    {
        _customMetricsProvider = customMetricsProvider;
        _logger = logger;
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="context"></param>
    public void OnActionExecuting(ActionExecutingContext context)
    {
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="context"></param>
    /// <exception cref="NotImplementedException"></exception>
    public void OnActionExecuted(ActionExecutedContext context)
    {
        // 取得 Result 並轉型成 ResponseModel<object>
        if (context.Result is not ObjectResult { Value: ResponseModel<object> response }) return;

        // 現在你可以存取 response 的屬性
        var resultCode = response.Result;

        // 只紀錄非成功的回應
        if (resultCode == "0001") return;

        var tags = new KeyValuePair<string, object?>[]
        {
            new("path", context.HttpContext.Request.Path),
            new ("method", context.HttpContext.Request.Method),
            new ("resultCode", resultCode)
        };

        _customMetricsProvider.RequestErrorCounter.Add(1, tags);
    }
}