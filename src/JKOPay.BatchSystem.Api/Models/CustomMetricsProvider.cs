using System.Diagnostics.Metrics;
using JKOPay.Platform.OpenTelemetry.Metrics;

namespace JKOPay.BatchSystem.Api.Models;

/// <summary>
/// Custom metrics provider
/// </summary>
public class CustomMetricsProvider
{
    /// <summary>
    /// Counter for request error
    /// </summary>
    public Counter<int> RequestErrorCounter { get; }

    /// <summary>
    /// Status of schedule job
    /// </summary>
    public Gauge ScheduledJobsList { get; }

    /// <summary>
    /// Histogram of HTTP request durations
    /// </summary>
    public Histogram RequestDurationHistogram { get; }

    /// <summary>
    /// Summary of request latencies
    /// </summary>
    public Summary RequestLatencySummary { get; }

    /// <summary>
    /// Gauge for request latencies
    /// </summary>
    public Gauge RequestLatencyGauge { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="CustomMetricsProvider"/> class.
    /// </summary>
    public CustomMetricsProvider(IMeterFactory meterFactory)
    {
        var meter = meterFactory.Create("BatchSystem.Metrics");
        RequestErrorCounter = meter.CreateCounter<int>("request_error_counter", "request");
        ScheduledJobsList = new Gauge(
            meter: meter,
            "schedule_job_list",
            "job",
            "Status of schedule job");
        var buckets = new double[] { 0.1, 0.5, 1, 2.5, 5, 10 };
        RequestDurationHistogram = new Histogram(
            meter: meter,
            "request_duration_histogram",
            "seconds",
            "Histogram of HTTP request durations",
            buckets, new KeyValuePair<string, object?>[] { new("tag1", "value1"), new("tag2", "value2") });
        var quantiles = new double[] { 0.5, 0.95, 0.99 };
        RequestLatencySummary = new Summary(
            meter: meter,
            "request_latency_summary",
            "seconds",
            "Summary of request latencies",
            quantiles, ["tag1", "tag2"]);
        RequestLatencyGauge = new Gauge(
            meter: meter,
            "request_latency_gauge",
            "seconds",
            "Gauge for request latencies");
    }
}
