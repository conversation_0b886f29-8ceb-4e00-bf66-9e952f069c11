using System.Text.RegularExpressions;
using JKOPay.BatchSystem.Core.Models.Option.ArgoWorkflow;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace JKOPay.BatchSystem.Api.Models.HealthChecks;

/// <inheritdoc />
public class LivenessHealthCheck : IHealthCheck
{
    private readonly BatchDbContext _context;

    private readonly HttpClient _httpClient;

    private readonly ArgoWorkflowOption _option;

    private ILogger<LivenessHealthCheck> _logger;

    /// <summary>
    /// Health check for batch system.
    /// </summary>
    /// <param name="option"></param>
    /// <param name="context"></param>
    /// <param name="factory"></param>
    /// <param name="logger"></param>
    public LivenessHealthCheck(
        ArgoWorkflowOption option,
        BatchDbContext context,
        IHttpClientFactory factory,
        ILogger<LivenessHealthCheck> logger)
    {
        _option = option;
        _httpClient = factory.CreateClient("ArgoWorkflow");
        _context = context;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = new CancellationToken())
    {
        var data = new Dictionary<string, object>();
        try
        {
            var connectionString = _context.Database.GetConnectionString();
            MaskSecretData(ref connectionString);
            data.Add("ConnectionString", connectionString!);

            var url = new Uri($"{_option.Domain}api/v1/version");
            var response = await _httpClient.GetAsync(url, cancellationToken);

            // 只有在連線失敗時才記錄錯誤
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to connect to ArgoWorkflow: {StatusCode}", response.StatusCode);
            }

            data.Add("ArgoWorkflow", response.StatusCode);

            var topicNames = new[]
            {
                $"{_option.KafkaTopic.Priority[..5]}*****{_option.KafkaTopic.Priority[^10..]}",
                $"{_option.KafkaTopic.Normal[..5]}*****{_option.KafkaTopic.Normal[^10..]}"
            };

            data.Add("TopicNames", topicNames);

            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "local")
            {
                return await Task.FromResult(HealthCheckResult.Healthy("Batch system is healthy.", data));
            }

            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "development")
                return await Task.FromResult(HealthCheckResult.Healthy("Batch system is healthy.", data));

            const string tokenPath = "/var/run/secrets/kubernetes.io/serviceaccount/token";
            var token = File.ReadAllText(tokenPath);
            var showToken = $"{token[..2]}*****{token[^2..]}";
            data.Add("Token", showToken);
            return await Task.FromResult(HealthCheckResult.Healthy("Batch system is healthy.", data));
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Batch system is unhealthy.");
            return await Task.FromResult(HealthCheckResult.Unhealthy("Batch system is unhealthy.", data: data));
        }
    }

    private static void MaskSecretData(ref string? data)
    {
        if (data == null) return;

        var split = data.Split(';');
        for (var i = 0; i < split.Length; i++)
        {
            var parts = split[i].Split('=');
            if (parts.Length <= 1) continue;

            if (IsMaskMatch(split[i], new[] { "*password*", "*pwd*", "*user*" }))
            {
                var tmp = parts[1];
                split[i] = $"{parts[0]}={tmp[..2]}*****{tmp[^2..]}";
            }
            else if (IsMaskMatch(split[i], new[] { "*server*" }))
            {
                var server = parts[1];
                split[i] = $"{parts[0]}={server[..3]}*****{server[^3..]}";
            }
        }

        data = string.Join(';', split);
    }

    /// <summary>
    /// Is secret data match with mask.
    /// </summary>
    /// <param name="data">secret data</param>
    /// <param name="blacklist">black list</param>
    /// <returns></returns>
    private static bool IsMaskMatch(string data, string[] blacklist)
    {
        return blacklist.Any(item => Regex.IsMatch(data, WildCardToRegular(item), RegexOptions.IgnoreCase | RegexOptions.CultureInvariant));
    }

    private static string WildCardToRegular(string value)
    {
        return "^" + Regex.Escape(value).Replace("\\*", ".*") + "$";
    }
}
