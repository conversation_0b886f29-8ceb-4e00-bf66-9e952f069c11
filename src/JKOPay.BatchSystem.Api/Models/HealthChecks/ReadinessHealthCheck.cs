using JKOPay.BatchSystem.Core.Models.Option.ArgoWorkflow;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace JKOPay.BatchSystem.Api.Models.HealthChecks;

/// <summary>
/// Readiness health check.
/// </summary>
public class ReadinessHealthCheck : IHealthCheck
{
    private readonly ArgoWorkflowOption _argoWorkflowOption;

    private readonly HttpClient _httpClient;

    /// <summary>
    /// Rediness health check.
    /// </summary>
    /// <param name="factory"></param>
    /// <param name="argoWorkflowOption"></param>
    public ReadinessHealthCheck(IHttpClientFactory factory, ArgoWorkflowOption argoWorkflowOption)
    {
        _httpClient = factory.CreateClient("ArgoWorkflow");
        _argoWorkflowOption = argoWorkflowOption;
    }

    /// <summary>
    /// Check health for readiness.
    /// </summary>
    /// <param name="context"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = new CancellationToken())
    {
        var url = new Uri(_argoWorkflowOption.Domain);
        var response = await _httpClient.GetAsync(url, cancellationToken);
        return response.IsSuccessStatusCode ? HealthCheckResult.Healthy() : HealthCheckResult.Unhealthy();
    }
}
