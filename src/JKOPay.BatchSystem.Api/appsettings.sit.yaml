---
Logging:
  LogLevel:
    Default: Information
    Microsoft.AspNetCore: Warning
Serilog:
  Using:
  - Serilog.Sinks.Console
  - Serilog.Sinks.Async
  MinimumLevel:
    Default: Information
    Override:
      Microsoft.AspNetCore: Warning
      Microsoft.EntityFrameworkCore.Database.Command: Warning
  Filter: []
  WriteTo:
  - Name: Async
    Args:
      configure:
      - Name: Console
        Args:
          formatter:
            type: "Serilog.Formatting.Json.JsonFormatter, Serilog"
            renderMessage: true
  Enrich:
    - FromLogContext
ArgoWorkflow:
  Domain: https://argo-workflow.jkopay.app/
  KafkaTopic:
     Priority: foundation_batchsystem_workflow_sit
     Normal: foundation_batchsystem_workflow_sit
OpenTelemetry:
  Exporter:
    OTLP:
      Host: http://sit-sre-alloy-idc.monitoring
      Port: 4317
