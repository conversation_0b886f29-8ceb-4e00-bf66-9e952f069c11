{"ProviderNamespace": "JKOPay.BatchSystem.Api", "Name": "BatchSystem.Metrics", "Metrics": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MetricName": "request_error_counter", "Type": "Counter", "DataType": "int", "Unit": "request", "Description": "Counter for request error", "Buckets": null, "Quantiles": null, "AllowKeys": null, "Labels": [{"Name": "path", "Value": ""}, {"Name": "method", "Value": ""}, {"Name": "resultCode", "Value": ""}]}, {"Name": "RequestDurationHistogram", "MetricName": "request_duration_histogram", "Type": "Histogram", "DataType": null, "Unit": "seconds", "Description": "Histogram of HTTP request durations", "Buckets": [0.1, 0.5, 1, 2.5, 5, 10], "Quantiles": null, "AllowKeys": null, "Labels": [{"Name": "tag1", "Value": "value1"}, {"Name": "tag2", "Value": "value2"}]}, {"Name": "RequestLatencySummary", "MetricName": "request_latency_summary", "Type": "Summary", "DataType": null, "Unit": "seconds", "Description": "Summary of request latencies", "Buckets": null, "Quantiles": [0.5, 0.95, 0.99], "AllowKeys": ["tag1", "tag2"], "Labels": null}, {"Name": "RequestLatencyGauge", "MetricName": "request_latency_gauge", "Type": "Gauge", "DataType": null, "Unit": "seconds", "Description": "Gauge for request latencies", "Buckets": null, "Quantiles": null, "AllowKeys": null, "Labels": [{"Name": "tag1", "Value": "value1"}, {"Name": "tag2", "Value": "value2"}]}]}