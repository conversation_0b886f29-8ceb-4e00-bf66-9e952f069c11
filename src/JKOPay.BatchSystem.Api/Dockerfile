﻿FROM mcr.microsoft.com/dotnet/aspnet:9.0-alpine-composite AS base
USER $APP_UID
WORKDIR /app
EXPOSE 9090

FROM mcr.microsoft.com/dotnet/sdk:9.0-alpine AS build
ARG TARGETARCH
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Directory.Packages.props", "./"]
COPY ["nuget.config", "./"]
COPY ["src/JKOPay.BatchSystem.Api/JKOPay.BatchSystem.Api.csproj", "src/JKOPay.BatchSystem.Api/"]
COPY ["src/JKOPay.BatchSystem.Core/JKOPay.BatchSystem.Core.csproj", "src/JKOPay.BatchSystem.Core/"]
RUN dotnet restore "src/JKOPay.BatchSystem.Api/JKOPay.BatchSystem.Api.csproj" -v d
COPY . .
WORKDIR "/src/src/JKOPay.BatchSystem.Api"
RUN dotnet build "JKOPay.BatchSystem.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "JKOPay.BatchSystem.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
ENV ASPNETCORE_ENVIRONMENT=development
ENV ASPNETCORE_HTTP_PORTS 9090
ENV ASPNETCORE_URLS=http://*:9090
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "JKOPay.BatchSystem.Api.dll"]
