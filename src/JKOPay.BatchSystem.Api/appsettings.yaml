---
Logging:
  LogLevel:
    Default: Information
    Microsoft.AspNetCore: Warning
AllowedHosts: "*"
Serilog:
  Using:
  - Serilog.Sinks.Console
  - Serilog.Sinks.Async
  - Serilog.Exceptions
  - Serilog.Expressions
  MinimumLevel:
    Default: Information
    Override:
      Microsoft.AspNetCore: Warning
  Filter:
    - Name: ByExcluding
      Args:
        expression: "@RequestPath like '/healthz'"
    - Name: ByExcluding
      Args:
        expression: "@RequestPath like '/health/live'"
  WriteTo:
  - Name: Async
    Args:
      configure:
      - Name: Console
        Args:
          formatter:
            type: "Serilog.Formatting.Json.JsonFormatter, Serilog"
            renderMessage: true
  Enrich:
    - FromLogContext
    - WithExceptionDetails
OpenTelemetry:
  Endpoint: http://localhost:24317
Kafka:
  Server: ka1.jkopay.app:9093,ka2.jkopay.app:9093,ka3.jkopay.app:9093
  Topic: found-argo-workflow-lab
ArgoWorkflow:
  Domain: https://localhost:2746/
  KafkaTopic:
    Priority: foundation_batchsystem_workflow_dev
    Normal: foundation_batchsystem_workflow_dev
  Workflow:
    Submit: api/v1/workflows/argo/submit
    Resubmit: api/v1/workflows/{namespace}/{workflowName}/resubmit
    List: api/v1/workflows
    Log: api/v1/workflows/{namespace}/{name}/log
    Stop: api/v1/workflow/{namespace}/{name}/stop
    Terminate: api/v1/workflows/{namespace}/{name}/terminate
    Delete: api/v1/workflows/{namespace}/{name}
  WorkflowTemplate:
    List: api/v1/workflow-templates
    Delete: api/v1/workflow-templates
    Get: api/v1/workflow-templates/{namespace}/{name}
  Sensor:
    List: api/v1/sensors
    Delete: api/v1/sensors
  Eventsource:
    List: api/v1/event-sources
    Delete: api/v1/event-sources
  CronWorkflow:
    List: api/v1/cron-workflows/{namespace}
    Delete: api/v1/cron-workflow
    Submit: api/v1/workflows/{namespace}/submit
    Suspend: api/v1/cron-workflows/{namespace}/{name}/suspend
    Resume: api/v1/cron-workflows/{namespace}/{name}/resume