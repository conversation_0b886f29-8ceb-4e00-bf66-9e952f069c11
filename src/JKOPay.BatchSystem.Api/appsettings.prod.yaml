---
Logging:
  LogLevel:
    Default: Information
    Microsoft.AspNetCore: Warning
Serilog:
  Using:
  - Serilog.Sinks.Console
  - Serilog.Sinks.Async
  MinimumLevel:
    Default: Information
    Override:
      Microsoft.AspNetCore: Warning
      Microsoft.EntityFrameworkCore.Database.Command: Warning
  Filter: []
  WriteTo:
  - Name: Async
    Args:
      configure:
      - Name: Console
        Args:
          formatter:
            type: "Serilog.Formatting.Json.JsonFormatter, Serilog"
            renderMessage: true
  Enrich:
    - FromLogContext
Kafka:
  Server: kafka-07.jkopay.prod:9093,kafka-08.jkopay.prod:9093,kafka-09.jkopay.prod:9093,kafka-10.jkopay.prod:9093,kafka-11.jkopay.prod:9093,kafka-12.jkopay.prod:9093
  Topic: found-batchsystem-workflow
ArgoWorkflow:
  Domain: https://argo-workflow.jkopay.com/
  KafkaTopic:
    Priority: foundation_batchsystem_workflow
    Normal: foundation_batchsystem_workflow