---
Logging:
  LogLevel:
    Default: Information
    Microsoft.AspNetCore: Warning

Serilog:
  Using:
  - Serilog.Sinks.Console
  - Serilog.Sinks.Async
  - Serilog.Exceptions
  MinimumLevel:
    Default: Information
    Override:
      Microsoft.AspNetCore: Warning
  Filter:
    - Name: ByExcluding
      Args:
        expression: "@RequestPath like '/health'"
    - Name: ByExcluding
      Args:
        expression: "@RequestPath like '/health/live'"
  WriteTo:
  - Name: Async
    Args:
      configure:
      - Name: Console
        Args:
          formatter:
            type: "Serilog.Formatting.Json.JsonFormatter, Serilog"
            renderMessage: true
ArgoWorkflow:
  Domain: https://argo-server.argo.svc.cluster.local:2746/
OpenTelemetry:
  Exporter:
    OTLP:
      Host: http://alloy.monitor
      Port: 4317
