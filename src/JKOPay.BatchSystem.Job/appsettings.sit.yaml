---
Logging:
  LogLevel:
    Default: Information
    Microsoft.AspNetCore: Warning
Serilog:
  Using:
  - Serilog.Sinks.Console
  - Serilog.Sinks.Async
  - Serilog.Exceptions
  MinimumLevel:
    Default: Information
    Override:
      Microsoft.AspNetCore: Warning
  WriteTo:
  - Name: Async
    Args:
      configure:
      - Name: Console
        Args:
          formatter:
            type: "Serilog.Formatting.Json.JsonFormatter, Serilog"
            renderMessage: true
  Enrich:
    - FromLogContext
    - WithExceptionDetails
BatchSystemAPI:
  Domain: http://sit-foundation-jkopay-batchsystem-api-svc.foundation:9090/
ArgoWorkflow:
  Domain: https://argo-workflow.jkopay.app/
  Workflow:
    Submit: api/v1/workflows/argo/submit
    List: api/v1/workflows
    Log: api/v1/workflows/{namespace}/{jobName}/log
  WorkflowTemplate:
    List: api/v1/workflow-templates
  Sensor:
    List: api/v1/sensors
  Eventsource:
    List: api/v1/event-sources