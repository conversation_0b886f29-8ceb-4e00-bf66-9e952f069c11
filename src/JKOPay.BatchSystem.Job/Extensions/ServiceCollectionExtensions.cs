using Confluent.Kafka;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace JKOPay.BatchSystem.Job.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddOptions(this IServiceCollection services, params (Type Type, string Key)[] args)
    {
        ArgumentNullException.ThrowIfNull(services);

        var serviceProvider = services.BuildServiceProvider();
        var configuration = serviceProvider.GetRequiredService<IConfiguration>();
        if (configuration == null)
        {
            throw new ArgumentNullException(nameof(configuration));
        }

        foreach (var (type, key) in args)
        {
            var tmpConfig = Convert.ChangeType(Activator.CreateInstance(type), type);
            configuration.GetSection($"{key}")
                .Bind(tmpConfig);
            services.AddSingleton(type, p => tmpConfig!);
        }

        return services;
    }

    public static IServiceCollection AddOption<T>(this IServiceCollection services, string key)
    {
        ArgumentNullException.ThrowIfNull(services);

        var serviceProvider = services.BuildServiceProvider();
        var configuration = serviceProvider.GetRequiredService<IConfiguration>();
        if (configuration == null)
        {
            throw new ArgumentNullException(nameof(configuration));
        }

        var option = configuration.GetSection($"{key}")
            .Get<T>();
        services.AddSingleton(typeof(T), p => option!);

        return services;
    }

    public static IServiceCollection AddKafkaProducer(this IServiceCollection services)
    {
        var user = Environment.GetEnvironmentVariable("KAFKA_USERNAME");
        var pw = Environment.GetEnvironmentVariable("KAFKA_PASSWORD");
        var serviceProvider = services.BuildServiceProvider();
        var configuration = serviceProvider.GetRequiredService<IConfiguration>();
        if (configuration == null)
        {
            throw new ArgumentNullException(nameof(configuration));
        }

        var option = configuration.GetSection("Kafka");
        var kafkaHost = option["Server"];
        var producer = new ProducerBuilder<string, string>(new ProducerConfig()
        {
            // The Kafka endpoint address
            BootstrapServers = kafkaHost,
            SecurityProtocol = SecurityProtocol.SaslPlaintext,
            SaslMechanism = SaslMechanism.Plain,
            SaslUsername = user,
            SaslPassword = pw,
            EnableDeliveryReports = true,
            Acks = Acks.All
        }).Build();

        services.AddSingleton<IProducer<string, string>>(producer);

        return services;
    }
}