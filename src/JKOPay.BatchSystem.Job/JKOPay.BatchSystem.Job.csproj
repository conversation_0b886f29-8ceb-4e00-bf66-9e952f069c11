﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>
    <PropertyGroup>
      <!-- 確保使用中央套件管理 -->
      <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
      <!-- 指定要使用的 NuGet 配置檔案位置 -->
      <RestoreConfigFile>$(MSBuildThisFileDirectory)..\..\nuget.config</RestoreConfigFile>
    </PropertyGroup>

    <ItemGroup>
      <Content Include="..\..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Confluent.Kafka" />
      <PackageReference Include="JKOPay.Platform.BatchSystem" />
      <PackageReference Include="JKOPay.Platform.HttpClientExtension" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" >
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.Extensions.Hosting" />
      <PackageReference Include="NetEscapades.Configuration.Yaml" />
      <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" />
      <PackageReference Include="Serilog.Exceptions" />
      <PackageReference Include="Serilog.Sinks.Async" />
    </ItemGroup>

    <ItemGroup>
      <None Update="appsettings.local.yaml">
        <DependentUpon>appsettings.yaml</DependentUpon>
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.development.yaml">
        <DependentUpon>appsettings.yaml</DependentUpon>
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.yaml">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.sit.yaml">
        <DependentUpon>appsettings.yaml</DependentUpon>
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.prod.yaml">
        <DependentUpon>appsettings.yaml</DependentUpon>
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\JKOPay.BatchSystem.Core\JKOPay.BatchSystem.Core.csproj" />
    </ItemGroup>

</Project>
