---
Logging:
  LogLevel:
    Default: Information
    Microsoft.AspNetCore: Warning
Serilog:
  Using:
  - Serilog.Sinks.Console
  - Serilog.Sinks.Async
  - Serilog.Exceptions
  MinimumLevel:
    Default: Information
    Override:
      Microsoft.AspNetCore: Warning
  WriteTo:
  - Name: Async
    Args:
      configure:
      - Name: Console
        Args:
          formatter:
            type: "Serilog.Formatting.Json.JsonFormatter, Serilog"
            renderMessage: true
  Enrich:
    - FromLogContext
    - WithExceptionDetails
Kafka:
  Server: ka1.jkopay.app:9093,ka2.jkopay.app:9093,ka3.jkopay.app:9093
  Topic: found-argo-workflow-lab
ArgoWorkflow:
  Domain: https://argo-workflow.jkopay.app/
  Workflow:
    Submit: api/v1/workflows/argo/submit
    List: api/v1/workflows
    Log: api/v1/workflows/{namespace}/{jobName}/log
    Stop: api/v1/workflow/{namespace}/{jobName}/stop
    Terminate: api/v1/workflows/{namespace}/{jobName}/terminate
    Delete: api/v1/workflows/{namespace}/{jobName}
  WorkflowTemplate:
    List: api/v1/workflow-templates
  Sensor:
    List: api/v1/sensors
  Eventsource:
    List: api/v1/event-sources