﻿FROM mcr.microsoft.com/dotnet/aspnet:9.0-alpine-composite AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:9.0-alpine AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Directory.Packages.props", "./"]
COPY ["nuget.config", "./"]
COPY ["src/JKOPay.BatchSystem.Job/JKOPay.BatchSystem.Job.csproj", "src/JKOPay.BatchSystem.Job/"]
COPY ["src/JKOPay.BatchSystem.Core/JKOPay.BatchSystem.Core.csproj", "src/JKOPay.BatchSystem.Core/"]
RUN dotnet restore "src/JKOPay.BatchSystem.Job/JKOPay.BatchSystem.Job.csproj"
COPY . .
WORKDIR "/src/src/JKOPay.BatchSystem.Job"
RUN dotnet build "JKOPay.BatchSystem.Job.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "JKOPay.BatchSystem.Job.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
USER $APP_UID
ENTRYPOINT ["dotnet", "JKOPay.BatchSystem.Job.dll"]
