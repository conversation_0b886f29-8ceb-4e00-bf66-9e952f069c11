using System.Text;
using JKOPay.Platform.BatchSystem.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Job.Models.Job;

public class SlackJob(IHttpClientFactory factory, ILogger<SlackJob> logger) : Base<PERSON><PERSON>(logger), IJob
{
    private readonly HttpClient _httpClient = factory.CreateClient("test");

    public override async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        var tmp = JsonConvert.DeserializeObject<SlackContent>(base.Arg!);
        var title = $"WorkflowId: test1, Content: {tmp!.Content}";
        var text = $"Result: Succeeded. {DateTime.Now:yyyy-mm-dd HH:mm:ss}";

        //JSON訊息
        var data = new
        {
            // 填入 Webhook 接收到的 userId
            channel = "team-eng-foundation-testing",
            text = $"Workflow test1",
            attachments = new List<object>()
            {
                new
                {
                    color = "#36a64f",
                    title = title,
                    text = text
                }
            },
        };

        //傳送訊息
        //Channel access token
        logger.LogInformation("Send message to slack");
        var channelAccessToken = Program.SlackToken;
        await PushMessage(channelAccessToken, data);
    }

    private async Task PushMessage(string channelAccessToken, object data)
    {
        logger.LogInformation("Token: {TOKEN}", channelAccessToken);
        // 先清除任何現有的 Authorization header
        if (_httpClient.DefaultRequestHeaders.Contains("Authorization"))
        {
            _httpClient.DefaultRequestHeaders.Remove("Authorization");
        }

        // 添加 Authorization header
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {channelAccessToken}");

        var payload = JsonConvert.SerializeObject(data);
        logger.LogInformation("Payload: {PAYLOAD}", payload);

        var content = new StringContent(payload, Encoding.UTF8, "application/json");
        var response = await _httpClient.PostAsync("https://slack.com/api/chat.postMessage", content).ConfigureAwait(false);

        var responseBody = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
        logger.LogInformation("Response status: {STATUS}, body: {BODY}", response.StatusCode, responseBody);

        // 解析 Slack API 回應以檢查 ok 欄位
        try
        {
            var slackResponse = JsonConvert.DeserializeObject<SlackApiResponse>(responseBody);
            if (slackResponse?.Ok == true)
            {
                logger.LogInformation("Push message success");
            }
            else
            {
                logger.LogError("Push message failed: {ERROR}", slackResponse?.Error ?? "Unknown error");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to parse Slack API response");
        }
    }
}

// 用於解析 Slack API 回應的類別
public class SlackApiResponse
{
    [JsonProperty("ok")]
    public bool Ok { get; set; }

    [JsonProperty("error")]
    public string? Error { get; set; }
}