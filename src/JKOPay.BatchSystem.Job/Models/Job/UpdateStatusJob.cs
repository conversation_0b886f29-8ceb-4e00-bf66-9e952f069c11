// ReSharper disable ConvertToPrimaryConstructor
// ReS<PERSON>per disable once ClassWithVirtualMembersNeverInherited.Global
// ReSharper disable ClassWithVirtualMembersNeverInherited.Global

using Microsoft.EntityFrameworkCore;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Models.Job;
using JKOPay.BatchSystem.Core.Services;
using JKOPay.BatchSystem.Core.Utilities;
using JKOPay.Platform.BatchSystem.Models;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Workflow = JKOPay.BatchSystem.Core.Models.Argo.Workflow.Workflow;

namespace JKOPay.BatchSystem.Job.Models.Job;

/// <summary>
/// Update job and workflow information
/// </summary>
public class UpdateStatusJob : BaseJob, IJob
{
    private readonly ILogger<UpdateStatusJob> _logger;

    private readonly IArgoWorkflowUtility _argoUtility;

    private readonly IJobService _jobService;

    private readonly BatchDbContext _dbContext;

    public UpdateStatusJob(IJobService jobService, IArgoWorkflowUtility argoWorkflow, BatchDbContext dbContext, ILogger<UpdateStatusJob> logger) : base(logger)
    {
        _jobService = jobService;
        _argoUtility = argoWorkflow;
        _dbContext = dbContext;
        _logger = logger;
    }

    public override async Task ExecuteAsync(CancellationToken token)
    {
        _logger.LogInformation("In update status execute.");
        var workflows = await _argoUtility.GetWorkflowsAsync();
        if (workflows.IsSuccess == false)
        {
            _logger.LogError(workflows.ErrorMessage);
            return ;
        }

        var data = workflows.Data?
            .Where(p => p.Status.StartedAt >= GetNow.DateTime.AddMinutes(-100))
            .OrderBy(p => p.Status.StartedAt)
            .ToList();
        if (data == null)
        {
            return ;
        }

        _logger.LogInformation("Workflow count: {Count}", data.Count);
        foreach (var workflow in data)
        {
            _logger.LogInformation(
                "Workflow Id: {WorkflowId},Kind: {Kind} Name: {WorkflowName}, Status: {Status}, StartedAt: {StartedAt}, FinishedAt: {FinishedAt}",
                workflow.Metadata.Uid,
                workflow.Metadata.OwnerReferences?.First().Kind,
                workflow.Metadata.Name,
                workflow.Status.Phase,
                workflow.Status.StartedAt.ToUniversalTime(),
                workflow.Status.FinishedAt.ToUniversalTime());

            switch (workflow.Status.Phase)
            {
                case "Succeeded":
                    await UpdateJobRecordAsync(workflow, JobStatusEnum.WorkflowComplete);
                    await WorkflowHandlerAsync(workflow, "");
                    break;
                case "Failed":
                    var logs = await _argoUtility.GetWorkflowLogAsync(workflow.Metadata.Namespace, workflow.Metadata.Name);
                    var log = JsonConvert.SerializeObject(logs.Data);
                    await UpdateJobRecordAsync(workflow, JobStatusEnum.Error);
                    await WorkflowHandlerAsync(workflow, log);
                    break;
                default:
                    await WorkflowHandlerAsync(workflow, "");
                    break;
            }
        }

        _logger.LogInformation("Data process completed.");
    }

    protected virtual async Task UpdateJobRecordAsync(Workflow wf, JobStatusEnum status)
    {
        // 這邊拿不到 JobId 所以傳空字串
        var updateJob = new UpdateJob
        {
            JobId = "",
            WorkflowName = "",
            WorkflowId = wf.Metadata.Uid,
            Status = status.ToString()
        };

        await _jobService.UpdateJobStatusAsync(updateJob);
        if (wf.Metadata.OwnerReferences?.First().Kind == "CronWorkflow")
        {
            await UpdateScheduleJobAsync(wf);
        }
    }

    protected virtual async Task WorkflowHandlerAsync(Workflow wf, string log)
    {
        await UpdateWorkflowAsync(wf, log);
    }

    /// <summary>
    /// Update schedule job information
    /// </summary>
    /// <param name="wf">Workflow</param>
    protected virtual async Task UpdateScheduleJobAsync(Workflow wf)
    {
        _logger.LogInformation("In update schedule job async");
        _logger.LogInformation(JsonConvert.SerializeObject(wf));
        var result = await _dbContext.ScheduleJobs
            .Where(p => p.CronWorkflowId == wf.Metadata.OwnerReferences!.First().Uid)
            .ExecuteUpdateAsync(p =>
                p.SetProperty(item => item.LatestStatus, wf.Status.Phase)
                    .SetProperty(item => item.LatestStartedAt, wf.Status.StartedAt.ToUniversalTime())
                    .SetProperty(item => item.LatestFinishedAt, wf.Status.FinishedAt.ToUniversalTime())
                    .SetProperty(item => item.LatestWorkflowId, wf.Metadata.Uid)
                    .SetProperty(item => item.LatestWorkflowName, wf.Metadata.Name)
                    .SetProperty(item => item.UpdatedTime, GetNow.DateTime));
        if (result < 1)
        {
            _logger.LogWarning("Update cron job information failed:({ScheduleJobName})", wf.Metadata.OwnerReferences?.First().Name);
        }
    }

    /// <summary>
    /// Update workflow information
    /// </summary>
    /// <param name="wf">Workflow</param>
    /// <param name="log">Workflow log</param>
    protected virtual async Task UpdateWorkflowAsync(Workflow wf, string log)
    {
        var now = GetNow.DateTime;
        var workflowUpdateResult = await _dbContext.Workflows.Where(p => p.WorkflowId == wf.Metadata.Uid)
            .ExecuteUpdateAsync(
                p =>
                    p.SetProperty(item => item.Status, wf.Status.Phase)
                        .SetProperty(item => item.Log, log)
                        .SetProperty(item => item.StartedAt, wf.Status.StartedAt.ToUniversalTime())
                        .SetProperty(item => item.FinishedAt, wf.Status.FinishedAt.ToUniversalTime())
                        .SetProperty(item => item.UpdatedTime, now));

        if (workflowUpdateResult == 0)
        {
            _logger.LogWarning("Update workflow information failed ({WorkflowId})", wf.Metadata.Uid);
        }
    }

    protected virtual DateTimeOffset GetNow => DateTimeOffset.UtcNow;
}