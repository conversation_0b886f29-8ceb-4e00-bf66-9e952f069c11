using JKOPay.Platform.BatchSystem.Models;
using Microsoft.Extensions.Logging;

namespace JKOPay.BatchSystem.Job.Models.Job;

public class TestJob(ILogger<TestJob> logger) : BaseJob(logger), IJob
{
  private readonly ILogger _logger = logger;

  public override async Task ExecuteAsync(CancellationToken cancellationToken)
  {
    var totalDataCount = 123;
    var processedData = 10;
    var updateResult = await base.SetTotalRecordAsync(totalDataCount, cancellationToken);
    var updateProcessedResult = await base.UpdateProcessedRecordAsync(processedData, cancellationToken);

    _logger.LogInformation(
      "TestJob executed successfully. Total Data Count: {TotalDataCount}, Processed Data: {ProcessedData}, Update Result: {UpdateResult}, Update Processed Result: {UpdateProcessedResult}",
      totalDataCount, processedData, updateResult, updateProcessedResult);
  }
}
