// <PERSON><PERSON><PERSON><PERSON> disable ConvertToPrimaryConstructor
// <PERSON><PERSON><PERSON>per disable once ClassWithVirtualMembersNeverInherited.Global

using JKOPay.Platform.BatchSystem.Models;
using JKOPay.BatchSystem.Core.Utilities;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Workflow = JKOPay.BatchSystem.Core.Models.Argo.Workflow.Workflow;

namespace JKOPay.BatchSystem.Job.Models.Job;

public class DeleteCanceledWorkflowJob : BaseJob, IJob
{
    private readonly ILogger<DeleteCanceledWorkflowJob> _logger;

    private readonly ArgoWorkflowUtility _argoUtility;

    private readonly BatchDbContext _dbContext;


    public DeleteCanceledWorkflowJob(ArgoWorkflowUtility argoWorkflow, BatchDbContext dbContext, ILogger<DeleteCanceledWorkflowJob> logger) : base(logger)
    {
        _argoUtility = argoWorkflow;
        _dbContext = dbContext;
        _logger = logger;
    }

    public override async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("In delete workflow execute.");
        var workflows = await _argoUtility.GetWorkflowsAsync();
        if (workflows.IsSuccess == false)
        {
            _logger.LogError(workflows.ErrorMessage);
            return;
        }

        foreach (var workflow in workflows.Data!)
        {
            _logger.LogInformation(
                "Workflow Id: {WorkflowId}, Name: {WorkflowName}, Status: {Status}, StartedAt: {StartedAt}, FinishedAt: {FinishedAt}",
                workflow.Metadata.Uid,
                workflow.Metadata.Name,
                workflow.Status.Phase,
                workflow.Status.StartedAt.ToUniversalTime(),
                workflow.Status.FinishedAt.ToUniversalTime());

            switch (workflow.Status.Phase)
            {
                case "Failed":
                    if (GetNow > workflow.Metadata.CreationTimestamp.AddMinutes(_argoUtility.GetArgoWorkflowOption().Workflow.ExpirationThreshold))
                    {
                        _logger.LogInformation("Deleted workflow: {WorkflowName}", workflow.Metadata.Name);
                        await _argoUtility.DeleteWorkflowAsync(workflow.Metadata.Namespace, workflow.Metadata.Name);
                    }

                    break;
            }
        }
    }


    /// <summary>
    /// Update schedule job information
    /// </summary>
    /// <param name="wf">Worfkow</param>
    protected virtual async Task UpdateScheduleJobAsync(Workflow wf)
    {
        var result = await _dbContext.ScheduleJobs
            .Where(p => p.ScheduleJobName == wf.Metadata.OwnerReferences!.First().Name)
            .ExecuteUpdateAsync(p =>
                p.SetProperty(item => item.LatestStatus, wf.Status.Phase)
                    .SetProperty(item => item.LatestStartedAt, wf.Status.StartedAt.ToUniversalTime())
                    .SetProperty(item => item.LatestFinishedAt, wf.Status.FinishedAt.ToUniversalTime())
                    .SetProperty(item => item.LatestWorkflowId, wf.Metadata.Uid)
                    .SetProperty(item => item.LatestWorkflowName, wf.Metadata.Name));
        if (result < 1)
        {
            _logger.LogWarning("Update cron job information failed:({ScheduleJobName})", wf.Metadata.OwnerReferences?.First().Name);
        }
    }

    /// <summary>
    /// Update workflow information
    /// </summary>
    /// <param name="wf">Workflow</param>
    /// <param name="log">Workflow log</param>
    protected virtual async Task UpdateWorkflowAsync(Workflow wf, string log)
    {
        var now = GetNow.DateTime;
        var workflowUpdateResult = await _dbContext.Workflows.Where(p => p.WorkflowId == wf.Metadata.Uid)
            .ExecuteUpdateAsync(
                p =>
                    p.SetProperty(item => item.Status, wf.Status.Phase)
                        .SetProperty(item => item.Log, log)
                        .SetProperty(item => item.StartedAt, wf.Status.StartedAt.ToUniversalTime())
                        .SetProperty(item => item.FinishedAt, wf.Status.FinishedAt.ToUniversalTime())
                        .SetProperty(item => item.UpdatedTime, now));

        if (workflowUpdateResult == 0)
        {
            _logger.LogWarning("Update workflow information failed ({WorkflowId})", wf.Metadata.Uid);
        }
    }

    protected virtual DateTimeOffset GetNow => DateTimeOffset.UtcNow;

}