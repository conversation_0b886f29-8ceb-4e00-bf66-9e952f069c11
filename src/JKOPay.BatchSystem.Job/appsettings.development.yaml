---
Logging:
  LogLevel:
    Default: Information
    Microsoft.AspNetCore: Warning
ConnectionStrings:
  MySql: Server=aprd-mysql.foundation.svc.cluster.local;Port=3306;Database=batchsystem;User ID=batchuser;Password=batchpw;
Serilog:
  Using:
  - Serilog.Sinks.Console
  - Serilog.Sinks.Async
  - Serilog.Exceptions
  MinimumLevel:
    Default: Information
    Override:
      Microsoft.AspNetCore: Warning
  WriteTo:
  - Name: Async
    Args:
      configure:
      - Name: Console
        Args:
          formatter:
            type: "Serilog.Formatting.Json.JsonFormatter, Serilog"
            renderMessage: true
  Enrich:
    - FromLogContext
    - WithExceptionDetails
OpenTelemetry:
  Exporter:
    OTLP:
      AgentHost: alloy.monitor
      AgentPort: 4317
      ServiceName: JKOPay.BatchSystem.Api
BatchSystemAPI:
  Domain: http://dev-foundation-jkopay-batchsystem-api-svc:9090/
ArgoWorkflow:
  Domain: https://argo-server.argo.svc.cluster.local:2746/
  Workflow:
    Submit: api/v1/workflows/argo/submit
    List: api/v1/workflows
    Log: api/v1/workflows/{namespace}/{jobName}/log
  WorkflowTemplate:
    List: api/v1/workflow-templates
  Sensor:
    List: api/v1/sensors
  Eventsource:
    List: api/v1/event-sources