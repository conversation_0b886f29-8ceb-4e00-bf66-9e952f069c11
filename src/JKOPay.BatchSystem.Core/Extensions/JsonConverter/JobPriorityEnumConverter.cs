using JKOPay.BatchSystem.Core.Models.Core;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JKOPay.BatchSystem.Core.Extensions.JsonConverter;
public class JobPriorityEnumConverter : Newtonsoft.Json.JsonConverter
{
    public override bool CanConvert(Type objectType)
    {
        return objectType == typeof(JobPriorityEnum);
    }

    public override object ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
    {
        var token = JToken.Load(reader);
        var value = token.ToString();

        if (Enum.TryParse(typeof(JobPriorityEnum), value, true, out var result))
        {
            return result;
        }

        throw new JsonSerializationException($"Unable to convert \"{value}\" to {typeof(JobPriorityEnum)}.");
    }

    public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        writer.WriteValue(value?.ToString());
    }
}