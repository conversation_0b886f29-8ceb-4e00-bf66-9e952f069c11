namespace JKOPay.BatchSystem.Core.Extensions;

public static class DateTimeExtension
{
    public static DateTime? ConvertToLocalTime(this DateTime? dateTime)
    {
        // use DateTimeOffset change the time zone to local time
        return dateTime.HasValue == false ? dateTime : new DateTimeOffset(dateTime.Value, TimeSpan.Zero).ToLocalTime().DateTime;
    }

    public static DateTime ConvertToLocalTime(this DateTime dateTime)
    {
        // use DateTimeOffset change the time zone to local time
        return new DateTimeOffset(dateTime, TimeSpan.Zero).ToLocalTime().DateTime;
    }
}