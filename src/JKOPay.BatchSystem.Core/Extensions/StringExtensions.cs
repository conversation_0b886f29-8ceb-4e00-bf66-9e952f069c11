using System.Text;

namespace JKOPay.BatchSystem.Core.Extensions;

public static class StringExtensions
{
    public static byte[] GetBytes(this string str)
    {
        return Encoding.UTF8.GetBytes(str);
    }

    public static string ToBase64String(this string plainText)
    {
        var plainTextBytes = Encoding.UTF8.GetBytes(plainText);
        return Convert.ToBase64String(plainTextBytes);
    }
}