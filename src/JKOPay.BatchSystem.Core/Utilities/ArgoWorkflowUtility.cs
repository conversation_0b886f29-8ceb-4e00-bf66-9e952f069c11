using System.Text;
using JKOPay.BatchSystem.Core.Models;
using JKOPay.BatchSystem.Core.Models.Argo.Cron;
using JKOPay.BatchSystem.Core.Models.Argo.Eventsource;
using JKOPay.BatchSystem.Core.Models.Argo.Sensor;
using JKOPay.BatchSystem.Core.Models.Argo.Workflow;
using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;
using JKOPay.BatchSystem.Core.Models.Option.ArgoWorkflow;
using JKOPay.Platform.BatchSystem.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Workflow = JKOPay.BatchSystem.Core.Models.Argo.Workflow.Workflow;

namespace JKOPay.BatchSystem.Core.Utilities;

// ReSharper disable once ClassWithVirtualMembersNeverInherited.Global
public class ArgoWorkflowUtility : IArgoWorkflowUtility
{
    private readonly HttpClient _httpClient;

    private readonly JsonNetSerializerUtility _jsonNetSerializerUtility;

    private readonly ArgoWorkflowOption _argoWorkflowOption;

    private readonly ILogger<ArgoWorkflowUtility> _logger;

    // ReSharper disable once ConvertToPrimaryConstructor
    public ArgoWorkflowUtility(IHttpClientFactory factory, JsonNetSerializerUtility jsonNetSerializerUtility, ArgoWorkflowOption argoWorkflowOption, ILogger<ArgoWorkflowUtility> logger)
    {
        _httpClient = factory.CreateClient("ArgoWorkflow");
        _jsonNetSerializerUtility = jsonNetSerializerUtility;
        _argoWorkflowOption = argoWorkflowOption;
        _logger = logger;
    }

    /// <summary>
    /// Get ArgoWorkflow setting
    /// </summary>
    /// <returns>ArgoWorkflowOption</returns>
    public ArgoWorkflowOption GetArgoWorkflowOption() => _argoWorkflowOption;

    /// <summary>
    /// Create workflow
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    public async Task<QueryResult<SubmitResponse>> SubmitWorkflowAsync(SubmitRequest request)
    {
        var payloadStr = JsonConvert.SerializeObject(request);
        var content = new StringContent(payloadStr, Encoding.UTF8, "application/json");

        var path = _argoWorkflowOption.Workflow.Submit;
        if (request.Namespace != "argo")
        {
            path = path.Replace("argo", request.Namespace);
        }

        var url = _argoWorkflowOption.Domain + path;
        try
        {
            var response = await _httpClient.PostAsync(url, content).ConfigureAwait(false);
            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<SubmitResponse>(await response.Content.ReadAsStringAsync());
                if (result == null) return new QueryResult<SubmitResponse>()
                {
                    IsSuccess = false,
                    ErrorMessage = "Response is null"
                };

                return new QueryResult<SubmitResponse>
                {
                    IsSuccess = true,
                    Data = result
                };
            }
            else
            {
                _logger.LogWarning("Url: {ArgoAPI}, Body: {Body}, StatusCode: {StatusCode}", url, payloadStr, response.StatusCode);
                var error = _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
                return new QueryResult<SubmitResponse>()
                {
                    IsSuccess = false,
                    ErrorMessage = error["message"]!.ToString(),
                    Code = error["code"]?.ToString()
                };
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return new QueryResult<SubmitResponse>
            {
                IsSuccess = false,
                ErrorMessage = e.Message,
            };
        }
    }

    public async Task<QueryResult<SubmitResponse>> SubmitCronWorkflowAsync(string @namespace, string cronWorkflowName)
    {
        var payload = new Dictionary<string, object>
        {
            {"namespace", @namespace},
            {"resourceName", cronWorkflowName},
            {"resourceKind", "cronwf"},
        };

        var payloadStr = JsonConvert.SerializeObject(payload);
        var content = new StringContent(payloadStr, Encoding.UTF8, "application/json");
        var path = _argoWorkflowOption.CronWorkflow.Submit;
        var url = _argoWorkflowOption.Domain + path;
        url = url.Replace("{namespace}", @namespace);
        try
        {
            var response = await _httpClient.PostAsync(url, content).ConfigureAwait(false);
            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<SubmitResponse>(await response.Content.ReadAsStringAsync());
                if (result == null) return new QueryResult<SubmitResponse>()
                {
                    IsSuccess = false,
                    ErrorMessage = "Response is null"
                };
                return new QueryResult<SubmitResponse>
                {
                    IsSuccess = true,
                    Data = result
                };
            }
            else
            {
                _logger.LogWarning("Url: {ArgoAPI}, Body: {Body}, StatusCode: {StatusCode}", url, payloadStr, response.StatusCode);
                var error = _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
                return new QueryResult<SubmitResponse>()
                {
                    IsSuccess = false,
                    ErrorMessage = error["message"]!.ToString(),
                    Code = error["code"]?.ToString()
                };
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return new QueryResult<SubmitResponse>
            {
                IsSuccess = false,
                ErrorMessage = e.Message
            };
        }

    }

    public async Task ResubmitWorkflowAsync(string @namespace, string workflowName)
    {
        var url = _argoWorkflowOption.Domain + _argoWorkflowOption.Workflow.Resubmit;
        url = url.Replace("{namespace}", @namespace).Replace("{workflowName}", workflowName);

        var content = new StringContent("{\"parameters\":[],\"memoized\":false}", Encoding.UTF8, "application/json");
        var response = await _httpClient.PutAsync(url, content).ConfigureAwait(false);
        if (response.IsSuccessStatusCode)
        {
        }
        else
        {
            var error =
                _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
            _logger.LogError(
                "Failed to resubmit workflow {WorkflowName} in namespace {Namespace}: {ErrorMessage}",
                workflowName,
                @namespace,
                error["message"]!.ToString());
        }
    }

    /// <summary>
    /// Get workflow resource
    /// </summary>
    /// <returns></returns>
    public async Task<QueryResult<List<Workflow>>> GetWorkflowsAsync()
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.Workflow.List}/";

        var response = await GetWorkflowsAsync(url);
        return response;
    }

    /// <summary>
    /// Get workflow resource
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <param name="labelSelector"></param>
    /// <returns></returns>
    public async Task<QueryResult<List<Workflow>>> GetWorkflowsAsync(string @namespace, string labelSelector)
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.Workflow.List}/{@namespace}";
        if (labelSelector.Length > 0)
        {
            url = $"{url}?listOptions.labelSelector={labelSelector}";
        }

        var response = await GetWorkflowsAsync(url);
        return response;
    }

    /// <summary>
    /// Get workflow resource
    /// </summary>
    /// <param name="url">api url</param>
    /// <returns></returns>
    protected virtual async Task<QueryResult<List<Workflow>>> GetWorkflowsAsync(string url)
    {
        var response = await _httpClient.GetAsync(url).ConfigureAwait(false);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var workflows = _jsonNetSerializerUtility.DeserializeFormStream<WorkflowListResponse>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<List<Workflow>>
            {
                IsSuccess = true,
                Data = workflows.Items ?? [],
            };
        }
        else
        {
            var error =
                _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<List<Workflow>>
            {
                IsSuccess = false,
                ErrorMessage = error["message"]!.ToString(),
                Code = error["code"]?.ToString()
            };
        }
    }

    /// <summary>
    /// Get workflow's log
    /// </summary>
    /// <param name="namespace">Workflow's namespace</param>
    /// <param name="jobName">Workflow name</param>
    /// <returns></returns>
    public async Task<QueryResult<List<WorkflowLogResponse>>> GetWorkflowLogAsync(string @namespace, string jobName)
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.Workflow.Log}?logOptions.container=main&grep=&logOptions.follow=true&podName={jobName}";
        url = url.Replace("{namespace}", @namespace);
        url = url.Replace("{jobName}", jobName);

        var response = await _httpClient.GetAsync(url).ConfigureAwait(false);
        if (response.IsSuccessStatusCode)
        {
            var oriLog = await response.Content.ReadAsStringAsync();
            if (oriLog.Length <= 0)
            {
                return new QueryResult<List<WorkflowLogResponse>>
                {
                    IsSuccess = true,
                    Data = [],
                };
            }

            // 需要移除字尾的 ','
            var logStr = $"[{oriLog.Replace("\n", ",")[..^1]}]";
            if (_jsonNetSerializerUtility.IsValidJson(logStr) == false)
            {
                return new QueryResult<List<WorkflowLogResponse>>
                {
                    IsSuccess = true,
                    Data = [],
                };
            }

            var logs = JsonConvert.DeserializeObject<List<WorkflowLogResponse>>(logStr);
            return new QueryResult<List<WorkflowLogResponse>>
            {
                IsSuccess = true,
                Data = logs,
            };
        }
        else
        {
            var error =
                _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<List<WorkflowLogResponse>>
            {
                IsSuccess = false,
                ErrorMessage = error["message"]!.ToString(),
                Code = error["code"]?.ToString()
            };
        }
    }

    public async Task<QueryResult<List<CronWorkflow>>> GetCronWorkflowsAsync(string @namespace)
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.CronWorkflow.List}";
        url = url.Replace("{namespace}", @namespace);
        var response = await _httpClient.GetAsync(url).ConfigureAwait(false);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var workflows = _jsonNetSerializerUtility.DeserializeFormStream<CronWorkflowListResponse>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<List<CronWorkflow>>
            {
                IsSuccess = true,
                Data = workflows.Items ?? [],
            };
        }
        else
        {
            var error =
                _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<List<CronWorkflow>>
            {
                IsSuccess = false,
                ErrorMessage = error["message"]!.ToString(),
                Code = error["code"]?.ToString()
            };
        }
    }

    /// <summary>
    /// Stop special workflow
    /// </summary>
    /// <param name="namespace">Workflow's namespace</param>
    /// <param name="jobName">Workflow name</param>
    /// <returns></returns>
    public async Task<QueryResult<object>> StopWorkflowAsync(string @namespace, string jobName)
    {
        return await ExecuteWorkflowActionAsync(
            @namespace,
            jobName,
            _argoWorkflowOption.Workflow.Stop,
            async (url, content) => await _httpClient.PutAsync(url, content).ConfigureAwait(false));
    }

    /// <summary>
    /// Terminate workflow
    /// </summary>
    /// <param name="namespace">Workflow's namespace</param>
    /// <param name="jobName">Workflow name</param>
    /// <returns></returns>
    public async Task<QueryResult<object>> TerminateWorkflowAsync(string @namespace, string jobName)
    {
        return await ExecuteWorkflowActionAsync(
            @namespace,
            jobName,
            _argoWorkflowOption.Workflow.Terminate,
            async (url, content) => await _httpClient.PutAsync(url, content).ConfigureAwait(false));
    }

    /// <summary>
    /// Delete workflow
    /// </summary>
    /// <param name="namespace">Workflow's namespace</param>
    /// <param name="jobName">Workflow name</param>
    /// <returns></returns>
    public async Task<QueryResult<object>> DeleteWorkflowAsync(string @namespace, string jobName)
    {
        return await ExecuteWorkflowActionAsync(
            @namespace,
            jobName,
            _argoWorkflowOption.Workflow.Delete,
            async (url, _) => await _httpClient.DeleteAsync(url).ConfigureAwait(false));
    }

    /// <summary>
    /// Execute workflow action
    /// </summary>
    /// <param name="namespace">Workflow's namespace</param>
    /// <param name="jobName">Workflow name</param>
    /// <param name="actionUrl">Argo workflow rest api url</param>
    /// <param name="httpMethod">Http method</param>
    /// <returns></returns>
    private async Task<QueryResult<object>> ExecuteWorkflowActionAsync(string @namespace, string jobName, string actionUrl, Func<string, HttpContent, Task<HttpResponseMessage>> httpMethod)
    {
        var url = $"{_argoWorkflowOption.Domain}{actionUrl}";
        url = url.Replace("{namespace}", @namespace);
        url = url.Replace("{name}", jobName);

        var body = new Dictionary<string, string>
        {
            { "name", jobName },
            { "namespace", @namespace }
        };
        var content = new StringContent(JsonConvert.SerializeObject(body), Encoding.UTF8, "application/json");
        var response = await httpMethod(url, content);

        if (response.IsSuccessStatusCode)
        {
            return new QueryResult<object>
            {
                IsSuccess = true,
            };
        }
        else
        {
            var error = _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<object>
            {
                IsSuccess = false,
                ErrorMessage = error["message"]!.ToString(),
                Code = error["code"]?.ToString()
            };
        }
    }

    /// <summary>
    /// Get WorkflowTemplate resource
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <param name="labelSelector"></param>
    /// <returns></returns>
    public async Task<QueryResult<List<WorkflowTemplate>>> GetWorkflowTemplatesAsync(string @namespace, string labelSelector)
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.WorkflowTemplate.List}/{@namespace}";
        if (labelSelector.Length > 0)
        {
            url = $"{url}?listOptions.labelSelector={labelSelector}";
        }

        var response = await _httpClient.GetAsync(url).ConfigureAwait(false);
        if (response.IsSuccessStatusCode)
        {
            try
            {
                var workflowTemplates = _jsonNetSerializerUtility.DeserializeFormStream<WorkflowTemplateListResponse>(await response.Content.ReadAsStreamAsync());

                // 檢查並移除不包含 Label 的 WorkflowTemplate
                for (var index = 0; index < workflowTemplates.Items!.Count; index++)
                {
                    if (workflowTemplates.Items[index].Metadata.Labels == null)
                    {
                        _logger.LogWarning("WorkflowTemplate {Name} has no labels", workflowTemplates.Items[index].Metadata.Name);

                    }

                    workflowTemplates.Items.RemoveAt(index);
                }

                return new QueryResult<List<WorkflowTemplate>>
                {
                    IsSuccess = true,
                    Data = workflowTemplates.Items == null ? [] : workflowTemplates.Items.Where(p =>
                        p.Metadata.Labels!.ContainsKey("batchsystem.owner") == false ||
                        p.Metadata.Labels["batchsystem.owner"] != "batchsystem").ToList(),
                };
            }
            catch (Exception e)
            {
                _logger.LogError(e, e.Message);
                return new QueryResult<List<WorkflowTemplate>>
                {
                    IsSuccess = false,
                    Data = [],
                    ErrorMessage = e.Message,
                };
            }
        }
        else
        {
            var error =
                _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<List<WorkflowTemplate>>
            {
                IsSuccess = false,
                ErrorMessage = error["message"]!.ToString(),
                Code = error["code"]?.ToString()
            };
        }
    }

    public async Task<QueryResult<WorkflowTemplate>> GetWorkflowTemplateAsync(string @namespace, string templateName)
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.WorkflowTemplate.Get}";
        url = url.Replace("{namespace}", @namespace);
        url = url.Replace("{name}", templateName);

        var response = await _httpClient.GetAsync(url).ConfigureAwait(false);
        if (response.IsSuccessStatusCode)
        {
            var workflowTemplate = _jsonNetSerializerUtility.DeserializeFormStream<WorkflowTemplate>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<WorkflowTemplate>
            {
                IsSuccess = true,
                Data = workflowTemplate,
            };
        }
        else
        {
            var error =
                _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<WorkflowTemplate>
            {
                IsSuccess = false,
                ErrorMessage = error["message"]!.ToString(),
                Code = error["code"]?.ToString()
            };
        }
    }

    /// <summary>
    /// Get Sensor resource
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <returns></returns>
    public async Task<QueryResult<List<Sensor>>> GetSensors(string @namespace)
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.Sensor.List}/{@namespace}";
        var response = await _httpClient.GetAsync(url).ConfigureAwait(false);
        if (response.IsSuccessStatusCode)
        {
            var sensors = _jsonNetSerializerUtility.DeserializeFormStream<SensorListResponse>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<List<Sensor>>
            {
                IsSuccess = true,
                Data = sensors.Items ?? [],
            };
        }
        else
        {
            var error =
                _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<List<Sensor>>
            {
                IsSuccess = false,
                ErrorMessage = error["message"]!.ToString(),
                Code = error["code"]?.ToString()
            };
        }
    }

    /// <summary>
    /// Get event source
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <returns></returns>
    public async Task<QueryResult<List<Eventsource>>> GetEventsource(string @namespace)
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.Eventsource.List}/{@namespace}";
        var response = await _httpClient.GetAsync(url).ConfigureAwait(false);
        if (response.IsSuccessStatusCode)
        {
            var workflows = _jsonNetSerializerUtility.DeserializeFormStream<EventsourceListResponse>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<List<Eventsource>>
            {
                IsSuccess = true,
                Data = workflows.Items ?? [],
            };
        }
        else
        {
            var error =
                _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<List<Eventsource>>
            {
                IsSuccess = false,
                ErrorMessage = await response.Content.ReadAsStringAsync(),
                Code = error["code"]?.ToString()
            };
        }
    }

    /// <summary>
    /// Suspends a cron workflow
    /// </summary>
    /// <param name="namespace"></param>
    /// <param name="cronWorkflowName"></param>
    /// <returns></returns>
    public async Task<QueryResult<object>> SuspendCronWorkflow(string @namespace, string cronWorkflowName)
    {
        return await ExecuteWorkflowActionAsync(
            @namespace,
            cronWorkflowName,
            _argoWorkflowOption.CronWorkflow.Suspend,
            async (url, content) => await _httpClient.PutAsync(url, content).ConfigureAwait(false));
    }

    /// <summary>
    /// Resume CronWorkflow
    /// </summary>
    /// <param name="namespace"></param>
    /// <param name="cronWorkflowName"></param>
    /// <returns></returns>
    public async Task<QueryResult<object>> ResumeCronWorkflow(string @namespace, string cronWorkflowName)
    {
        return await ExecuteWorkflowActionAsync(
            @namespace,
            cronWorkflowName,
            _argoWorkflowOption.CronWorkflow.Resume,
            async (url, content) => await _httpClient.PutAsync(url, content).ConfigureAwait(false));
    }

    /// <summary>
    /// Delete WorkflowTemplate
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <param name="templateName">WorkflowTemplate name</param>
    /// <returns></returns>
    public async Task<QueryResult<object>> DeleteWorkflowTemplate(string @namespace, string templateName)
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.WorkflowTemplate.Delete}/{@namespace}/{templateName}";
        var result = await DeleteResource<object>(url);
        return result;
    }

    /// <summary>
    /// Delete CronWorkflow
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <param name="cronWorkflowName">CronWorkflow name</param>
    /// <returns></returns>
    public async Task<QueryResult<object>> DeleteCronWorkflow(string @namespace, string cronWorkflowName)
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.CronWorkflow.Delete}/{@namespace}/{cronWorkflowName}";
        var result = await DeleteResource<object>(url);
        return result;
    }

    /// <summary>
    /// Delete event source
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <param name="eventsourceName">eventsource name</param>
    /// <returns></returns>
    public async Task<QueryResult<object>> DeleteEventsource(string @namespace, string eventsourceName)
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.Eventsource.Delete}/{@namespace}/{eventsourceName}";
        var result = await DeleteResource<object>(url);
        return result;
    }

    /// <summary>
    /// Delete sensor
    /// </summary>
    /// <param name="namespace">resource'a namespace</param>
    /// <param name="sensorName">sensor name</param>
    /// <returns></returns>
    public async Task<QueryResult<object>> DeleteSensor(string @namespace, string sensorName)
    {
        var url = $"{_argoWorkflowOption.Domain}{_argoWorkflowOption.Sensor.Delete}/{@namespace}/{sensorName}";
        var result = await DeleteResource<object>(url);
        return result;
    }

    /// <summary>
    /// Common delete resource method
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    protected virtual async Task<QueryResult<object>> DeleteResource<T>(string url) where T : class
    {
        var response = await _httpClient.DeleteAsync(url).ConfigureAwait(false);
        if (response.IsSuccessStatusCode)
        {
            var workflows = _jsonNetSerializerUtility.DeserializeFormStream<T>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<object>
            {
                IsSuccess = true,
            };
        }
        else
        {
            var error =
                _jsonNetSerializerUtility.DeserializeFormStream<JObject>(await response.Content.ReadAsStreamAsync());
            return new QueryResult<object>
            {
                IsSuccess = false,
                ErrorMessage = error["message"]?.ToString(),
                Code = error["code"]?.ToString()
            };
        }
    }
}
