using JKOPay.BatchSystem.Core.Models;
using JKOPay.BatchSystem.Core.Models.Argo.Cron;
using JKOPay.BatchSystem.Core.Models.Argo.Eventsource;
using JKOPay.BatchSystem.Core.Models.Argo.Sensor;
using JKOPay.BatchSystem.Core.Models.Argo.Workflow;
using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;
using JKOPay.BatchSystem.Core.Models.Option.ArgoWorkflow;

namespace JKOPay.BatchSystem.Core.Utilities;

public interface IArgoWorkflowUtility
{
    /// <summary>
    /// Get ArgoWorkflow setting
    /// </summary>
    /// <returns>ArgoWorkflowOption</returns>
    ArgoWorkflowOption GetArgoWorkflowOption();

    /// <summary>
    /// Create workflow
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    Task<QueryResult<SubmitResponse>> SubmitWorkflowAsync(SubmitRequest request);

    /// <summary>
    /// Execute cron workflow
    /// </summary>
    /// <param name="namespace">namespace</param>
    /// <param name="cronWorkflowName">cronworkflow name</param>
    /// <returns></returns>
    Task<QueryResult<SubmitResponse>> SubmitCronWorkflowAsync(string @namespace, string cronWorkflowName);

    Task ResubmitWorkflowAsync(string @namespace, string workflowName);

    /// <summary>
    /// Get workflow resource
    /// </summary>
    /// <returns></returns>
    Task<QueryResult<List<Workflow>>> GetWorkflowsAsync();

    /// <summary>
    /// Get workflow resource
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <param name="labelSelector"></param>
    /// <returns></returns>
    Task<QueryResult<List<Workflow>>> GetWorkflowsAsync(string @namespace, string labelSelector);

    /// <summary>
    /// Get cron workflow resource
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <returns></returns>
    Task<QueryResult<List<CronWorkflow>>> GetCronWorkflowsAsync(string @namespace);

    /// <summary>
    /// Get workflow's log
    /// </summary>
    /// <param name="namespace">Workflow's namespace</param>
    /// <param name="jobName">Workflow name</param>
    /// <returns></returns>
    Task<QueryResult<List<WorkflowLogResponse>>> GetWorkflowLogAsync(string @namespace, string jobName);

    /// <summary>
    /// Stop special workflow
    /// </summary>
    /// <param name="namespace">Workflow's namespace</param>
    /// <param name="jobName">Workflow name</param>
    /// <returns></returns>
    Task<QueryResult<object>> StopWorkflowAsync(string @namespace, string jobName);

    /// <summary>
    /// Terminate workflow
    /// </summary>
    /// <param name="namespace">Workflow's namespace</param>
    /// <param name="jobName">Workflow name</param>
    /// <returns></returns>
    Task<QueryResult<object>> TerminateWorkflowAsync(string @namespace, string jobName);

    /// <summary>
    /// Delete workflow
    /// </summary>
    /// <param name="namespace">Workflow's namespace</param>
    /// <param name="jobName">Workflow name</param>
    /// <returns></returns>
    Task<QueryResult<object>> DeleteWorkflowAsync(string @namespace, string jobName);

    /// <summary>
    /// Get WorkflowTemplate resource
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <param name="labelSelector"></param>
    /// <returns></returns>
    Task<QueryResult<List<WorkflowTemplate>>> GetWorkflowTemplatesAsync(string @namespace, string labelSelector);

    /// <summary>
    /// // Get WorkflowTemplate resource
    /// </summary>
    /// <param name="namespace">Namespace</param>
    /// <param name="templateName">WorkflowTemplate name</param>
    /// <returns></returns>
    Task<QueryResult<WorkflowTemplate>> GetWorkflowTemplateAsync(string @namespace, string templateName);

    /// <summary>
    /// Get Sensor resource
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <returns></returns>
    Task<QueryResult<List<Sensor>>> GetSensors(string @namespace);

    /// <summary>
    /// Get event source
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <returns></returns>
    Task<QueryResult<List<Eventsource>>> GetEventsource(string @namespace);

    /// <summary>
    /// Suspend CronWorkflow
    /// </summary>
    /// <param name="namespace"></param>
    /// <param name="cronWorkflowName"></param>
    /// <returns></returns>
    Task<QueryResult<object>> SuspendCronWorkflow(string @namespace, string cronWorkflowName);

    /// <summary>
    /// Resume CronWorkflow
    /// </summary>
    /// <param name="namespace"></param>
    /// <param name="cronWorkflowName"></param>
    /// <returns></returns>
    Task<QueryResult<object>> ResumeCronWorkflow(string @namespace, string cronWorkflowName);

    /// <summary>
    /// Delete WorkflowTemplate
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <param name="templateName">WorkflowTemplate name</param>
    /// <returns></returns>
    Task<QueryResult<object>> DeleteWorkflowTemplate(string @namespace, string templateName);

    /// <summary>
    /// Delete CronWorkflow
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <param name="cronWorkflowName">CronWorkflow name</param>
    /// <returns></returns>
    Task<QueryResult<object>> DeleteCronWorkflow(string @namespace, string cronWorkflowName);

    /// <summary>
    /// Delete event source
    /// </summary>
    /// <param name="namespace">resource's namespace</param>
    /// <param name="eventsourceName">eventsource name</param>
    /// <returns></returns>
    Task<QueryResult<object>> DeleteEventsource(string @namespace, string eventsourceName);

    /// <summary>
    /// Delete sensor
    /// </summary>
    /// <param name="namespace">resource'a namespace</param>
    /// <param name="sensorName">sensor name</param>
    /// <returns></returns>
    Task<QueryResult<object>> DeleteSensor(string @namespace, string sensorName);
}