using Confluent.Kafka;
using Microsoft.Extensions.Logging;

namespace JKOPay.BatchSystem.Core.Utilities;

public class KafkaUtility(IProducer<string, string> producer, ILogger<KafkaUtility> logger) : IKafkaUtility
{
    public virtual async Task<bool> SendToKafkaAsync(Headers headers, string value, string topicName)
    {
        var deliveryResult = await producer.ProduceAsync(topicName, new Message<string, string> { Headers = headers, Value = value, Key = GetGuid().ToString()});
        if (deliveryResult.Status == PersistenceStatus.Persisted)
        {
            logger.LogInformation("Message delivered to {TopicPartitionOffset}", deliveryResult.TopicPartitionOffset);
            return true;
        }

        logger.LogError("Message delivery failed: {Status}", deliveryResult.Status);
        return false;
    }

    protected virtual Guid GetGuid()
    {
        return Guid.NewGuid();
    }
}