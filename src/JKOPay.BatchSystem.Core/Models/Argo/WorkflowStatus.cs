using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;
using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Argo;

public class WorkflowStatus
{
    [JsonProperty("phase")]
    public string Phase { get; set; } = null!;

    [JsonProperty("startedAt")]
    public DateTime StartedAt { get; set; }

    [JsonProperty("finishedAt")]
    public DateTime FinishedAt { get; set; }

    [JsonProperty("progress")]
    public string Progress { get; set; } = null!;

    [JsonProperty("message")]
    public string? Message { get; set; }

    [JsonProperty("storedWorkflowTemplateSpec")]
    public StoredWorkflowTemplateSpec StoredWorkflowTemplateSpec { get; set; } = null!;
}