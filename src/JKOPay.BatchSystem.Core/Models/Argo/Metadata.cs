using JKOPay.BatchSystem.Core.Models.Argo.Workflow;
using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Argo;

public class Metadata
{
    [JsonProperty("name")]
    public string Name { get; set; } = null!;

    [JsonProperty("namespace")]
    public string Namespace { get; set; } = null!;

    [JsonProperty("uid")]
    public string Uid { get; set; } = null!;

    [JsonProperty("labels")]
    public Dictionary<string, string>? Labels { get; set; }

    [JsonProperty("ownerReferences")]
    public List<WorkflowOwnerReferences>? OwnerReferences { get; set; }

    [JsonProperty("creationTimestamp")]
    public DateTimeOffset CreationTimestamp { get; set; }
}