using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;

public class StoredWorkflowTemplateSpec
{
    [JsonProperty("templates")]
    public List<Template> Templates { get; set; } = null!;

    [JsonProperty("entrypoint")]
    public string Entrypoint { get; set; } = null!;

    [JsonProperty("arguments")]
    public WorkflowArgs? Arguments { get; set; }

    [JsonProperty("parallelism")]
    public int Parallelism { get; set; }

    [JsonProperty("ttlStrategy")]
    public Dictionary<string, string> TtlStrategy { get; set; } = null!;

    [JsonProperty("workflowTemplateRef")]
    public Dictionary<string, string> WorkflowTemplateRef { get; set; } = null!;
}