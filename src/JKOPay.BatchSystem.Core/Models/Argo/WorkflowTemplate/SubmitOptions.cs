using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;

public class SubmitOptions
{
    [JsonProperty("entryPoint")]
    public string EntryPoint { get; set; } = null!;

    [JsonProperty("parameters")]
    public List<string> Parameters { get; set; } = null!;

    [JsonProperty("labels")]
    public string Labels { get; set; } = null!;

    [JsonProperty("name")]
    public string Name { get; set; } = null!;

    [JsonProperty("serviceAccount")]
    public string ServiceAccount { get; set; } = null!;
}