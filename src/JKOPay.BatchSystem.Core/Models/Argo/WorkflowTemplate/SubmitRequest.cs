using System.Text;
using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;

public class SubmitRequest
{
    public SubmitRequest(string @namespace, string jobName, string workflowTemplateName, string entryPoint, Dictionary<string, object> parameters, Dictionary<string, string> labels)
    {
        this.Namespace = @namespace;
        this.ResourceName = workflowTemplateName;
        var labelStr = new StringBuilder();
        foreach (var item in labels)
        {
            labelStr.Append($"{item.Key}={item.Value},");
        }

        var name = NameInit();
        this.SubmitOptions = new SubmitOptions
        {
            EntryPoint = entryPoint,
            Parameters = parameters.Select(item =>
            {
                if (item.Key == "jobId")
                {
                    var guid = Guid.Parse(item.Value.ToString()!).ToString("N");
                    name = $"{jobName}-workflow-{guid}".ToLower();
                }

                if (item.Key.Equals("jobData", StringComparison.CurrentCultureIgnoreCase))
                {
                    var bytes = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(item.Value));
                    var value = Convert.ToBase64String(bytes);
                    return $"{item.Key}={value}";
                }
                else
                {
                    return $"{item.Key}={item.Value}";
                }
            }).ToList(),

            // 移除字串尾巴的 ,
            Labels = labelStr.ToString()[..(labelStr.ToString().Length - 1)],
            Name = name,
            ServiceAccount = $"{Namespace}-batchsystem-sa",
        };
    }

    [JsonProperty("namespace")]
    public string Namespace { get; set; }

    [JsonProperty("resourceKind")]
    public string ResourceKind { get;} = "WorkflowTemplate";

    [JsonProperty("resourceName")]
    public string ResourceName { get; set; }

    [JsonProperty("submitOptions")]
    public SubmitOptions SubmitOptions { get; set; }

    protected virtual Guid GetGuid()
    {
        return Guid.NewGuid();
    }

    private string NameInit()
    {
        return  $"general-workflow-{GetGuid().ToString().ToLower()}";
    }
}