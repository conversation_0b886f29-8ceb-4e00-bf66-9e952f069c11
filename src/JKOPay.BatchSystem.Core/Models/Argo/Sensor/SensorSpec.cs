using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JKOPay.BatchSystem.Core.Models.Argo.Sensor;

public class SensorSpec
{
    [JsonProperty("dependencies")]
    public List<Dependency>? Dependencies { get; set; }

    [JsonProperty("triggers")]
    public List<JObject>? Triggers { get; set; }

    [JsonProperty("template")]
    public SensorTemplate Template { get; set; } = null!;

    [JsonProperty("eventBusName")]
    public string EventBusName { get; set; } = null!;
}