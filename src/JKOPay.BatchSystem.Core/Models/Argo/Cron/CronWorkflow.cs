using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Argo.Cron;
public class CronWorkflow
{
    [JsonProperty("apiVersion")]
    public string ApiVersion { get; set; } = null!;

    [JsonProperty("kind")]
    public string Kind { get; set; } = null!;

    [JsonProperty("metadata")]
    public Metadata Metadata { get; set; } = null!;

    [JsonProperty("spec")]
    public Spec Spec { get; set; } = null!;
}