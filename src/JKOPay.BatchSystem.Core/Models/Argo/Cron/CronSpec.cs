using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;
using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Argo.Cron;

public class Spec
{
    [JsonProperty("arguments")]
    public WorkflowArgs? Arguments { get; set; }

    [JsonProperty("schedule")]
    public string Schedule { get; set; } = null!;

    [JsonProperty("workflowSpec")]
    public WorkflowSpec? WorkflowSpec { get; set; }

    [JsonProperty("suspend")]
    public bool? Suspend { get; set; } = false;
}
