using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Argo;

public class Template
{
    [JsonProperty("name")]
    public string Name { get; set; } = null!;

    [JsonProperty("inputs")]
    public Inputs? Inputs { get; set; }

    [JsonProperty("container")]
    public Container Container { get; set; } = null!;

    [JsonProperty("retryStrategy")]
    public object? RetryStrategy { get; set; }
}