using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;
using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Argo;

public class WorkflowSpec
{
    [JsonProperty("arguments")]
    public WorkflowArgs Arguments { get; set; } = null!;

    [JsonProperty("workflowTemplateRef")]
    public Dictionary<string, string>? WorkflowTemplateRef { get; set; }

    [JsonProperty("shutdown")]
    public string? Shutdown { get; set; }

    [JsonProperty("templates")]
    public List<Template> Templates { get; set; } = [];

    [JsonProperty("serviceAccountName")]
    public string ServiceAccountName { get; set; } = null!;
}