// ReSharper disable All

using JKOPay.BatchSystem.Core.Extensions.JsonConverter;
using JKOPay.BatchSystem.Core.Models.Core;
using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Api;

public class UpdateJob
{
    [JsonProperty("jobId")]
    public string JobId { get; set; } = null!;

    [JsonProperty("workflowId")]
    public string WorkflowId { get; set; } = null!;

    [JsonProperty("status")]
    public string Status { get; set; } = null!;

    [JsonProperty("workflowName")]
    public string WorkflowName { get; set; } = null!;

    [<PERSON><PERSON><PERSON>onverter(typeof(JobPriorityEnumConverter))]
    public JobPriorityEnum Priority { get; set; }
}