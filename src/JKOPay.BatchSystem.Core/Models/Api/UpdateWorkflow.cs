using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Api;

public class UpdateWorkflow
{
    [JsonProperty("workflowName")]
    public string WorkflowName { get; set; } = null!;

    [JsonProperty("workflowId")]
    public Guid WorkflowId { get; set; }

    [JsonProperty("startedAt")]
    public DateTime StartedAt { get; set; }

    [JsonProperty("finishedAt")]
    public DateTime FinishedAt { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; } = null!;
}