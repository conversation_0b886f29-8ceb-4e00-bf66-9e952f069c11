using JKOPay.BatchSystem.Core.Extensions.JsonConverter;
using JKOPay.BatchSystem.Core.Models.Core;
using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Core.Models.Api;

public class CreateScheduleJob
{
    [JsonProperty("jobName")] public string JobName { get; set; } = null!;

    [JsonProperty("workflowId")]
    public string WorkflowId { get; set; } = null!;

    [JsonProperty("status")]
    public string Status { get; set; } = null!;

    [JsonProperty("workflowName")]
    public string WorkflowName { get; set; } = null!;

    [Json<PERSON>onverter(typeof(JobPriorityEnumConverter))]
    public JobPriorityEnum Priority { get; set; }

    [Json<PERSON>roperty("workflowTemplateName")]
    public string WorkflowTemplateName { get; set; } = null!;

    [Json<PERSON>roperty("team")]
    public string Team { get; set; } = null!;
}