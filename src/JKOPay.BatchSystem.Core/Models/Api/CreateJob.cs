using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using JKOPay.BatchSystem.Core.Extensions.JsonConverter;
using JKOPay.BatchSystem.Core.Models.Core;
using Newtonsoft.Json;

// ReSharper disable UnusedAutoPropertyAccessor.Global
namespace JKOPay.BatchSystem.Core.Models.Api;

/// <summary>
/// Job information
/// <remarks>Test</remarks>
/// </summary>
public class CreateJob
{
    /// <summary>
    /// Option, if trigger type is kafka, pls write
    /// </summary>
    /// <example>[JobPriorityEnum.Immediate, JobPriorityEnum.Priority, JobPriorityEnum.Normal]</example>
    [Required]
    [JsonConverter(typeof(JobPriorityEnumConverter))]
    [JsonProperty("priority")]
    public JobPriorityEnum Priority { get; set; }

    /// <summary>
    /// Job name
    /// </summary>
    [Required]
    [StringLength(40, ErrorMessage = "JobName length can't be more than 40.")]
    [JsonProperty("jobName")]
    public string JobName { get; set; } = null!;

    /// <summary>
    /// Argo workflow name
    /// </summary>
    [Required]
    [JsonProperty("workflowTemplateName")]
    public string WorkflowTemplateName { get; set; } = null!;

    // /// <summary>
    // /// Kafka header
    // /// </summary>
    // public Dictionary<string, string>? KafkaHeader { get; set; }

    /// <summary>
    /// Job need data
    /// <example>"Message": { "jobData" : {"Parameter":{},"JobName":"updatestatusjob"} }</example> /// </summary>
    [JsonProperty("message")]
    public Dictionary<string, object> Message { get; set; } = null!;

    /// <summary>
    /// Which module does the request come from
    /// </summary>
    [Required]
    [JsonProperty("serviceNamespace")]
    public string ServiceNamespace { get; set; } = null!;
}