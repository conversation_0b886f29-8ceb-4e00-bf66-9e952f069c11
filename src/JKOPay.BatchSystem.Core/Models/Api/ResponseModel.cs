// ReSharper disable StaticMemberInGenericType
using Newtonsoft.Json;
using System.Text.RegularExpressions;

namespace JKOPay.BatchSystem.Core.Models.Api;

public class ResponseModel<TResult> where TResult : class
{
    // 靜態快取 (保持不變)
    private static readonly Dictionary<string, ResultCode> CodeStringMap;
    private static readonly Dictionary<ResultCode, ResultCodeAttribute> ResultCodeAttributeMap;

    // 靜態構造函數初始化快取 (保持不變)
    static ResponseModel()
    {
        // 已有的初始化代碼...
        ResultCodeAttributeMap = new Dictionary<ResultCode, ResultCodeAttribute>();
        CodeStringMap = new Dictionary<string, ResultCode>();

        // 獲取所有枚舉值
        var enumValues = Enum.GetValues(typeof(ResultCode));

        // 遍歷每個枚舉值獲取其 Attribute
        for (var i = 0; i < enumValues.Length; i++)
        {
            var code = (ResultCode)enumValues.GetValue(i)!;
            var field = typeof(ResultCode).GetField(code.ToString());

            if (field == null) continue;
            var attribute = (ResultCodeAttribute)Attribute.GetCustomAttribute(field, typeof(ResultCodeAttribute))!;

            {
                // 保存枚舉值到屬性的映射
                ResultCodeAttributeMap[code] = attribute;

                // 保存代碼字符串到枚舉值的映射，處理重複代碼的情況
                var codeString = attribute.CodeString;
                if (CodeStringMap.TryGetValue(codeString, out var value) && (int)code >= (int)value) continue;

                value = code;
                CodeStringMap[codeString] = value;
            }
        }
    }

    private ResultCode _resultCode = ResultCode.Success;
    private string _message = string.Empty;
    private readonly string _originalTemplate = string.Empty;
    // private object[] _messageParams = Array.Empty<object>();

    /// <summary>
    /// Result code
    /// </summary>
    [JsonProperty(nameof(Result))]
    public string Result
    {
        get => GetResultCodeString(_resultCode);
        set => _resultCode = ParseResultCode(value);
    }

    /// <summary>
    /// Internal ResultCode enum
    /// </summary>
    [JsonIgnore]
    public ResultCode ResultCode
    {
        get => _resultCode;
        init
        {
            _resultCode = value;
            _originalTemplate = GetMessageTemplate(_resultCode);
            // 自動設置訊息模板，但不填充參數
            if (string.IsNullOrEmpty(_message))
            {
                _message = _originalTemplate;
            }
        }
    }

    /// <summary>
    /// System result message
    /// </summary>
    [JsonProperty(nameof(Message))]
    public string Message
    {
        get => _message;
        set
        {
            // 如果設定的值與模板相同，直接設定
            if (value == _originalTemplate || string.IsNullOrEmpty(_originalTemplate))
            {
                _message = value;
                return;
            }

            // 檢查模板中是否有佔位符 {0}, {1} 等
            var hasPlaceholders = Regex.IsMatch(_originalTemplate, @"\{\d+\}");

            if (hasPlaceholders)
            {
                // 假設傳入的是格式化參數
                try
                {
                    _message = string.Format(_originalTemplate, value);
                }
                catch (FormatException)
                {
                    // 如果格式化失敗，直接設定值
                    _message = value;
                }
            }
            else
            {
                // 直接設定值
                _message = value;
            }
        }
    }

    /// <summary>
    /// Set message using multiple parameters
    /// </summary>
    public void SetMessageWithParams(params object[] args)
    {
        if (args == null || args.Length == 0)
        {
            return;
        }

        if (string.IsNullOrEmpty(_originalTemplate))
        {
            // 如果沒有模板，使用第一個參數作為消息
            _message = args[0]?.ToString() ?? string.Empty;
            return;
        }

        try
        {
            _message = string.Format(_originalTemplate, args);
        }
        catch (FormatException)
        {
            // 如果格式化失敗，將所有參數連接起來
            _message = string.Join(", ", args.Select(a => a?.ToString() ?? string.Empty));
        }
    }

    /// <summary>
    /// Result data
    /// </summary>
    [JsonProperty(nameof(ResultObject))]
    public TResult ResultObject { get; set; } = null!;

    /// <summary>
    /// Creates a ResponseModel with specified ResultCode and format parameters
    /// </summary>
    /// <param name="resultCode">The result code to use</param>
    /// <param name="messageParams">Parameters to format into the message template</param>
    /// <returns>A new ResponseModel instance with formatted message</returns>
    public static ResponseModel<TResult> Create(ResultCode resultCode, params object[] messageParams)
    {
        var model = new ResponseModel<TResult> { ResultCode = resultCode };

        if (messageParams != null && messageParams.Length > 0)
        {
            model.SetMessageWithParams(messageParams);
        }

        return model;
    }

    /// <summary>
    /// Creates a success ResponseModel with the specified result object
    /// </summary>
    /// <param name="resultObject">The result object to include</param>
    /// <returns>A new ResponseModel instance with Success result code</returns>
    public static ResponseModel<TResult> Success(TResult resultObject)
    {
        return new ResponseModel<TResult>
        {
            ResultCode = ResultCode.Success,
            ResultObject = resultObject
        };
    }

    // 獲取結果代碼字符串 (保持不變)
    private static string GetResultCodeString(ResultCode resultCode)
    {
        return ResultCodeAttributeMap.TryGetValue(resultCode, out var attribute) ? attribute.CodeString : resultCode.ToString();
    }

    // 獲取錯誤訊息模板 (保持不變)
    private static string GetMessageTemplate(ResultCode resultCode)
    {
        return ResultCodeAttributeMap.TryGetValue(resultCode, out var attribute) ? attribute.MessageTemplate : string.Empty;
    }

    /// <summary>
    /// Format error message with parameters and returns the formatted message
    /// </summary>
    public string FormatErrorMessage(params object[] args)
    {
        var template = GetMessageTemplate(_resultCode);
        if (string.IsNullOrEmpty(template)) return string.Empty;

        try
        {
            return string.Format(template, args);
        }
        catch (FormatException)
        {
            // 處理參數數量不匹配的情況
            return template;
        }
    }

    /// <summary>
    /// Format error message with parameters and sets the Message property
    /// </summary>
    public void SetErrorMessage(params object[] args)
    {
        _message = FormatErrorMessage(args);
    }

    // 將字符串結果代碼解析為 Enum (保持不變)
    private static ResultCode ParseResultCode(string value)
    {
        return CodeStringMap.TryGetValue(value, out var resultCode) ? resultCode : ResultCode.Success; // 默認值
    }
}
