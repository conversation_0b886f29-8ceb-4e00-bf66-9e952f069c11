namespace JKOPay.BatchSystem.Core.Models;

public enum ResultCode
{
    [ResultCode("0001", "Succeeded")]
    Success = 1,

    [ResultCode("0002", "Workflow not found")]
    WorkflowNotFound = 2,

    [ResultCode("0004", "Job not exist.")]
    JobNotExist = 4,

    [ResultCode("0004", "error")]
    Error = 104,  // 使用不同的枚舉值

    [ResultCode("0006", "Job cancelled failed")]
    JobCancellationFailed = 6,

    // Batch Job 相關錯誤
    [ResultCode("Batch-Job-Ex-0002", "No job found with the provided workflowId: {0}")]
    BatchJobEx0002WorkflowNotFound = 10002,

    [ResultCode("Batch-Job-Ex-0002", "Create job failed, {0}")]
    BatchJobEx0002CreateFailed = 10003,

    [ResultCode("Batch-Job-Ex-0002", "Update Job status failed, WorkflowId: {0}")]
    BatchJobEx0002UpdateFailed = 10004,

    [ResultCode("Batch-Job-Ex-0002", "No job found with the provided jobId: {0}")]
    BatchJobEx0002JobNotFound = 10005,

    [ResultCode("Batch-Job-Ex-0003", "Job not found, JobId: {0}")]
    BatchJobEx0003JobIdNotFound = 10006,

    [ResultCode("Batch-Job-Ex-0003", "Job not found, WorkflowId: {0}, JobId: {1}")]
    BatchJobEx0003WorkflowJobNotFound = 10007,

    [ResultCode("Batch-Job-Ex-0003", "Job not found, {0} ID: {1}")]
    BatchJobEx0003ActionIdNotFound = 10008,

    // Schedule Job 相關錯誤
    [ResultCode("Batch-ScheduleJob-Ex-0002", "disable schedule Job failed")]
    ScheduleJobEx0002 = 20002,

    [ResultCode("Batch-ScheduleJob-Ex-0003", "Schedule Job not exist.")]
    ScheduleJobEx0003 = 20003,

    [ResultCode("Batch-ScheduleJob-Ex-0004", "Workflow not exist.")]
    ScheduleJobEx0004 = 20004,

    [ResultCode("Batch-ScheduleJob-Ex-0005", "{0} schedule Job failed, {1}")]
    ScheduleJobEx0005 = 20005,

    // Argo Workflow 相關錯誤
    [ResultCode("Argo-Workflow-Ex-0002", "Failed to submit workflow")]
    ArgoWorkflowEx0002 = 30002
}