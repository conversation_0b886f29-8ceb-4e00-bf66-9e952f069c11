using Microsoft.EntityFrameworkCore.Metadata.Internal;

namespace JKOPay.BatchSystem.Core.Models.Option.ArgoWorkflow;

// ReSharper disable once ClassNeverInstantiated.Global
public class WorkflowOption
{
    public string Submit { get; set; } = null!;

    public string List { get; set; } = null!;

    public string? Log { get; set; }

    public string Stop { get; set; } = null!;

    public string Terminate { get; set; } = null!;

    public string Delete { get; set; } = null!;

    public int ExpirationThreshold { get; set; }

    public string Resubmit { get; set; } = null!;
}