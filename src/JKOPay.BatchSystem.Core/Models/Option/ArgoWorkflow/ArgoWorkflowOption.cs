using JKOPay.BatchSystem.Core.Models.Argo.Cron;

namespace JKOPay.BatchSystem.Core.Models.Option.ArgoWorkflow;

public class ArgoWorkflowOption
{
    public string Domain { get; set; } = null!;

    public KafkaTopicOption KafkaTopic { get; set; } = null!;

    public WorkflowTemplateOption WorkflowTemplate { get; set; } = null!;

    public WorkflowOption Workflow { get; set; } = null!;

    public CronWorkflowOption CronWorkflow { get; set; } = null!;

    public SensorOption Sensor { get; set; } = null!;

    public EventsourceOption Eventsource { get; set; } = null!;
}