﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    <PropertyGroup>
      <!-- 確保使用中央套件管理 -->
      <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
      <!-- 指定要使用的 NuGet 配置檔案位置 -->
      <RestoreConfigFile>$(MSBuildThisFileDirectory)..\..\nuget.config</RestoreConfigFile>
    </PropertyGroup>
    <ItemGroup>
      <PackageReference Include="Confluent.Kafka" />
      <PackageReference Include="JKOPay.Platform.BatchSystem" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" >
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.Extensions.Hosting" />
      <PackageReference Include="Microsoft.Extensions.Http" />
      <PackageReference Include="Newtonsoft.Json" />
      <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Repositories\" />
    </ItemGroup>
</Project>
