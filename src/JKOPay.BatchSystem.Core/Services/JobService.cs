// ReSharper disable ConvertToPrimaryConstructor
// ReSharper disable ClassWithVirtualMembersNeverInherited.Global

using System.Linq.Expressions;
using JKOPay.BatchSystem.Core.Extensions;
using JKOPay.BatchSystem.Core.Models;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;
using JKOPay.BatchSystem.Core.Models.Core;
using JKOPay.BatchSystem.Core.Models.Job;
using JKOPay.BatchSystem.Core.Utilities;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JKOPay.BatchSystem.Core.Services;

public class JobService : IJobService
{
    private readonly BatchDbContext _dbContext;

    private readonly IArgoWorkflowUtility _argoUtility;

    private readonly IArgoWorkflowService _argoWorkflowService;

    private readonly ILogger<JobService> _logger;

    private readonly string _env;

    public JobService(
        BatchDbContext dbContext,
        IArgoWorkflowUtility argoUtility,
        IArgoWorkflowService argoService,
        [FromKeyedServices("ENV")] string env,
        ILogger<JobService> logger)
    {
        _argoUtility = argoUtility;
        _dbContext = dbContext;
        _argoWorkflowService = argoService;
        _logger = logger;
        _env = env;
    }

    /// <summary>
    /// Get Job Record by Job Id
    /// </summary>
    /// <param name="jobId"></param>
    /// <returns></returns>
    public async Task<ResponseModel<object>> GetJobAsyncUseJobId(string jobId)
    {
        return await GetJobInternalAsync(j => j.JobId == jobId, jobId.ToString(), "Job");
    }

    /// <summary>
    /// Get Job Record by Workflow Id
    /// </summary>
    /// <param name="workflowId">Workflow Id</param>
    /// <returns></returns>
    public async Task<ResponseModel<object>> GetJobAsyncUseWorkflowId(string workflowId)
    {
        return await GetJobInternalAsync(j => j.WorkflowId == workflowId, workflowId, "Workflow");
    }

    public async Task<ResponseModel<List<JobRecord>>> GetJobsAsync(string team, int page, int count)
    {
        var jobs = await _dbContext.JobRecords.Where(j => j.Team == team)
            .OrderByDescending(p => p.CreatedTime)
            .Skip((page - 1) * count)
            .Take(count)
            .ToListAsync();

        return new ResponseModel<List<JobRecord>>
        {
            ResultCode = ResultCode.Success,
            ResultObject = jobs
        };
    }

    public async Task<ResponseModel<object>> GetJobInfoAsync(string jobId)
    {
        var job = await _dbContext.JobRecords.FirstOrDefaultAsync(j => j.JobId.ToString() == jobId);
        if (job == null)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.BatchJobEx0003JobIdNotFound,
                Message = new ResponseModel<object>().FormatErrorMessage(jobId)
            };
        }

        var createJob = new CreateJob
        {
            JobName = job.JobName!,
            WorkflowTemplateName = job.WorkflowTemplateName!,
            Priority = (JobPriorityEnum)Enum.Parse(typeof(JobPriorityEnum), job.Priority!),
            ServiceNamespace = job.Team!,
        };

        if (job.JobContent != null && job.JobContent.Length > 0)
        {
            createJob.Message = JsonConvert.DeserializeObject<Dictionary<string, object>>(job.JobContent)!;
        }

        return new ResponseModel<object>
        {
            ResultCode = ResultCode.Success,
            ResultObject = new Dictionary<string, object>
            {
                { "data", JsonConvert.SerializeObject(createJob) }
            }
        };
    }

    public async Task<ResponseModel<object>> UpdateTotalRecordsAsync(int totalCount, string workflowId)
    {
        try
        {
            var jobRecord = await _dbContext.JobRecords.FirstOrDefaultAsync(j => j.WorkflowId == workflowId);
            if (jobRecord == null)
            {
                return new ResponseModel<object>
                {
                    ResultCode = ResultCode.BatchJobEx0002WorkflowNotFound,
                    Message = new ResponseModel<object>().FormatErrorMessage(workflowId)
                };
            }

            jobRecord.TotalRecords = totalCount;
            var result = await _dbContext.SaveChangesAsync().ConfigureAwait(false);
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Success,
                Message = "Job record executed successfully.",
                ResultObject = new Dictionary<string, object>
                {
                    { "UpdatedCount", result }
                }
            };
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task<ResponseModel<object>> UpdateProcessRecordsAsync(int processedRecords, string workflowId)
    {
        var jobRecord = await _dbContext.JobRecords.FirstOrDefaultAsync(j => j.WorkflowId == workflowId);
        if (jobRecord == null)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.BatchJobEx0002WorkflowNotFound,
                Message = new ResponseModel<object>().FormatErrorMessage(workflowId)
            };
        }

        jobRecord.ProcessedRecords = processedRecords;
        await _dbContext.SaveChangesAsync().ConfigureAwait(false);
        return new ResponseModel<object>
        {
            ResultCode = ResultCode.Success,
            Message = "Job record executed successfully."
        };
    }

    public async Task<ResponseModel<object>> GetJobInfoTemplateAsync(string @namespace, string jobId, string templateName, string source)
    {
        if (source.Equals("job", StringComparison.CurrentCultureIgnoreCase))
        {
            var workflowTemplateName = _env == "prod" ? templateName : $"{_env}-{templateName}";
            _logger.LogInformation("Creating job template: {workflowTemplateName}", workflowTemplateName);

            var tmp = new JObject
            {
                { "priority", "{Immediate or Priority}" },
                { "jobName", "{Your are JobName}" },
                { "workflowTemplateName", workflowTemplateName },
                { "message", new JObject
                    {
                        { "jobData", new JObject
                            {
                                { "parameter", new JObject() },
                                { "jobName", "{Your are JobName}" }
                            }
                        }
                    }
                },
                { "serviceNamespace", $"{@namespace}" }
            };

            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Success,
                ResultObject = new Dictionary<string, object>
                {
                    { "data", JsonConvert.SerializeObject(tmp, Formatting.Indented) }
                }
            };
        }

        var jobRecord = _dbContext.JobRecords.FirstOrDefault(p => p.JobId! == jobId);
        if (jobRecord == null)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.BatchJobEx0002JobNotFound,
                Message = new ResponseModel<object>().FormatErrorMessage(jobId)
            };
        }

        var workflowTemplate = await _argoWorkflowService.GetWorkflowTemplateAsync(@namespace, jobRecord.WorkflowTemplateName!);
        var jobContent = new JObject
        {
            { "priority", "Immediate" },
            { "jobName", $"{jobRecord.JobName}" },
            { "workflowTemplateName", $"{jobRecord.WorkflowTemplateName}" },
            { "message", JObject.Parse(jobRecord.JobContent!) },
            { "serviceNamespace", $"{@namespace}" }
        };

        return new ResponseModel<object>
        {
            ResultCode = ResultCode.Success,
            ResultObject = new Dictionary<string, object>
            {
                { "data", JsonConvert.SerializeObject(jobContent, Formatting.Indented) }
            }
        };
    }

    public async Task<ResponseModel<Dictionary<string, string>>> CreateJobAsync(CreateJob sourceJob)
    {
        var time = GetNow().DateTime;
        var jobRecord = new JobRecord()
        {
            JobId = GetGuid(),
            JobName = sourceJob.JobName,
            Priority = sourceJob.Priority.ToString(),
            WorkflowId = "default",
            WorkflowName = "",
            WorkflowTemplateName = sourceJob.WorkflowTemplateName,
            Team = sourceJob.ServiceNamespace,
            CreatedTime = time,
            UpdatedTime = time,
            Status = JobStatusEnum.New.ToString(),
            JobContent = JsonConvert.SerializeObject(sourceJob.Message)
        };

        _dbContext.JobRecords.Add(jobRecord);
        await _dbContext.SaveChangesAsync().ConfigureAwait(false);

        switch (sourceJob.Priority)
        {
            case JobPriorityEnum.Immediate:
                var submitResult = await CreateImmediateJobHandlerAsync(sourceJob, jobRecord);
                if (submitResult.IsSuccess == false)
                {
                    jobRecord.Status = JobStatusEnum.Error.ToString();
                    await _dbContext.SaveChangesAsync().ConfigureAwait(false);
                    var returnObj = new ResponseModel<Dictionary<string, string>>
                    {
                        ResultCode = ResultCode.BatchJobEx0002CreateFailed,
                        Message = submitResult.ErrorMessage!
                    };

                    return returnObj;
                }

                jobRecord.WorkflowId = submitResult.Data!.Metadata.Uid;
                jobRecord.WorkflowName = submitResult.Data!.Metadata.Name;

                var wf = new Workflow
                {
                    WorkflowId = submitResult.Data!.Metadata.Uid,
                    WorkflowName = submitResult.Data.Metadata.Name,
                    Status = "Pending",
                    CreatedTime = GetNow().DateTime,
                    UpdatedTime = GetNow().DateTime
                };

                _dbContext.Workflows.Add(wf);
                await _dbContext.SaveChangesAsync().ConfigureAwait(false);

                break;
            case JobPriorityEnum.Priority:
            case JobPriorityEnum.Normal:
                var result = await _argoWorkflowService.SubmitWorkflowAsync(sourceJob, jobRecord.JobId!);
                jobRecord.WorkflowName = result.Result == "0001" ? result.ResultObject.Metadata.Name : jobRecord.WorkflowName;
                var workflow = new Workflow
                {
                    WorkflowName = jobRecord.WorkflowName,
                    CreatedTime = jobRecord.CreatedTime,
                    UpdatedTime = jobRecord.UpdatedTime,
                    Status = "Pending"
                };

                _dbContext.Workflows.Add(workflow);
                await _dbContext.SaveChangesAsync().ConfigureAwait(false);
                break;
            case JobPriorityEnum.Schedule:
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }

        return new ResponseModel<Dictionary<string, string>>
        {
            ResultCode = ResultCode.Success,
            ResultObject = new Dictionary<string, string>
            {
                { "JobId", jobRecord.JobId ?? "" },
                { "WorkflowId", jobRecord.WorkflowId },
                { "WorkflowName", jobRecord.WorkflowName }
            }
        };
    }

    /// <summary>
    /// 給 Schedule Job 新增 Job record 使用
    /// </summary>
    /// <param name="sourceJob">job information</param>
    /// <returns></returns>
    public async Task<ResponseModel<object>> CreateJobAsync(CreateScheduleJob sourceJob)
    {
        var job = new JobRecord()
        {
            WorkflowName = sourceJob.WorkflowName,
            WorkflowTemplateName = sourceJob.WorkflowTemplateName,
            Team = sourceJob.Team,
            Priority = sourceJob.Priority.ToString(),
            Status = JobStatusEnum.New.ToString(),
            WorkflowId = sourceJob.WorkflowId,
            JobId = GetGuid(),
            JobName = "",
            CreatedTime = GetNow().DateTime,
            UpdatedTime = GetNow().DateTime
        };

        _dbContext.JobRecords.Add(job);


        var jobNameWithEnv = _env == "prod" ? sourceJob.JobName : $"{_env}-{sourceJob.JobName}";
        _logger.LogInformation("Create schedule job, JobName: {JobName}", jobNameWithEnv);
        var result = 0;
        var scheduleJob = await _dbContext.ScheduleJobs.FirstOrDefaultAsync(p => p.IsEnable == true && p.ScheduleJobName == jobNameWithEnv);
        if (scheduleJob != null)
        {
            scheduleJob.LatestStatus = ScheduleJobStatusEnum.Running.ToString();
            scheduleJob.LatestStartedAt = default(DateTime);
            scheduleJob.LatestFinishedAt = default(DateTime);
            scheduleJob.LatestWorkflowId = string.Empty;
            scheduleJob.LatestWorkflowName = string.Empty;
            result = await _dbContext.SaveChangesAsync().ConfigureAwait(false);
        }
        else
        {
            _logger.LogError("ScheduleJob not found, ScheduleJobName: {ScheduleJobName}", sourceJob.JobName);
        }

        _logger.LogInformation("Create schedule job record, JobName: {JobName}, workflowId: {WorkflowId}, Result: {Result}", jobNameWithEnv, sourceJob.WorkflowId, result);
        var resultCode = result == 0 ? ResultCode.ScheduleJobEx0002 : ResultCode.Success;
        return new ResponseModel<object>
        {
            ResultCode = resultCode,
            ResultObject = new
            {
                JobId = job.JobId
            }
        };
    }

    /// <summary>
    /// 建立 JobRecord
    /// </summary>
    /// <param name="namespace"></param>
    /// <param name="jobId"></param>
    /// <returns></returns>
    public async Task<ResponseModel<object>> UpdateJobAsync(string @namespace, string jobId)
    {
        var jobRecord = await _dbContext.JobRecords.FirstOrDefaultAsync(j => j.JobId.ToString() == jobId);
        if (jobRecord == null)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.BatchJobEx0002JobNotFound,
                Message = jobId
            };
        }

        // 只先做透過 API resubmit 的方式而不透過 kafka
        switch (jobRecord.Priority)
        {
            case "Immediate":
            case "Priority":
            case "Normal":
                jobRecord.WorkflowName = jobRecord.WorkflowName ?? "";
                var submitResult = await _argoWorkflowService.ResubmitWorkflow(@namespace, jobRecord.WorkflowName);
                jobRecord.Status = JobStatusEnum.Created.ToString();
                await _dbContext.SaveChangesAsync().ConfigureAwait(false);
                break;
        }

        // Return the job record
        return new ResponseModel<object>
        {
            ResultCode = ResultCode.Success,
            Message = "Job record executed successfully."
        };
    }

    /// <summary>
    /// Cancel job
    /// </summary>
    /// <param name="namespace">Team name</param>
    /// <param name="jobId"></param>
    /// <returns></returns>
    public async Task<ResponseModel<object>> CanceledJobAsync(string @namespace, string jobId)
    {
        // check if Job exists in the db and it's status is not already cancelled
        var job = _dbContext.JobRecords.FirstOrDefault(j => j.JobId == jobId);
        if (job == null)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.JobNotExist,
                ResultObject = new { }
            };
        }

        if (Enum.TryParse(typeof(JobStatusEnum), job.Status, true, out var status) && status is JobStatusEnum jobStatus)
        {
            var index = (int)jobStatus;
            if (jobStatus is JobStatusEnum.Canceled)
            {
                return new ResponseModel<object>
                {
                    ResultCode = ResultCode.Error,
                    ResultObject = new { }
                };
            }

            if (index < (int)JobStatusEnum.WorkflowStarted)
            {
                job.Status = JobStatusEnum.Canceled.ToString();
                job.UpdatedTime = GetNow().DateTime;
                // _dbContext.JobRecords.Update(job);
                var changeCount = await _dbContext.SaveChangesAsync().ConfigureAwait(false);

                if (changeCount == 1)
                {
                    return new ResponseModel<object>
                    {
                        ResultCode = ResultCode.Success,
                        Message = "Job cancelled successfully",
                    };
                }
                else
                {
                    return new ResponseModel<object>
                    {
                        ResultCode = ResultCode.JobCancellationFailed,
                        Message = "Job cancelled failed",
                    };
                }
            }
            else if (index < (int)JobStatusEnum.WorkflowComplete)
            {
                job.WorkflowName = job.WorkflowName ?? "";
                var result = await _argoWorkflowService.TerminateWorkflowAsync(@namespace, job.WorkflowName);
                if (result.Result == "0001")
                {
                    _logger.LogInformation("Terminate workflow success, WorkflowName: {WorkflowName}", jobId);
                    job.Status = JobStatusEnum.Canceled.ToString();
                    // _dbContext.JobRecords.Update(job);
                    var changeCount = await _dbContext.SaveChangesAsync().ConfigureAwait(false);

                    if (changeCount == 1)
                    {
                        return new ResponseModel<object>
                        {
                            ResultCode = ResultCode.Success,
                            Message = "Job cancelled successfully",
                        };
                    }
                }
                else
                {
                    _logger.LogError("Terminate workflow failed, WorkflowName: {WorkflowName}", jobId);
                    return new ResponseModel<object>
                    {
                        ResultCode = ResultCode.Error,
                        ResultObject = new { }
                    };
                }
            }
        }
        else
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Error,
                ResultObject = new { }
            };
        }

        return new ResponseModel<object>
        {
            ResultCode = ResultCode.Error,
            ResultObject = new { }
        };
    }

    public async Task<ResponseModel<Dictionary<string, string>>> UpdateJobStatusAsync(UpdateJob updateJob)
    {

        if (Enum.TryParse(typeof(JobStatusEnum), updateJob.Status, true, out var inputStatus) == false)
        {
            _logger.LogError("Input status error: {Status}", updateJob.Status);
            return new ResponseModel<Dictionary<string, string>>
            {
                ResultCode = ResultCode.BatchJobEx0002UpdateFailed,
                Message = updateJob.WorkflowId
            };
        }

        var job = string.IsNullOrEmpty(updateJob.JobId)
            ? await _dbContext.JobRecords.FirstOrDefaultAsync(p => p.WorkflowId == updateJob.WorkflowId)
            : await _dbContext.JobRecords.FirstOrDefaultAsync(p => p.JobId == updateJob.JobId);

        if (job == null)
        {
            _logger.LogError("JobRecord not exist, WorkflowId: {WorkflowId}", updateJob.WorkflowId);
            // return new ResponseModel<Dictionary<string, string>>
            // {
            //     ResultCode = ResultCode.BatchJobEx0003WorkflowJobNotFound,
            //     Message = new ResponseModel<Dictionary<string, string>>().FormatErrorMessage(updateJob.WorkflowId, updateJob.JobId)
            // };
            return ResponseModel<Dictionary<string, string>>.Create(
                ResultCode.BatchJobEx0003WorkflowJobNotFound, updateJob.WorkflowId, updateJob.JobId);
        }

        var workflowName = string.IsNullOrEmpty(updateJob.WorkflowName) ? job.WorkflowName : updateJob.WorkflowName;
        var jobStatus = Enum.Parse<JobStatusEnum>(job.Status ?? JobStatusEnum.Error.ToString());

        if (ValidateStatus(updateJob.WorkflowId, updateJob.Status, inputStatus, jobStatus, out var responseModel) == false)
        {
            return responseModel!;
        }

        // 這邊更新 WorkflowId 主要是因為透過 Kafka 驅動的 Workflow 一開始新增 JobRecord 的時候沒有 WorkflowId
        job.WorkflowId = updateJob.WorkflowId;
        job.Status = updateJob.Status;
        job.WorkflowName = workflowName;
        job.UpdatedTime = GetNow().DateTime;

        // 如果沒有 Workflow 則為 Schedule Job 這邊要新增 Workflow
        var workflow = await _dbContext.Workflows.FirstOrDefaultAsync(p => p.WorkflowName == updateJob.WorkflowName);
        if (workflow != null)
        {
            workflow.WorkflowId = updateJob.WorkflowId;
            workflow.UpdatedTime = job.UpdatedTime;
        }
        else
        {
            var wf = new Workflow
            {
                WorkflowId = updateJob.WorkflowId,
                WorkflowName = updateJob.WorkflowName,
                Status = "Running",
                CreatedTime = GetNow().DateTime,
                UpdatedTime = GetNow().DateTime
            };

            _dbContext.Workflows.Add(wf);
        }

        var result = await _dbContext.SaveChangesAsync().ConfigureAwait(false);
        if (result > 0)
        {
            return new ResponseModel<Dictionary<string, string>>
            {
                ResultCode = ResultCode.Success,
                ResultObject = new Dictionary<string, string>
                {
                    { "Count", result.ToString() }
                }
            };
        }

        return new ResponseModel<Dictionary<string, string>>
        {
            ResultCode = ResultCode.BatchJobEx0002UpdateFailed,
            Message = new ResponseModel<Dictionary<string, string>>().FormatErrorMessage(updateJob.WorkflowId)
        };
    }

    protected virtual bool ValidateStatus(string workflowId, string status, object? inputStatus,
        JobStatusEnum jobStatus,
        out ResponseModel<Dictionary<string, string>>? responseModel)
    {
        if (inputStatus is JobStatusEnum inputJobStatus && (int)inputJobStatus > (int)jobStatus)
        {
            responseModel = new ResponseModel<Dictionary<string, string>>
            {
                ResultCode = ResultCode.Success
            };

            return true;
        }

        _logger.LogError("Input status error: {Status}", status);
        {
            responseModel = new ResponseModel<Dictionary<string, string>>
            {
                ResultCode = ResultCode.BatchJobEx0002UpdateFailed,
                Message = new ResponseModel<Dictionary<string, string>>().FormatErrorMessage(workflowId)
            };

            return false;
        }
    }

    protected virtual async Task<QueryResult<SubmitResponse>> CreateImmediateJobHandlerAsync(CreateJob sourceJob, JobRecord job)
    {
        sourceJob.Message.Add("jobId", job.JobId!);
        sourceJob.Message.Add("entryPoint", "main");

        var workflowName = _env == "prod" ? sourceJob.WorkflowTemplateName : $"{_env}-{sourceJob.WorkflowTemplateName}";
        sourceJob.Message.Add("templateName", workflowName);
        sourceJob.Message.Add("priority", sourceJob.Priority.ToString());
        var workflowTemplateName = _env == "prod" ? $"{sourceJob.ServiceNamespace}-batchsystem-workflow-template" : $"{_env}-{sourceJob.ServiceNamespace}-batchsystem-workflow-template";
        _logger.LogInformation("Creating job template: {workflowTemplateName}", workflowTemplateName);

        var submitRequest = new SubmitRequest(
            sourceJob.ServiceNamespace,
            sourceJob.JobName,
            workflowTemplateName,
            "main",
            sourceJob.Message,
            new Dictionary<string, string>
            {
                { "team", sourceJob.ServiceNamespace }
            });

        var result = await _argoUtility.SubmitWorkflowAsync(submitRequest);
        if (result.IsSuccess)
        {
            return new QueryResult<SubmitResponse>
            {
                IsSuccess = result.IsSuccess,
                Data = result.Data
            };
        }
        else
        {
            return new QueryResult<SubmitResponse>
            {
                IsSuccess = false,
                ErrorMessage = result.ErrorMessage
            };
        }
    }


    protected virtual DateTimeOffset GetNow()
    {
        return DateTimeOffset.UtcNow;
    }

    protected virtual string? GetGuid()
    {
        return Guid.NewGuid().ToString();
    }

    private async Task<ResponseModel<object>> GetJobInternalAsync(Expression<Func<JobRecord, bool>> predicate, string idValue, string action)
    {
        var job = await _dbContext.JobRecords.FirstOrDefaultAsync(predicate);

        if (job == null)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.BatchJobEx0003ActionIdNotFound,
                Message = new ResponseModel<object>().FormatErrorMessage(action, idValue)
            };
        }

        job.CreatedTime = job.CreatedTime.ConvertToLocalTime();
        job.UpdatedTime = job.UpdatedTime.ConvertToLocalTime();

        return new ResponseModel<object>
        {
            ResultCode = ResultCode.Success,
            ResultObject = job,
        };
    }
}
