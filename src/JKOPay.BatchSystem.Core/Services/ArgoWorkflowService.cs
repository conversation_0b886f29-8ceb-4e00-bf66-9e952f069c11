// ReSharper disable InterpolatedStringExpressionIsNotIFormattable
using Confluent.Kafka;
using JKOPay.BatchSystem.Core.Extensions;
using JKOPay.BatchSystem.Core.Models;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Models.Argo.Sensor;
using JKOPay.BatchSystem.Core.Models.Argo.Workflow;
using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;
using JKOPay.BatchSystem.Core.Models.Core;
using JKOPay.BatchSystem.Core.Utilities;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Eventsource = JKOPay.BatchSystem.Core.Models.Argo.Eventsource.Eventsource;
using Metadata = JKOPay.BatchSystem.Core.Models.Argo.Metadata;
using Workflow = JKOPay.BatchSystem.Core.Models.Argo.Workflow.Workflow;

namespace JKOPay.BatchSystem.Core.Services;

public class ArgoWorkflowService(
    IArgoWorkflowUtility argoWorkflowUtility,
    IKafkaUtility kafkaUtility,
    [FromKeyedServices("ENV")] string env,
    ILogger<ArgoWorkflowService> logger) : IArgoWorkflowService
{
    public async Task<ResponseModel<SubmitResponse>> SubmitWorkflowAsync(SubmitRequest request)
    {
        var response = await argoWorkflowUtility.SubmitWorkflowAsync(request);
        if (response.IsSuccess)
        {
            return new ResponseModel<SubmitResponse> { ResultCode = ResultCode.Success, ResultObject = response.Data! };
        }
        else
        {
            return new ResponseModel<SubmitResponse>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = "Failed to submit workflow"
            };
        }
    }

    public async Task<ResponseModel<SubmitResponse>> SubmitCronWorkflowAsync(string @namespace, string cronWorkflowName)
    {
        var response = await argoWorkflowUtility.SubmitCronWorkflowAsync(@namespace, cronWorkflowName);
        if (response.IsSuccess)
        {
            return new ResponseModel<SubmitResponse> { ResultCode = ResultCode.Success, ResultObject = response.Data! };
        }
        else
        {
            return new ResponseModel<SubmitResponse>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = "Failed to submit workflow"
            };
        }
    }

    public async Task<ResponseModel<SubmitResponse>> SubmitWorkflowAsync(CreateJob sourceJob, string jobId)
    {
        var templateName = env == "prod" ? sourceJob.WorkflowTemplateName : $"{env}-{sourceJob.WorkflowTemplateName}";
        var entry = env == "prod" ? $"{sourceJob.ServiceNamespace}-batchsystem-workflow-template" : $"{env}-{sourceJob.ServiceNamespace}-batchsystem-workflow-template";
        var name = $"{sourceJob.ServiceNamespace}-workflow-{jobId:N}";

        logger.LogDebug($"templateName: {templateName}, entry: {entry}, name: {name}");
        var headers = new Headers
        {
            { "jobId", jobId.GetBytes() },
            { "team", sourceJob.ServiceNamespace.GetBytes() },
            { "templateName", templateName.GetBytes()},
            { "name", name.GetBytes()},

            // avoid using specific terms, use "teamAccount" instead of "serviceAccountName"
            { "teamAccount", $"{sourceJob.ServiceNamespace}-batchsystem-sa".GetBytes()},

            { "entry", entry.GetBytes()},
            { "namespace", sourceJob.ServiceNamespace.GetBytes()},
            {"priority", sourceJob.Priority.ToString().GetBytes()}
        };

        var value = JsonConvert.SerializeObject(sourceJob.Message["jobData"]);
        var topicName = sourceJob.Priority == JobPriorityEnum.Priority
            ? argoWorkflowUtility.GetArgoWorkflowOption().KafkaTopic.Priority
            : argoWorkflowUtility.GetArgoWorkflowOption().KafkaTopic.Normal;
        var sendResult = await kafkaUtility.SendToKafkaAsync(headers, value, topicName);
        if (sendResult)
        {
            return new ResponseModel<SubmitResponse>
            {
                ResultCode = ResultCode.Success,
                ResultObject = new SubmitResponse
                {
                    IsSucceed = true,
                    Metadata = new Metadata
                    {
                        Namespace = sourceJob.ServiceNamespace,
                        Name = name
                    }
                }
            };
        }
        else
        {
            return new ResponseModel<SubmitResponse>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = "Failed to submit workflow"
            };
        }
    }

    public async Task<ResponseModel<object>> ResubmitWorkflow(string @namespace, string workflowName)
    {
        await argoWorkflowUtility.ResubmitWorkflowAsync(@namespace, workflowName);
        return new ResponseModel<object>
        {
            ResultCode = ResultCode.Success
        };
    }

    public async Task<ResponseModel<object>> SubmitCronWorkflow(string @namespace, string cronWorkflowName)
    {
        var response = await argoWorkflowUtility.SubmitCronWorkflowAsync(@namespace, cronWorkflowName);
        if (response.IsSuccess)
        {
            return new ResponseModel<object> { ResultCode = ResultCode.Success, ResultObject = response.Data! };
        }
        else
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = "Failed to submit workflow"
            };
        }
    }

    public async Task<ResponseModel<List<WorkflowTemplate>>> GetWorkflowTemplatesAsync(string @namespace,
        string labelSelector)
    {
        var queryResult = await argoWorkflowUtility.GetWorkflowTemplatesAsync(@namespace, labelSelector);
        if (queryResult.Data == null)
        {
            return new ResponseModel<List<WorkflowTemplate>>
            {
                ResultCode = ResultCode.Success,
                ResultObject = []
            };
        }

        if (queryResult.IsSuccess)
        {
            foreach (var item in queryResult.Data)
            {
                item.Metadata.Name = item.Metadata.Name.Replace($"{env}-", "");
                item.Metadata.CreationTimestamp = item.Metadata.CreationTimestamp.DateTime.ToLocalTime();
            }

            return new ResponseModel<List<WorkflowTemplate>>
            {
                ResultCode = ResultCode.Success,
                ResultObject = queryResult.Data!
            };
        }
        else
        {
            return new ResponseModel<List<WorkflowTemplate>>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = queryResult.ErrorMessage!
            };
        }
    }

    public async Task<ResponseModel<WorkflowTemplate>> GetWorkflowTemplateAsync(string @namespace, string templateName)
    {
        var queryResult = await argoWorkflowUtility.GetWorkflowTemplateAsync(@namespace, $"{env}-{templateName}");
        if (queryResult.Data == null)
        {
            return new ResponseModel<WorkflowTemplate>
            {
                ResultCode = ResultCode.Success
            };
        }

        if (queryResult.IsSuccess)
        {
            return new ResponseModel<WorkflowTemplate>
            {
                ResultCode = ResultCode.Success,
                ResultObject = queryResult.Data!
            };
        }
        else
        {
            return new ResponseModel<WorkflowTemplate>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = queryResult.ErrorMessage!
            };
        }
    }

    public async Task<ResponseModel<List<Workflow>>> GetWorkflows(string @namespace, string labelSelector)
    {
        var queryResult = await argoWorkflowUtility.GetWorkflowsAsync(@namespace, labelSelector);
        if (queryResult.IsSuccess)
        {
            return new ResponseModel<List<Workflow>>
            {
                ResultCode = ResultCode.Success,
                ResultObject = queryResult.Data!
            };
        }
        else
        {
            return new ResponseModel<List<Workflow>>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = queryResult.ErrorMessage!
            };
        }
    }

    public async Task<ResponseModel<List<WorkflowLogResponse>>> GetWorkflowLog(string @namespace, string jobName)
    {
        var queryResult = await argoWorkflowUtility.GetWorkflowLogAsync(@namespace, jobName);
        if (queryResult.IsSuccess)
        {
            return new ResponseModel<List<WorkflowLogResponse>>
            {
                ResultCode = ResultCode.Success,
                ResultObject = queryResult.Data!
            };
        }
        else
        {
            return new ResponseModel<List<WorkflowLogResponse>>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = queryResult.ErrorMessage!
            };
        }
    }

    public async Task<ResponseModel<List<Sensor>>> GetSensors(string @namespace)
    {
        var queryResult = await argoWorkflowUtility.GetSensors(@namespace);
        if (queryResult.IsSuccess)
        {
            return new ResponseModel<List<Sensor>>
            {
                ResultCode = ResultCode.Success,
                ResultObject = queryResult.Data!
            };
        }
        else
        {
            return new ResponseModel<List<Sensor>>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = queryResult.ErrorMessage!
            };
        }
    }

    public async Task<ResponseModel<List<Eventsource>>> GetEventsource(string @namespace)
    {
        var queryResult = await argoWorkflowUtility.GetEventsource(@namespace);
        if (queryResult.IsSuccess)
        {
            return new ResponseModel<List<Eventsource>>
            {
                ResultCode = ResultCode.Success,
                ResultObject = queryResult.Data!
            };
        }
        else
        {
            return new ResponseModel<List<Eventsource>>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = queryResult.ErrorMessage!
            };
        }
    }

    public async Task<ResponseModel<object>> DeleteWorkflow(string @namespace, string workflowName)
    {
        return await HandleWorkflowActionAsync(@namespace, workflowName, argoWorkflowUtility.DeleteWorkflowAsync, "deleted");
    }

    public async Task<ResponseModel<object>> TerminateWorkflowAsync(string @namespace, string workflowName)
    {
        return await HandleWorkflowActionAsync(@namespace, workflowName, argoWorkflowUtility.TerminateWorkflowAsync, "terminate");
    }

    public async Task<ResponseModel<object>> SuspendCronWorkflow(string @namespace, string cronWorkflowName)
    {
        return await HandleWorkflowActionAsync(@namespace, cronWorkflowName, argoWorkflowUtility.SuspendCronWorkflow, "suspend");
    }

    public async Task<ResponseModel<object>> ResumeCronWorkflow(string @namespace, string cronWorkflowName)
    {
        return await HandleWorkflowActionAsync(@namespace, cronWorkflowName, argoWorkflowUtility.ResumeCronWorkflow, "resume");
    }

    public async Task<ResponseModel<object>> DeleteWorkflowTemplate(string @namespace, string templateName)
    {
        var queryResult = await argoWorkflowUtility.DeleteWorkflowTemplate(@namespace, templateName);
        if (queryResult.IsSuccess)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Success
            };
        }
        else
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = queryResult.ErrorMessage!
            };
        }
    }

    public async Task<ResponseModel<object>> DeleteCronWorkflow(string @namespace, string cronWorkflowName)
    {
        var queryResult = await argoWorkflowUtility.DeleteCronWorkflow(@namespace, cronWorkflowName);
        if (queryResult.IsSuccess)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Success
            };
        }
        else
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = queryResult.ErrorMessage!
            };
        }
    }

    public async Task<ResponseModel<object>> DeleteEventsource(string @namespace, string eventsourceName)
    {
        var queryResult = await argoWorkflowUtility.DeleteEventsource(@namespace, eventsourceName);
        if (queryResult.IsSuccess)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Success
            };
        }
        else
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = queryResult.ErrorMessage!
            };
        }
    }

    public async Task<ResponseModel<object>> DeleteSensor(string @namespace, string sensorName)
    {
        var queryResult = await argoWorkflowUtility.DeleteSensor(@namespace, sensorName);
        if (queryResult.IsSuccess)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Success
            };
        }
        else
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = queryResult.ErrorMessage!
            };
        }
    }

    private async Task<ResponseModel<object>> HandleWorkflowActionAsync(
        string @namespace,
        string workflowName,
        Func<string, string, Task<QueryResult<object>>> workflowAction,
        string action)
    {
        logger.LogInformation("{Action} workflow, namespace: {Namespace}, workflow name: {WorkflowName}", action, @namespace, workflowName);

        var queryResult = await workflowAction(@namespace, workflowName);
        if (queryResult.IsSuccess)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Success,
                Message = $"Workflow {action} successfully"
            };
        }
        else
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.ArgoWorkflowEx0002,
                Message = queryResult.ErrorMessage!
            };
        }
    }
}