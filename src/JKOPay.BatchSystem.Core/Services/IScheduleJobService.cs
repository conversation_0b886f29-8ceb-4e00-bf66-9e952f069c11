using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.Platform.BatchSystem.Models.Database;

namespace JKOPay.BatchSystem.Core.Services;

public interface IScheduleJobService
{
    Task<ResponseModel<List<ScheduleJob>>> GetScheduleJobsAsync(string team, int page, int count);
    Task<ResponseModel<Dictionary<string, string>>> ExecuteScheduleJobAsync(string @namespace, string scheduleJobName);
    Task<ResponseModel<object>> CreateScheduleJobAsync(ScheduleJob job);
    Task<ResponseModel<object>> DeleteScheduleJobAsync(ScheduleJob job);
    Task<ResponseModel<object>> UpdateScheduleJobAsync(ScheduleJob job);
    Task<ResponseModel<object>> UpdateScheduleJobStatusAsync(ScheduleJob job);
    Task<ResponseModel<object>> ResumeScheduleJobAsync(string @namespace, string scheduleJobName);
    Task<ResponseModel<object>> SuspendScheduleJobAsync(string @namespace, string scheduleJobName);
}