// ReSharper disable ConvertToPrimaryConstructor

using JKOPay.BatchSystem.Core.Extensions;
using JKOPay.BatchSystem.Core.Models;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MySqlConnector;

namespace JKOPay.BatchSystem.Core.Services;

public class ScheduleJobService : IScheduleJobService
{
    private readonly IArgoWorkflowService _argoWorkflowService;
    private readonly BatchDbContext _dbContext;

    private ILogger<ScheduleJobService> _logger;

    public ScheduleJobService(BatchDbContext dbContext, IArgoWorkflowService argoWorkflowService,
        ILogger<ScheduleJobService> logger)
    {
        _argoWorkflowService = argoWorkflowService;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<ResponseModel<List<ScheduleJob>>> GetScheduleJobsAsync(string team, int page, int count)
    {
        var skip = (page - 1) * count;
        var sql = """
                      SELECT sj.*
                      FROM schedule_job sj
                      INNER JOIN (
                          SELECT schedule_job_name, MAX(updated_time) AS LatestUpdateTime
                          FROM schedule_job
                          WHERE team = @team
                          And is_deleted = 0
                          GROUP BY schedule_job_name
                      ) latest ON sj.schedule_job_name = latest.schedule_job_name AND sj.updated_time = latest.LatestUpdateTime
                      ORDER BY sj.latest_started_at DESC
                      LIMIT @count OFFSET @skip
                  """;

        try
        {

            var jobs = await _dbContext.ScheduleJobs
                .FromSqlRaw(sql,
                    new MySqlParameter("@team", team),
                    new MySqlParameter("@count", count),
                    new MySqlParameter("@skip", skip))
                .ToListAsync();

            foreach (var job in jobs)
            {
                job.LatestFinishedAt = job.LatestFinishedAt.ConvertToLocalTime();
                job.LatestStartedAt = job.LatestStartedAt.ConvertToLocalTime();
                job.CreatedTime = job.CreatedTime.ConvertToLocalTime();
                job.UpdatedTime = job.UpdatedTime.ConvertToLocalTime();
            }

            return new ResponseModel<List<ScheduleJob>>
            {
                ResultCode = ResultCode.Success,
                ResultObject = jobs
            };
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task<ResponseModel<Dictionary<string, string>>> ExecuteScheduleJobAsync(string @namespace,
        string scheduleJobName)
    {
        var workflow = await _argoWorkflowService.SubmitCronWorkflowAsync(@namespace, scheduleJobName);
        var jobRecord = await _dbContext.JobRecords.FirstOrDefaultAsync(
            p => p.WorkflowName == workflow.ResultObject.Metadata.Name);
        return new ResponseModel<Dictionary<string, string>>
        {
            ResultCode = ResultCode.Success,
            ResultObject = new Dictionary<string, string>
            {
                { "JobId", jobRecord?.JobId?.ToString() ?? Guid.Empty.ToString() },
                { "WorkflowId", workflow.ResultObject.Metadata.Uid },
                { "WorkflowName", workflow.ResultObject.Metadata.Name }
            }
        };
    }

    public async Task<ResponseModel<object>> ResumeScheduleJobAsync(string @namespace, string scheduleJobName)
    {
        return await ChangeScheduleJobEnabledStateAsync(@namespace, scheduleJobName, true, _argoWorkflowService.ResumeCronWorkflow);
    }

    public async Task<ResponseModel<object>> SuspendScheduleJobAsync(string @namespace, string scheduleJobName)
    {
        return await ChangeScheduleJobEnabledStateAsync(@namespace, scheduleJobName, false, _argoWorkflowService.SuspendCronWorkflow);
    }

    public async Task<ResponseModel<object>> CreateScheduleJobAsync(ScheduleJob job)
    {
        var succeededResponse = new ResponseModel<object>
        {
            ResultCode = ResultCode.Success,
            ResultObject = new { }
        };

        var isExist = _dbContext.ScheduleJobs.Any(p =>
            p.ScheduleJobName == job.ScheduleJobName &&
            p.Team == job.Team &&
            p.IsDeleted == false &&
            p.IsEnable == true);
        _logger.LogTrace("ScheduleJob is exist, CronWorkflowId: {CronWorkflowId}", job.CronWorkflowId);
        if (isExist) return succeededResponse;

        job.CreatedTime = GetNow().DateTime;
        job.UpdatedTime = GetNow().DateTime;
        _dbContext.ScheduleJobs.Add(job);
        await _dbContext.SaveChangesAsync().ConfigureAwait(false);
        return succeededResponse;
    }

    public async Task<ResponseModel<object>> DeleteScheduleJobAsync(ScheduleJob job)
    {
        var scheduleJob = await _dbContext.ScheduleJobs.FirstOrDefaultAsync(p =>
            p.ScheduleJobName == job.ScheduleJobName &&
            p.Team == job.Team &&
            p.IsDeleted == false &&
            p.IsEnable == true);

        if (scheduleJob == null)
        {
            return new ResponseModel<object>()
            {
                ResultCode = ResultCode.ScheduleJobEx0003,
                ResultObject = new { }
            };
        }

        scheduleJob.IsDeleted = true;
        scheduleJob.IsEnable = false;
        scheduleJob.UpdatedTime = GetNow().DateTime;
        var updateResult = await _dbContext.SaveChangesAsync().ConfigureAwait(false);
        if (updateResult == 0)
        {
            _logger.LogWarning("Update schedule job failed, schedule name: {ScheduleName} not exist.",
                job.ScheduleJobName);
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.ScheduleJobEx0002,
                ResultObject = new { }
            };
        }
        else
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Success,
                ResultObject = new { }
            };
        }
    }

    public async Task<ResponseModel<object>> UpdateScheduleJobAsync(ScheduleJob job)
    {
        var succeededResponse = new ResponseModel<object>
        {
            ResultCode = ResultCode.Success,
            ResultObject = new { }
        };

        var scheduleJob = _dbContext.ScheduleJobs.FirstOrDefault(p =>
            p.ScheduleJobName == job.ScheduleJobName &&
            p.Team == job.Team &&
            p.IsDeleted == false &&
            p.IsEnable == true);

        _logger.LogTrace("ScheduleJob is exist, CronWorkflowId: {CronWorkflowId}", job.CronWorkflowId);
        if (scheduleJob is null)
        {
            _logger.LogWarning("Update schedule job failed, schedule name: {ScheduleName} not exist.",
                job.ScheduleJobName);
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.ScheduleJobEx0003,
                ResultObject = new { }
            };
        }

        scheduleJob.CronWorkflowId = job.CronWorkflowId;
        scheduleJob.Comments = job.Comments;
        scheduleJob.ExecutionCycle = job.ExecutionCycle;
        scheduleJob.WorkflowTemplateRef = job.WorkflowTemplateRef;
        scheduleJob.UpdatedTime = GetNow().DateTime;
        await _dbContext.SaveChangesAsync().ConfigureAwait(false);
        return succeededResponse;
    }

    public async Task<ResponseModel<object>> UpdateScheduleJobStatusAsync(ScheduleJob job)
    {
        var succeededResponse = new ResponseModel<object>
        {
            ResultCode = ResultCode.Success,
            ResultObject = new { }
        };

        var scheduleJob = _dbContext.ScheduleJobs.FirstOrDefault(p =>
            p.ScheduleJobName == job.ScheduleJobName &&
            p.CronWorkflowId == job.CronWorkflowId &&
            p.Team == job.Team &&
            p.IsDeleted == false &&
            p.IsEnable == true);
        if (scheduleJob is null)
        {
            _logger.LogWarning("Update schedule job failed, schedule name: {ScheduleName} not exist.",
                job.ScheduleJobName);
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.ScheduleJobEx0003,
                ResultObject = new { }
            };
        }

        var workflow = _dbContext.Workflows.FirstOrDefault(p => p.WorkflowId == job.LatestWorkflowId);
        if (workflow is null)
        {
            _logger.LogWarning("Update schedule job failed, workflow name: {WorkflowName}, workflow id: {WorkflowId} not exist.",
                job.LatestWorkflowName, job.LatestWorkflowId);

            // 目前這一隻 API 只會給 Operator 使用因為先後順序的關係
            // 所以 Operator 呼叫這一隻 API 的時後有可能還沒有建立 Workflow
            // 為了不影響監控所以這邊不會回傳錯誤只紀錄 Warning
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Success,
                ResultObject = new { }
            };
        }

        if (scheduleJob.LatestWorkflowName == job.LatestWorkflowName && scheduleJob.LatestStatus == job.LatestStatus)
        {
            return succeededResponse;
        }

        scheduleJob.LatestStatus = job.LatestStatus;
        scheduleJob.LatestWorkflowName = job.LatestWorkflowName;
        scheduleJob.LatestWorkflowId = job.LatestWorkflowId;
        scheduleJob.LatestStartedAt = job.LatestStartedAt;

        workflow.Status = job.LatestStatus;
        if (job is { LatestStatus: "Succeeded", LatestFinishedAt: not null })
        {
            scheduleJob.LatestFinishedAt = job.LatestFinishedAt;
            workflow.FinishedAt = job.LatestFinishedAt;
        }

        scheduleJob.UpdatedTime = GetNow().DateTime;
        workflow.UpdatedTime = GetNow().DateTime;
        workflow.StartedAt = job.LatestStartedAt;
        await _dbContext.SaveChangesAsync().ConfigureAwait(false);
        return succeededResponse;
    }

    protected virtual DateTimeOffset GetNow()
    {
        return DateTimeOffset.UtcNow;
    }

    protected virtual Guid GetGuid()
    {
        return Guid.NewGuid();
    }

    private async Task<ResponseModel<object>> ChangeScheduleJobEnabledStateAsync(string @namespace, string scheduleJobName,
        bool isEnable, Func<string, string, Task<ResponseModel<object>>> argoWorkflowAction)
    {
        var scheduleJob = await _dbContext.ScheduleJobs.FirstOrDefaultAsync(p =>
            p.ScheduleJobName == scheduleJobName &&
            p.Team == @namespace &&
            p.IsDeleted == false &&
            p.IsEnable != isEnable);

        if (scheduleJob == null)
        {
            _logger.LogWarning("Update schedule job failed, schedule name: {ScheduleName} not exist.", scheduleJobName);
            return new ResponseModel<object>()
            {
                ResultCode = ResultCode.ScheduleJobEx0003,
                ResultObject = new { }
            };
        }

        scheduleJob.IsEnable = isEnable;
        scheduleJob.UpdatedTime = GetNow().DateTime;

        // Update argo workflow status on argo workflw server
        var argoUpdateResult = await argoWorkflowAction(@namespace, scheduleJobName);
        if (argoUpdateResult.ResultCode != ResultCode.Success)
        {
            _logger.LogWarning("{Action} schedule job {ScheduleJobName} failed, {Message}",
                isEnable ? "Resume" : "Suspend", scheduleJobName, argoUpdateResult.Message);
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.ScheduleJobEx0005,
                Message = new ResponseModel<object>().FormatErrorMessage(isEnable ? "Resume" : "Suspend", argoUpdateResult.Message ?? "")
            };
        }

        var updateResult = await _dbContext.SaveChangesAsync().ConfigureAwait(false);
        if (updateResult == 0)
        {
            _logger.LogWarning("Update schedule job failed, schedule name: {ScheduleName} changed failed.",
                scheduleJobName);
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.ScheduleJobEx0002,
                Message = new ResponseModel<object>().FormatErrorMessage(isEnable ? "enable" : "disable")
            };
        }
        else
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Success
            };
        }
    }
}
