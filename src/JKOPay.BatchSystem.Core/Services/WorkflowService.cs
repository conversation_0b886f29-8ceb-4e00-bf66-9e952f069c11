// ReSharper disable once ConvertToPrimaryConstructor

using JKOPay.BatchSystem.Core.Models;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.Platform.BatchSystem.Models.Database;
using JKOPay.BatchSystem.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace JKOPay.BatchSystem.Core.Services;

public class WorkflowService(BatchDbContext dbContext, ILogger<JobService> logger) : IWorkflowService
{
    private readonly ILogger<JobService> _logger = logger;

    /// <summary>
    /// 取得紀錄在資料庫裡的 Workflow 資訊
    /// </summary>
    /// <param name="workflowName">Workflow Name</param>
    /// <returns></returns>
    public async Task<ResponseModel<Workflow>> GetWorkflowAsync(string workflowName)
    {
        var workflow = await dbContext.Workflows.FirstOrDefaultAsync(x => x.WorkflowName == workflowName);
        if(workflow == null)
        {
            return new ResponseModel<Workflow>
            {
                ResultCode = ResultCode.WorkflowNotFound
            };
        }

        workflow.StartedAt = workflow.StartedAt.ConvertToLocalTime();
        workflow.FinishedAt = workflow.FinishedAt.ConvertToLocalTime();
        workflow.CreatedTime = workflow.CreatedTime.ConvertToLocalTime();
        workflow.UpdatedTime = workflow.UpdatedTime.ConvertToLocalTime();

        return new ResponseModel<Workflow>
        {
            ResultCode = ResultCode.Success,
            ResultObject = workflow,
        };
    }

    public async Task<ResponseModel<object>> UpdateWorkflowStatusAsync(UpdateWorkflow wf)
    {
        var workflow = await dbContext.Workflows.FirstOrDefaultAsync(x => x.WorkflowName == wf.WorkflowName);
        if(workflow == null)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.WorkflowNotFound
            };
        }

        if (workflow.Status == wf.Status && workflow.StartedAt == wf.StartedAt)
        {
            return new ResponseModel<object>
            {
                ResultCode = ResultCode.Success
            };
        }

        workflow.Status = wf.Status;
        workflow.StartedAt = wf.StartedAt;
        if(wf.FinishedAt != DateTime.MinValue)
        {
            workflow.FinishedAt = wf.FinishedAt;
        }

        workflow.UpdatedTime = DateTime.UtcNow;
        await dbContext.SaveChangesAsync();

        return new ResponseModel<object>
        {
            ResultCode = ResultCode.Success
        };
    }
}