using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.Platform.BatchSystem.Models.Database;

namespace JKOPay.BatchSystem.Core.Services;

public interface IJobService
{
    Task<ResponseModel<List<JobRecord>>> GetJobsAsync(string team, int page, int count);
    Task<ResponseModel<object>> GetJobAsyncUseWorkflowId(string workflowId);
    Task<ResponseModel<Dictionary<string, string>>> CreateJobAsync(CreateJob sourceJob);
    Task<ResponseModel<object>> CreateJobAsync(CreateScheduleJob sourceJob);
    Task<ResponseModel<object>> UpdateJobAsync(string @namespace, string jobId);
    Task<ResponseModel<Dictionary<string, string>>> UpdateJobStatusAsync(UpdateJob updateJob);
    Task<ResponseModel<object>> CanceledJobAsync(string @namespace, string jobId);
    Task<ResponseModel<object>> GetJobAsyncUseJobId(string jobId);
    Task<ResponseModel<object>> GetJobInfoAsync(string jobId);
    Task<ResponseModel<object>> UpdateTotalRecordsAsync(int totalCount, string workflowId);
    Task<ResponseModel<object>> UpdateProcessRecordsAsync(int processRecords, string workflowId);

    Task<ResponseModel<object>> GetJobInfoTemplateAsync(string ns, string jobId, string templateName, string source);
}