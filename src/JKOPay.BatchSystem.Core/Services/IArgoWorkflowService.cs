using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Models.Argo.Eventsource;
using JKOPay.BatchSystem.Core.Models.Argo.Sensor;
using JKOPay.BatchSystem.Core.Models.Argo.Workflow;
using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;

namespace JKOPay.BatchSystem.Core.Services;

public interface IArgoWorkflowService
{
    Task<ResponseModel<SubmitResponse>> SubmitWorkflowAsync(SubmitRequest request);

    Task<ResponseModel<SubmitResponse>> SubmitWorkflowAsync(CreateJob sourceJob, string jobId);

    Task<ResponseModel<SubmitResponse>> SubmitCronWorkflowAsync(string @namespace, string cronWorkflowName);

    Task<ResponseModel<object>> ResubmitWorkflow(string @namespace, string workflowName);

    Task<ResponseModel<List<WorkflowTemplate>>> GetWorkflowTemplatesAsync(string @namespace, string labelSelector);

    Task<ResponseModel<List<Workflow>>> GetWorkflows(string @namespace, string labelSelector);

    Task<ResponseModel<List<WorkflowLogResponse>>> GetWorkflowLog(string @namespace, string jobName);

    Task<ResponseModel<List<Sensor>>> GetSensors(string @namespace);

    Task<ResponseModel<List<Eventsource>>> GetEventsource(string @namespace);

    Task<ResponseModel<object>> TerminateWorkflowAsync(string @namespace, string workflowName);

    Task<ResponseModel<WorkflowTemplate>> GetWorkflowTemplateAsync(string @namespace, string templateName);

    Task<ResponseModel<object>> SuspendCronWorkflow(string @namespace, string cronWorkflowName);

    Task<ResponseModel<object>> ResumeCronWorkflow(string @namespace, string cronWorkflowName);

    Task<ResponseModel<object>> DeleteWorkflow(string @namespace, string workflowName);

    Task<ResponseModel<object>> DeleteCronWorkflow(string @namespace, string cronWorkflowName);

    Task<ResponseModel<object>> DeleteWorkflowTemplate(string @namespace, string templateName);

    Task<ResponseModel<object>> DeleteEventsource(string @namespace, string eventsourceName);

    Task<ResponseModel<object>> DeleteSensor(string @namespace, string sensorName);
}