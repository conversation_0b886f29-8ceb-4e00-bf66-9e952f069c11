// ReSharper disable ExplicitCallerInfoArgument
using System.Diagnostics;
using System.Net;
using System.Text;
using JKOPay.BatchSystem.Core.Models.Argo.Cron;
using JKOPay.BatchSystem.Operator.Extensions;
using JKOPay.BatchSystem.Operator.Models;
using JKOPay.Platform.BatchSystem.Models.Database;
using k8s;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Operator;

/// <summary>
/// 監控 CronWorkflow 的 Operator 當 CronWorkflow 設定異動時（包含新增 修改 刪除），會將資料傳送到 BatchSystemApi
/// </summary>
public sealed class ScheduleJobOperator : BackgroundService
{
    private readonly string _namespace;
    private static CancellationTokenSource? _cts;
    private readonly ILogger<ScheduleJobOperator> _logger;
    private readonly ActivitySource _activitySource;
    private readonly HttpClient _httpClient;
    private readonly IKubernetesClientFactory _clientFactory;
    private readonly BatchSystemApiOption _batchSystemApiOption;
    private IKubernetes _client;

    /// <inheritdoc />
    public ScheduleJobOperator(
        IKubernetesClientFactory clientFactory,
        IKubernetes client,
        BatchSystemApiOption batchSystemApiOption,
        [FromKeyedServices("Namespace")] string @namespace,
        IHttpClientFactory factory,
        ILogger<ScheduleJobOperator> logger)
    {
        _logger = logger;
        _logger.LogInformation("ArgoOperator init. Namespace: {Namespace}", @namespace);
        _clientFactory = clientFactory;
        _client = client;
        _namespace = @namespace;
        _batchSystemApiOption = batchSystemApiOption;
        _activitySource = new ActivitySource("K8sCustomControllerSource");
        _httpClient = factory.CreateClient("general");
    }

    public static void CancelOperation()
    {
        _cts?.Cancel();
    }

    public override Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("ScheduleJobOperator starting.");
        _client = _clientFactory.CreateClient();
        return base.StartAsync(cancellationToken);
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("ScheduleJobOperator stopping.");
        _client.Dispose();
        return base.StopAsync(cancellationToken);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            // 跟 ServiceController 註冊 JOB is running
            var result = ServiceController.RegisteredServices(typeof(ScheduleJobOperator));
            if (result.Result == false)
            {
                _logger.LogError("Service {ServiceName} register failed: {Message}", "WorkflowOperator", result.Message);
                HttpListenerService.Opahealth = false;
            }
            else
            {
                _logger.LogInformation("Service {ServiceName} register success", "ScheduleJobOperator");
            }

            _logger.LogInformation("Start watch event. Namespace: {Namespace}", _namespace);
            _cts = CancellationTokenSource.CreateLinkedTokenSource(stoppingToken);
            var combinedToken = _cts.Token;

            var createUrl = $"{_batchSystemApiOption.Domain}healthz";
            var response = await _httpClient.GetAsync(createUrl, combinedToken);
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Batch system health check: {Status}", response.StatusCode);
            }
            else
            {
                _logger.LogWarning("Batch system health check: {Status}", response.StatusCode);
            }

            // 啟動監控 Pod 資源的任務
            await WatchPods(combinedToken);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "An error occurred while executing ScheduleJobOperator: {Message}", e.Message);
            ServiceController.HealthCheck[typeof(WorkflowOperator)] = false;
        }
    }

    private async Task WatchPods(CancellationToken stoppingToken)
    {
        // 使用一個 while 迴圈來確保在發生可恢復的錯誤時能夠自動重試
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                _logger.LogInformation("Attempting to start watching CronWorkflows in namespace {Namespace}...", _namespace);

                var foundPodListResp = await _client.CustomObjects.ListNamespacedCustomObjectWithHttpMessagesAsync(
                    "argoproj.io", "v1alpha1", _namespace, "cronworkflows", watch: true,
                    cancellationToken: stoppingToken);

                // Watch() 方法返回一個 IDisposable，它會在背景處理事件
                // using 區塊確保當 watch 結束或出錯時，資源能被正確釋放
                using (foundPodListResp.Watch<CronWorkflow, object>(OnEvent, OnError, OnClosed))
                {
                    _logger.LogDebug("Successfully started watching CronWorkflows. Watch will remain active.");
                    ServiceController.HealthCheck[typeof(ScheduleJobOperator)] = true;

                    // 2. 使用 Task.Delay(Timeout.Infinite, stoppingToken) 來優雅地等待
                    // 這會讓 WatchPods 方法保持運行，直到 CancellationToken 被觸發
                    // 這比 while(true) { await Task.Delay(10000); } 更簡潔且意圖更明確
                    await Task.Delay(Timeout.Infinite, stoppingToken);
                }
            }
            catch (OperationCanceledException)
            {
                // 當應用程式關閉時，這是預期中的例外，直接跳出迴圈
                _logger.LogInformation("Watch operation was canceled. Shutting down.");
                break;
            }
            catch (Exception e)
            {
                // 3. 捕獲其他所有例外（例如網路錯誤），記錄日誌並在短暫延遲後重試
                _logger.LogError(e, "An unexpected error occurred in the watch loop. Retrying in 15 seconds...");
                OnError(e); // 呼叫 OnError 來統一處理錯誤狀態

                // 在重試前等待一段時間，避免在 API 伺服器有問題時造成大量請求
                await Task.Delay(TimeSpan.FromSeconds(15), stoppingToken);

                // 重新建立 Kubernetes client 可能有助於解決連線問題
                _client = _clientFactory.CreateClient();
            }
        }
    }

    private async void OnEvent(WatchEventType type, CronWorkflow item) => await SendRequest(item, type.ToString());

    private void OnError(Exception e)
    {
        _logger.LogError(e, "An error was reported by the watcher. The watch loop will attempt to reconnect.");
        ServiceController.HealthCheck[typeof(ScheduleJobOperator)] = false;
    }

    private void OnClosed()
    {
        _logger.LogInformation("Watcher connection was closed. The watch loop will attempt to reconnect if not shutting down.");
        ServiceController.HealthCheck[typeof(ScheduleJobOperator)] = false;
    }

    private async Task SendRequest(CronWorkflow cronWorkflow, string type)
    {
        using var activity = _activitySource.StartActivity("ArgoCronWorkflowEvent");
        _logger.LogInformation("On watch event, CronWorkflowName: {CronWorkflowName}, Schedule: {Schedule}, Suspend: {Suspend}, Event type: {Type}", type, cronWorkflow.Metadata.Name, cronWorkflow.Spec.Schedule, cronWorkflow.Spec.Suspend);
        _logger.LogDebug(JsonConvert.SerializeObject(cronWorkflow));

        if (cronWorkflow.Kind != "CronWorkflow") return;
        var scheduleJob = new ScheduleJob
        {
            Team = cronWorkflow.Metadata.Namespace,
            ExecutionCycle = cronWorkflow.Spec.Schedule,
            CronWorkflowId = cronWorkflow.Metadata.Uid,
            LatestStatus = "none",
            ScheduleJobName = cronWorkflow.Metadata.Name,
            WorkflowTemplateRef = cronWorkflow.Spec.WorkflowSpec?.WorkflowTemplateRef.Name!,
            IsDeleted = false,
            IsEnable = true
        };

        var json = JsonConvert.SerializeObject(scheduleJob);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var response = default(HttpResponseMessage);
        switch (type)
        {
            case "Added":
                try
                {
                    var createUrl = $"{_batchSystemApiOption.Domain}{_batchSystemApiOption.Schedule.Create}";
                    response = await _httpClient.PostAsync(createUrl, content: content).ConfigureAwait(false);
                }
                catch (Exception e)
                {
                    _logger.LogWarning("Body: {Body}, StatusCode: {StatusCode}", json, response?.StatusCode);
                    _logger.LogWarning(e.Message, e);
                }

                break;
            case "Modified":
                try
                {
                    var updateUrl = $"{_batchSystemApiOption.Domain}{_batchSystemApiOption.Schedule.Update}";
                    response = await _httpClient.PostAsync(updateUrl, content: content).ConfigureAwait(false);
                }
                catch (Exception e)
                {
                    _logger.LogWarning("Body: {Body}, StatusCode: {StatusCode}", json, response?.StatusCode);
                    _logger.LogWarning(e.Message, e);
                }

                break;
            case "Deleted":
                try
                {
                    var deleteUrl = $"{_batchSystemApiOption.Domain}{_batchSystemApiOption.Schedule.Delete}";
                    response = await _httpClient.PostAsync(deleteUrl, content: content).ConfigureAwait(false);
                }
                catch (Exception e)
                {
                    _logger.LogWarning("Body: {Body}, StatusCode: {StatusCode}", json, response?.StatusCode);
                    _logger.LogWarning(e.Message, e);
                }

                break;
        }

        if (response != null)
        {
            if (response!.StatusCode != HttpStatusCode.OK)
            {
                _logger.LogWarning("Schedule job action is failed. Response status: {Status}, content: {Content}", response.StatusCode.ToString(), await response.Content.ReadAsStringAsync());
            }

            _logger.LogDebug("Response content: {Response}", await response!.Content.ReadAsStringAsync());
        }
        else
        {
            _logger.LogWarning("Response is null.");
        }
    }

    private DateTimeOffset GetNow()
    {
        return DateTimeOffset.Now;
    }
}
