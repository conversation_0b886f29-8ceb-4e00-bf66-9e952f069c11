﻿FROM mcr.microsoft.com/dotnet/aspnet:9.0-alpine-composite AS base
WORKDIR /app

FROM --platform=$BUILDPLATFORM mcr.microsoft.com/dotnet/sdk:9.0-alpine AS build
ARG BUILD_CONFIGURATION=Release
ARG TARGETARCH
WORKDIR /src
COPY ["Directory.Packages.props", "./"]
COPY ["nuget.config", "./"]
COPY ["src/JKOPay.BatchSystem.Operator/JKOPay.BatchSystem.Operator.csproj", "src/JKOPay.BatchSystem.Operator/"]
COPY ["src/JKOPay.BatchSystem.Core/JKOPay.BatchSystem.Core.csproj", "src/JKOPay.BatchSystem.Core/"]
RUN dotnet restore "src/JKOPay.BatchSystem.Operator/JKOPay.BatchSystem.Operator.csproj" -a $TARGETARCH
COPY . .
WORKDIR "/src/src/JKOPay.BatchSystem.Operator"
RUN dotnet build "JKOPay.BatchSystem.Operator.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "JKOPay.BatchSystem.Operator.csproj" -c $BUILD_CONFIGURATION -a $TARGETARCH -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "JKOPay.BatchSystem.Operator.dll"]
