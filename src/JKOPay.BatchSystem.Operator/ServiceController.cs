using JKOPay.BatchSystem.Operator.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace JKOPay.BatchSystem.Operator
{
    public class ServiceController(
        IServiceProvider serviceProvider,
        [FromKeyedServices("Namespace")]string @namespace,
        CustomMetricsProvider metrics,
        ILogger<ServiceController> logger)
        : BackgroundService
    {
        public static Dictionary<Type, bool> HealthCheck { get; set; } = new()
        {
            // { typeof(ScheduleJobOperator), false },
            { typeof(WorkflowOperator), false },
        };

        public static (bool Result, string Message) RegisteredServices(Type service)
        {
            try
            {
                if (HealthCheck.TryGetValue(service, out var _))
                {
                    HealthCheck[service] = true;
                }
                else
                {
                    HealthCheck.Add(service, true);
                }

                return (true, "Service registered successfully");
            }
            catch (Exception e)
            {
                return (false, e.Message);
            }
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                while (!stoppingToken.IsCancellationRequested)
                {
                    foreach (var serviceType in HealthCheck.Where(item => !item.Value).Select(item => item.Key))
                    {
                        logger.LogInformation("Service {ServiceName} is unhealthy, attempting restart", serviceType.Name);

                        var service = serviceProvider.GetServices<IHostedService>()
                            .FirstOrDefault(p => p.GetType() == serviceType);
                        if (service == null)
                        {
                            logger.LogError("Service {ServiceName} not found", serviceType.Name);
                            continue;
                        }

                        await service.StopAsync(stoppingToken);
                        Thread.Sleep(5000);

                        await service.StartAsync(stoppingToken);

                        HealthCheck[serviceType] = true;
                        logger.LogInformation("Service {ServiceName} restarted successfully and is now healthy", serviceType.Name);
                    }

                    // 每 5 秒檢查一次
                    await Task.Delay(5000, stoppingToken);
                    metrics.HeaterBeat.Add(1, new KeyValuePair<string, object?>("service", "ServiceController"), new KeyValuePair<string, object?>("team", @namespace));

                    foreach (var item in HealthCheck)
                    {
                        var value = item.Value ? 1 : 0;
                        metrics.HeaterBeat.Add(value, new KeyValuePair<string, object?>("service", item.Key.Name), new KeyValuePair<string, object?>("team", @namespace));
                    }
                }
            }
            catch (Exception e)
            {
                logger.LogError(e, e.Message);
                HttpListenerService.Opahealth = false;
            }
        }
    }
}
