using System.Net;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace JKOPay.BatchSystem.Operator;

public class HttpListenerService : BackgroundService
{
    public static bool Opahealth = true;

    private readonly ILogger<WorkflowOperator> _logger;

    public HttpListenerService(IServiceProvider serviceProvider,ILogger<WorkflowOperator> logger)
    {
        _logger = logger;
    }
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var listener = new HttpListener();
        listener.Prefixes.Add("http://+:8080/");
        listener.Start();

        while (!stoppingToken.IsCancellationRequested)
        {
            if(!Opahealth)
            {
                _logger.LogWarning("Operator Heartbeat is unHealthy.");
                var context = await listener.GetContextAsync();
                var response = context.Response;
                response.StatusCode = 500;
                response.Close();
            }
            else
            {
                var context = await listener.GetContextAsync();
                var response = context.Response;
                response.StatusCode = 200;
                response.Close();
            }
        }

        listener.Stop();
    }
}
