using System.Diagnostics.Metrics;

namespace JKOPay.BatchSystem.Operator.Models;
/// <summary>
/// Custom metrics provider
/// </summary>
public class CustomMetricsProvider
{
    /// <summary>
    /// Counter for request error
    /// </summary>
    public Counter<int> HeaterBeat { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="CustomMetricsProvider"/> class.
    /// </summary>
    public CustomMetricsProvider(IMeterFactory meterFactory)
    {
        var meter = meterFactory.Create("BatchSystem.Operator");
        HeaterBeat = meter.CreateCounter<int>("batchsystem_operator_heartbeat", "unit", description:"Heartbeat of the batchsystem operator system");
    }
}
