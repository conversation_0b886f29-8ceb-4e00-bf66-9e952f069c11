﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>
    <PropertyGroup>
      <!-- 確保使用中央套件管理 -->
      <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
      <!-- 指定要使用的 NuGet 配置檔案位置 -->
      <RestoreConfigFile>$(MSBuildThisFileDirectory)..\..\nuget.config</RestoreConfigFile>
    </PropertyGroup>
    <ItemGroup>
      <Content Include="..\..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="JKOPay.Platform.HttpClientExtension" />
      <PackageReference Include="JKOPay.Platform.OpenTelemetry" />
      <PackageReference Include="KubernetesClient" />
      <PackageReference Include="Microsoft.Extensions.Configuration" />
      <PackageReference Include="NetEscapades.Configuration.Yaml" />
      <PackageReference Include="OpenTelemetry" />
      <PackageReference Include="OpenTelemetry.Exporter.Console" />
      <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" />
      <PackageReference Include="OpenTelemetry.Exporter.Prometheus.HttpListener" />
      <PackageReference Include="OpenTelemetry.Extensions.Hosting" />
      <PackageReference Include="OpenTelemetry.Instrumentation.Http" />
      <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" />
      <PackageReference Include="Serilog" />
      <PackageReference Include="Serilog.Exceptions" />
      <PackageReference Include="Serilog.Settings.Configuration" />
      <PackageReference Include="Serilog.Sinks.Async" />
      <PackageReference Include="Serilog.Sinks.Console" />
      <PackageReference Include="Serilog.Sinks.OpenTelemetry" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\JKOPay.BatchSystem.Core\JKOPay.BatchSystem.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Update="appsettings.development.yaml">
        <DependentUpon>appsettings.yaml</DependentUpon>
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.local.yaml">
        <DependentUpon>appsettings.yaml</DependentUpon>
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.yaml">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.sit.yaml">
        <DependentUpon>appsettings.yaml</DependentUpon>
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.prod.yaml">
        <DependentUpon>appsettings.yaml</DependentUpon>
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <AdditionalFiles Include="metrics.yaml" />
    </ItemGroup>

</Project>
