using System.Collections;
using System.Net;
using System.Security.Authentication;
using System.Text;
using k8s;

namespace JKOPay.BatchSystem.Operator.Extensions;

public class KubernetesClientFactory(string env, KubernetesClientConfiguration config) : IKubernetesClientFactory
{
    public Kubernetes CreateClient()
    {
        // 在本地開發環境跳過 SSL 驗證
        if (env != "local") return new Kubernetes(config);

        config.SkipTlsVerify = true;

        // 額外設定 HTTP 客戶端處理器以完全跳過憑證驗證
        config.HttpClientTimeout = TimeSpan.FromMinutes(10);

        // 添加自定義 HTTP 客戶端處理器設置
        config.FirstMessageHandlerSetup = (httpClientHandler) =>
        {
            httpClientHandler.SslOptions.RemoteCertificateValidationCallback = (_, certificate, chain, sslPolicyErrors) =>
            {
                Console.WriteLine($"Certificate: {certificate?.Subject}");
                Console.WriteLine($"SSL Errors: {sslPolicyErrors}");
                return true;
            };

            // 額外的 SSL 設定
            httpClientHandler.SslOptions.CertificateRevocationCheckMode =
                System.Security.Cryptography.X509Certificates.X509RevocationMode.NoCheck;

            httpClientHandler.SslOptions.EnabledSslProtocols = SslProtocols.Tls12 | SslProtocols.Tls13;
            httpClientHandler.PooledConnectionIdleTimeout = TimeSpan.FromMinutes(15);
        };

        return new Kubernetes(config);
    }
}
