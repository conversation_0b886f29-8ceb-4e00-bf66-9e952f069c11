// ReS<PERSON>per disable once CyclomaticComplexity
using System.Text;
using JKOPay.BatchSystem.Operator.Extensions;
using JKOPay.BatchSystem.Operator.Models;
using k8s;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;
using Serilog.Context;

namespace JKOPay.BatchSystem.Operator;

/// <summary>
/// 當 CronWorkflow 或 Workflow 執行時，更新狀態到 BatchSystemApi
/// </summary>
public sealed class WorkflowOperator : BackgroundService
{
    private readonly string _namespace;

    private static CancellationTokenSource? _cts;

    private readonly ILogger<WorkflowOperator> _logger;

    private readonly IKubernetesClientFactory _clientFactory;

    private readonly HttpClient _httpClient;

    private readonly BatchSystemApiOption _batchSystemApiOption;

    private IKubernetes _client;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="clientFactory"></param>
    /// <param name="client">IKubernetes client</param>
    /// <param name="batchSystemApiOption">Batch System Api Option</param>
    /// <param name="namespace">Namespace</param>
    /// <param name="factory">HttpClientFactory</param>
    /// <param name="logger">Logger</param>
    public WorkflowOperator(
        IKubernetesClientFactory clientFactory,
        IKubernetes client,
        BatchSystemApiOption batchSystemApiOption,
        [FromKeyedServices("Namespace")] string @namespace,
        IHttpClientFactory factory,
        ILogger<WorkflowOperator> logger)
    {
        _logger = logger;
        _client = client;
        _clientFactory = clientFactory;
        _namespace = @namespace;
        _batchSystemApiOption = batchSystemApiOption;
        _httpClient = factory.CreateClient("general");
    }

    public override Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("WorkflowOperator starting.");
        _client = _clientFactory.CreateClient();
        return base.StartAsync(cancellationToken);
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("WorkflowOperator stopping.");
        _client.Dispose();
        return base.StopAsync(cancellationToken);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            // 跟 ServiceController 註冊 JOB is running
            var result = ServiceController.RegisteredServices(typeof(WorkflowOperator));
            if (result.Result == false)
            {
                _logger.LogError("Service {ServiceName} register failed: {Message}", "WorkflowOperator", result.Message);
                HttpListenerService.Opahealth = false;
            }
            else
            {
                _logger.LogInformation("Service {ServiceName} register success", "WorkflowOperator");
            }

            _logger.LogInformation("Start watch event. Namespace: {Namespace}", _namespace);
            _cts = CancellationTokenSource.CreateLinkedTokenSource(stoppingToken);
            var combinedToken = _cts.Token;

            var createUrl = $"{_batchSystemApiOption.Domain}healthz";
            var response = await _httpClient.GetAsync(createUrl, combinedToken).ConfigureAwait(false);
            if (response.IsSuccessStatusCode)
            {
                _logger.LogDebug("Batch system health check: {Status}, body: {Body}", response.StatusCode, await response.Content.ReadAsStringAsync(stoppingToken));
            }
            else
            {
                _logger.LogWarning("Batch system health check: {Status}", response.StatusCode);
            }

            await WatchPods(combinedToken);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "WorkflowOperator execution failed: {Message}", e.Message);
            ServiceController.HealthCheck[typeof(WorkflowOperator)] = false;
        }
    }

    private async Task WatchPods(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                _logger.LogInformation("Attempting to start watching Workflows in namespace {Namespace}...", _namespace);

                var foundPodListResp = await _client.CustomObjects.ListNamespacedCustomObjectWithHttpMessagesAsync(
                    "argoproj.io", "v1alpha1", _namespace, "workflows", watch: true,
                    cancellationToken: stoppingToken);

                using (foundPodListResp.Watch<object, object>(OnEvent, onClosed: OnClosed, onError: OnError))
                {
                    _logger.LogInformation("Successfully started watching Workflows. Watch will remain active.");
                    ServiceController.HealthCheck[typeof(WorkflowOperator)] = true;

                    await Task.Delay(Timeout.Infinite, stoppingToken);
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Watch operation was canceled. Shutting down.");
                break;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "An unexpected error occurred in the watch loop. Retrying in 15 seconds...");
                OnError(e);

                await Task.Delay(TimeSpan.FromSeconds(15), stoppingToken);

                _client = _clientFactory.CreateClient();
            }
        }
        _logger.LogInformation("WatchPods task has completed.");
    }

    private async void OnEvent(WatchEventType type, object arg2)
    {
        var jsonObject = JObject.Parse(arg2.ToString() ?? string.Empty);
        var kind = jsonObject.SelectToken("kind")?.ToString();
        if (kind != "Workflow")
        {
            return;
        }

        var labels = jsonObject.SelectToken("metadata.labels")?.Cast<JProperty>().Select(p => p.Name).ToList();
        _logger.LogDebug("Labels: {Labels}", JsonConvert.SerializeObject(labels));
        if (labels?.Contains("workflows.argoproj.io/workflow-template") == true)
        {
            _logger.LogDebug("Workflow is workflowtemplate");
            if (await WorkflowHandler(jsonObject)) return;
        }

        if (labels?.Contains("workflows.argoproj.io/cron-workflow") == true)
        {
            _logger.LogDebug("Workflow is CronWorkflow");
            if (await CronWorkflowHandler(jsonObject)) return;
        }

        using (LogContext.PushProperty("Pod", jsonObject, destructureObjects: true))
        {
            Log.Debug("This is an informational message with an Pod.");
        }
    }

    private async Task<bool> CronWorkflowHandler(JObject jsonObject)
    {
        var cronWorkflowId = jsonObject["metadata"]?["ownerReferences"]?[0]?["uid"]?.ToString();
        var cronWorkflowName = jsonObject["metadata"]?["labels"]?["workflows.argoproj.io/cron-workflow"]?.ToString();
        var workflowPhase = jsonObject["metadata"]?["labels"]?["workflows.argoproj.io/phase"]?.ToString();
        var workflowName = jsonObject["metadata"]?["name"]?.ToString();
        var workflowId = jsonObject["metadata"]?["uid"]?.ToString();
        var @namespace = jsonObject.SelectToken("metadata.namespace")?.ToString();
        var startedAt = jsonObject.SelectToken("status.startedAt")?.ToString();
        var finishedAt = jsonObject.SelectToken("status.finishedAt")?.ToString();
        _logger.LogInformation("CronWorkflowId: {CronWorkflowId}, ScheduleJobName: {ScheduleJobName}, LatestStatus: {LatestStatus}, LatestWorkflowName: {LatestWorkflowName}, LatestWorkflowId: {LatestWorkflowId}, Team: {Team}, LatestStartAt: {LatestStartAt}, LatestFinishedAt: {LatestFinishedAt}", cronWorkflowId, cronWorkflowName, workflowPhase, workflowName, workflowId, @namespace, startedAt, finishedAt);

        var body = new JObject
        {
            new JProperty("CronWorkflowId", cronWorkflowId),
            new JProperty("ScheduleJobName", cronWorkflowName),
            new JProperty("LatestStatus", workflowPhase),
            new JProperty("LatestWorkflowName", workflowName),
            new JProperty("LatestWorkflowId", workflowId),
            new JProperty("Team", @namespace),
            new JProperty("LatestStartedAt", startedAt),
            new JProperty("LatestFinishedAt", finishedAt),
        };

        var json = JsonConvert.SerializeObject(body);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var updateUrl = $"{_batchSystemApiOption.Domain}{_batchSystemApiOption.Schedule.UpdateStatus}";
        var response = await _httpClient.PostAsync(updateUrl, content).ConfigureAwait(false);
        if (response.IsSuccessStatusCode == false)
        {
            _logger.LogWarning("Update schedule job status failed. Response: {Response}", await response.Content.ReadAsStringAsync());
        }

        return false;
    }

    private async Task<bool> WorkflowHandler(JObject jsonObject)
    {
        var workflowPhase = jsonObject["metadata"]?["labels"]?["workflows.argoproj.io/phase"]?.ToString();
        var workflowName = jsonObject["metadata"]?["name"]?.ToString();
        var workflowId = jsonObject["metadata"]?["uid"]?.ToString();
        var @namespace = jsonObject.SelectToken("metadata.namespace")?.ToString();
        var startedAt = jsonObject.SelectToken("status.startedAt")?.ToString();
        var finishedAt = jsonObject.SelectToken("status.finishedAt")?.ToString();

        var body = new JObject
        {
            new JProperty("status", workflowPhase),
            new JProperty("workflowName", workflowName),
            new JProperty("workflowId", workflowId),
            new JProperty("startedAt", startedAt),
            new JProperty("finishedAt", finishedAt),
        };

        if (workflowPhase != "Succeeded") return false;
        var json = JsonConvert.SerializeObject(body);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var updateUrl = $"{_batchSystemApiOption.Domain}{_batchSystemApiOption.Workflow.UpdateStatus}";
        var response = await _httpClient.PostAsync(updateUrl, content).ConfigureAwait(false);
        if (response.IsSuccessStatusCode == false)
        {
            _logger.LogWarning("Update schedule job status failed. Response: {Response}", await response.Content.ReadAsStringAsync());
        }

        return false;
    }

    private void OnClosed()
    {
        _logger.LogInformation("Watcher is closed.");
        ServiceController.HealthCheck[typeof(WorkflowOperator)] = false;

    }
    private void OnError(Exception e)
    {
        _logger.LogWarning(e.Message, e);
        ServiceController.HealthCheck[typeof(WorkflowOperator)] = false;
    }
}
