---
Logging:
  LogLevel:
    Default: Information
    Microsoft.AspNetCore: Warning
Serilog:
  Using:
  - Serilog.Sinks.Console
  - Serilog.Sinks.Async
  - Serilog.Exceptions
  MinimumLevel:
    Default: Information
    Override:
      Microsoft.AspNetCore: Warning
  WriteTo:
  - Name: Async
    Args:
      configure:
      - Name: Console
        Args:
          formatter:
            type: "Serilog.Formatting.Json.JsonFormatter, Serilog"
            renderMessage: true
  Enrich:
    - FromLogContext
    - WithExceptionDetails
BatchSystemAPI:
  Domain: https://jkopay-batchsystem-api.foundation.svc.cluster.local:8080/
  Schedule:
    Create: api/v1/schedule/create
    Delete: api/v1/schedule/delete
    Update: api/v1/schedule/update
    UpdateStatus: api/v1/schedule/update/status
  Workflow:
    UpdateStatus: api/v1/workflows/update/status