# -- 創建Workflow表
# CREATE TABLE `Workflow`
# (
#     `Id`           INT(11)                                                NOT NULL AUTO_INCREMENT,
#     `CreatedTime`  DATETIME(6)                                            NULL,
#     `FinishedAt`   DATETIME(6)                                            NULL,
#     `Log`          TEXT                                                   NULL,
#     `StartedAt`    DATETIME(6)                                            NULL,
#     `Status`       VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
#     `UpdatedTime`  DATETIME(6)                                            NULL,
#     `WorkflowId`   VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci  DEFAULT 'DefaultWorkflowId',
#     `WorkflowName` VARCHAR(101) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'DefaultWorkflowName',
#     PRIMARY KEY (`Id`),
#     INDEX `FinishedAt` (`FinishedAt`),
#     INDEX `StartedAt` (`StartedAt`),
#     INDEX `WorkflowId` (`WorkflowId`)
# ) ENGINE = InnoDB
#   DEFAULT CHARSET = utf8mb4
#   COLLATE = utf8mb4_unicode_ci;


-- 修正後的 Workflow 表
CREATE TABLE `workflow`
(
    `id`            INT(11)          NOT NULL AUTO_INCREMENT,
    `created_time`  DATETIME(6)      NULL,
    `finished_at`   DATETIME(6)      NULL,
    `log`           TEXT             NULL,
    `started_at`    DATETIME(6)      NULL,
    `status`        VARCHAR(20)      NULL,
    `updated_time`  DATETIME(6)      NULL,
    `workflow_id`   VARCHAR(50)      DEFAULT 'DefaultWorkflowId',
    `workflow_name` VARCHAR(100)     DEFAULT 'DefaultWorkflowName',
    PRIMARY KEY (`id`),
    INDEX `idx_finished_at` (`finished_at`),
    INDEX `idx_started_at` (`started_at`),
    INDEX `idx_workflow_id` (`workflow_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;