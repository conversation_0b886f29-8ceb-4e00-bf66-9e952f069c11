-- 創建Job_Record表
# CREATE TABLE `Job_Record`
# (
#     `id`                   INT(11)                                                 NOT NULL AUTO_INCREMENT,
#     `CreatedTime`          DATETIME(6)                                             NULL,
#     `JobContent`           TEXT                                                    NULL,
#     `JobName`              VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
#     `Priority`             VARCHAR(15) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL,
#     `ProcessedRecords`     INT(11)                                                 DEFAULT '0',
#     `Status`               VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL,
#     `Team`                 VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL,
#     `TotalRecords`         INT(11)                                                 DEFAULT '0',
#     `UpdatedTime`          DATETIME(6)                                             NULL,
#     `WorkflowId`           VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci  DEFAULT 'DefaultWorkflowId',
#     `WorkflowName`         VARCHAR(101) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'DefaultWorkflowName',
#     `WorkflowTemplateName` VARCHAR(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'DefaultTemplateName',
#     `JobId`                VARCHAR(255)                                            NULL,
#     PRIMARY KEY (`Id`),
#     INDEX `CreatedTime` (`CreatedTime`),
#     INDEX `JobId` (`JobId`),
#     INDEX `WorkflowId` (`WorkflowId`)
# ) ENGINE = InnoDB
#   DEFAULT CHARSET = utf8mb4
#   COLLATE = utf8mb4_unicode_ci;
#

-- 修正後的 Job_Record 表
CREATE TABLE `job_record`
(
    `id`                   INT(11)          NOT NULL AUTO_INCREMENT,
    `created_time`          DATETIME(6)      NULL,
    `job_content`           TEXT             NULL,
    `job_name`              VARCHAR(100)     NULL,
    `priority`              VARCHAR(15)      NULL,
    `processed_records`     INT(11)          DEFAULT '0',
    `status`                VARCHAR(20)      NULL,
    `team`                  VARCHAR(20)      NULL,
    `total_records`         INT(11)          DEFAULT '0',
    `updated_time`          DATETIME(6)      NULL,
    `workflow_id`           VARCHAR(50)      DEFAULT 'DefaultWorkflowId',
    `workflow_name`         VARCHAR(100)     DEFAULT 'DefaultWorkflowName',
    `workflow_template_name` VARCHAR(200)    DEFAULT 'DefaultTemplateName',
    `job_id`                VARCHAR(255)     NULL,
    PRIMARY KEY (`id`),
    INDEX `idx_created_time` (`created_time`),
    INDEX `idx_job_id` (`job_id`),
    INDEX `idx_workflow_id` (`workflow_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;