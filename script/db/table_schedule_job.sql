# -- 創建Schedule_Job表
# CREATE TABLE `Schedule_Job`
# (
#     `JobID`               INT(11)                                                 NOT NULL AUTO_INCREMENT,
#     `Comments`            VARCHAR(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
#     `CreatedTime`         DATETIME(6)                                             NULL,
#     `<PERSON>ronWorkflowId`      VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL,
#     `ExecutionCycle`      VARCHAR(100)                                            NULL,
#     `IsEnable`            TINYINT(1)                                              NOT NULL DEFAULT '1',
#     `LatestFinishedAt`    DATETIME(6)                                             NULL,
#     `LatestStartedAt`     DATETIME(6)                                             NULL,
#     `LatestStatus`        VARCHAR(50)                                             NULL,
#     `LatestWorkflowId`    VARCHAR(50)                                             NULL,
#     `LatestWorkflowName`  VARCHAR(100)                                            NULL,
#     `ScheduleJobName`     VARCHAR(100)                                            NULL,
#     `Team`                VARCHAR(10)                                             NULL,
#     `UpdatedTime`         DATETIME(6)                                             NULL,
#     `WorkflowTemplateRef` VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
#     `IsDeleted`           TINYINT(1)                                              NOT NULL DEFAULT '0',
#     PRIMARY KEY (`JobID`),
#     INDEX `JobID` (`JobID`, `ScheduleJobName`, `IsDeleted`, `IsEnable`)
# ) ENGINE = InnoDB
#   DEFAULT CHARSET = utf8mb4
#   COLLATE = utf8mb4_unicode_ci;

-- 修正後的 Schedule_Job 表
CREATE TABLE `schedule_job`
(
    `job_id`               INT(11)          NOT NULL AUTO_INCREMENT,
    `comments`             VARCHAR(500)     NULL,
    `created_time`         DATETIME(6)      NULL,
    `cron_workflow_id`     VARCHAR(50)      NULL,
    `execution_cycle`      VARCHAR(100)     NULL,
    `is_enable`            TINYINT(1)       NOT NULL DEFAULT '1',
    `latest_finished_at`   DATETIME(6)      NULL,
    `latest_started_at`    DATETIME(6)      NULL,
    `latest_status`        VARCHAR(50)      NULL,
    `latest_workflow_id`   VARCHAR(50)      NULL,
    `latest_workflow_name` VARCHAR(100)     NULL,
    `schedule_job_name`    VARCHAR(100)     NULL,
    `team`                 VARCHAR(10)      NULL,
    `updated_time`         DATETIME(6)      NULL,
    `workflow_template_ref` VARCHAR(100)    NULL,
    `is_deleted`           TINYINT(1)       NOT NULL DEFAULT '0',
    PRIMARY KEY (`job_id`),
    INDEX `idx_job` (`job_id`, `schedule_job_name`, `is_deleted`, `is_enable`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;