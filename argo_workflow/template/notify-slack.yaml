apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: notify-slack-template
  namespace: found
spec:
  serviceAccountName: found-batchsystem-sa
  arguments:
    parameters:
      - name: url
        valueFrom:
          configMapKeyRef:
            name: slack-config
            key: slack-url
  templates:
    - name: notify
      inputs:
        parameters:
          - name: workflowName
            value: default
      script:
        image: curlimages/curl:7.73.0
        command: [sh, -c]
        args:
          - |
            # tail -f /dev/null
            echo {{inputs.parameters.workflowName}}
            echo $SLACK_TOKEN
            curl -X POST $SLACK_URL \
              -H "Authorization: Bearer $SLACK_TOKEN" \
              -H "Content-Type: application/json" \
              -d '{
                    "channel": "team-eng-foundation-testing",
                    "text": "`{{inputs.parameters.workflowName}}`",
                    "attachments": [
                      {
                        "color": "#2eb886",
                        "blocks": [
                          {
                              "type": "section",
                              "text": {
                                  "type": "mrkdwn",
                                  "text": "*Argo Workflows*"
                              }
                          },
                          {
                              "type": "section",
                              "fields": [
                                  {
                                      "type": "mrkdwn",
                                      "text": "*Workflow:* `{{inputs.parameters.workflowName}}`"
                                  },
                                  {
                                      "type": "mrkdwn",
                                      "text": "*Status:* `Running`"
                                  },
                                  {
                                      "type": "mrkdwn",
                                      "text": "*Creator:* `system-serviceaccount-argo-admin`"
                                  },
                                  {
                                      "type": "mrkdwn",
                                      "text": "*WorkflowTemplate:* `hook-slack-bot-raw`"
                                  },
                                  {
                                      "type": "mrkdwn",
                                      "text": "*CreationTimestamp:* `2023-05-21T10:22:08Z`"
                                  },
                                  {
                                      "type": "mrkdwn",
                                      "text": "*Duration:* `0.000000s`"
                                  },
                                  {
                                      "type": "mrkdwn",
                                      "text": "*UID:* `5067d15c-957b-4f35-8aee-27bffe67a589`"
                                  }
                              ]
                          }
                        ]
                      }
                    ]
                  }'
        env:
          - name: SLACK_URL
            valueFrom:
              configMapKeyRef:
                name: slack-config
                key: slack-url
          - name: SLACK_TOKEN
            valueFrom:
              secretKeyRef:
                name: slack-token-secret
                key: token
        securityContext:
          runAsUser: 1000
          runAsGroup: 3000
          fsGroup: 2000
