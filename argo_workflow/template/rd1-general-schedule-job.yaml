apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  name: found-general-schedule-job
  namespace: rd1
  annotations:
    workflows.argoproj.io/description: |
      Shared CronWorkflow Process
    workflows.argoproj.io/version: ">= 3.2.0"
spec:
  serviceAccountName: rd1-batchsystem-sa
  arguments:
    parameters:
    - name: templateName
      value: default
    - name: entryPoint
      value: default
  schedule: "* */12 * * *"
  workflowSpec:
    workflowTemplateRef:
      name: found-batchsystem-main-cron-workflow-template
    arguments:
      parameters:
      - name: jobName
        value: found-general-schedule-job
      - name: templateName
        value: found-workflow-template-update-job-status
      - name: jobData
        value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInVwZGF0ZXN0YXR1c2pvYiIgfQ==
      - name: priority
        value: schedule
      - name: team
        value: found
