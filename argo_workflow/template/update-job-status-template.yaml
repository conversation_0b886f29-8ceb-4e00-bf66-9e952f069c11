apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: found-workflow-template-update-job-status
  namespace: found
spec:
  serviceAccountName: found-sa
  arguments:
    parameters:
    - name: jobData
      value: default
  templates:
  - name: main
    inputs:
      parameters:
      - name: jobData
    container:
      image: nexus.jkopay.app:5001/found/jkopay.batchsystem.console:0.0.0.21
      args: ["{{workflow.uid}}", "{{workflow.name}}", "{{workflow.namespace}}", "{{inputs.parameters.jobData}}"]
      env: # 新增 env 部分
      - name: NETCORE_ENVIRONMENT
        value: development
      - name: IS_BASE64
        value: "true"
      - name: KAFKA_USERNAME
        valueFrom:
          secretKeyRef:
            name: kafka-user
            key: user
      - name: KAFKA_PASSWORD
        valueFrom:
          secretKeyRef:
            name: kafka-user
            key: password
#       retryStrategy:
#         steps: 1