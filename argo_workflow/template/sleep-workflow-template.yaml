apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: sleep-workflow-template
  namespace: rd1
spec:
  serviceAccountName: rd1-argo-sa
  templates:
  - name: main
    container:
      image: alpine:3.12 # 您可以根据需要更改为其他镜像
      command: ["/bin/sh", "-c"]
      args: ["echo 'Sleeping for 5 minutes...'; sleep 300"]
      env: # 新增 env 部分
      - name: NETCORE_ENVIRONMENT
        value: development
      - name: IS_BASE64
        value: "true"
