---
apiVersion: argoproj.io/v1alpha1
kind: EventSource
metadata:
  name: sftp-eventsource
  namespace: argo
spec:
  eventBusName: eventbus
  template:
    container:
      volumeMounts:
        - mountPath: /upload
          name: upload
    volumes:
      - name: upload
        emptyDir: {}
  sftp:
    example:
      username:
        key: username
        name: uat-sftp
      password:
        key: password
        name: uat-sftp
      address:
        key: address
        name: uat-sftp
      watchPathConfig:
        # directory to watch
        directory: /upload
        pathRegexp: "([a-zA-Z0-9]+).yaml"
      pollIntervalDuration: s10
      eventType: CREATE
