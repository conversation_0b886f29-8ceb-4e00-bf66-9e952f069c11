---
apiVersion: argoproj.io/v1alpha1
kind: EventSource
metadata:
  name: sftp-eventsource
spec:
  template:
    container:
      volumeMounts:
        - mountPath: /upload
          name: upload
    volumes:
      - name: upload
        emptyDir: {}
  sftp:
    sftp-example:
      username:
        key: username
        name: uat-sftp
      password:
        key: password
        name: uat-sftp
      address:
        key: address
        name: uat-sftp
      watchPathConfig:
        # directory to watch
        directory: /upload
        # path to watch
        path: deployment.yaml
        # pathRegexp: "([a-zA-Z0-9]+).yaml"
      pollIntervalDuration: s10
      # type of the event
      # supported types are: CREATE, REMOVE
      eventType: CREATE
---
apiVersion: argoproj.io/v1alpha1
kind: Sensor
metadata:
  name: sftp-sensor
spec:
  template:
    serviceAccountName: operate-workflow-sa
  dependencies:
    - name: test-dep-sftp
      eventSourceName: sftp-eventsource
      eventName: sftp-example
  triggers:
    - template:
        name: sftp-workflow-trigger
        k8s:
          operation: create
          source:
            resource:
              apiVersion: argoproj.io/v1alpha1
              kind: Workflow
              metadata:
                generateName: sftp-watcher-
              spec:
                entrypoint: whalesay
                templates:
                  -
                    container:
                      args:
                        - "hello" # it will get replaced by the event payload
                      command:
                        - cowsay
                      image: "docker/whalesay:latest"
                    name: whalesay
          parameters:
            - src:
                dependencyName: test-dep-sftp
                dataKey: name
              dest: spec.templates.0.container.args.0
      retryStrategy:
        steps: 3
