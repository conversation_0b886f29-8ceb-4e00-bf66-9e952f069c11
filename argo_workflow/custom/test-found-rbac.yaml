---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: testfound-sa
  namespace: testfound
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: testfound-rb
  namespace: testfound
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: admin
subjects:
- kind: ServiceAccount
  name: testfound-sa
  namespace: testfound
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: patch-workflowtasksets
  namespace: testfound
rules:
- apiGroups:
  - argoproj.io
  resources:
  - workflowtasksets/status
  # required to GC the artifacts
  - workflowartifactgctasks
  - workflowartifactgctasks/status
  verbs:
  - get
  - list
  - watch
  - patch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: testfound-rb-argo
  namespace: testfound
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: patch-workflowtasksets
subjects:
  - kind: ServiceAccount
    name: testfound-sa
    namespace: testfound

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: read-secret-cr
rules:
  - apiGroups:
      - ""
    resources:
      - secrets
    verbs:
      - get
      - list
      - watch
    resourceNames:
      - argo-workflows-agent-ca-certificates

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: argo-read-secret
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: read-secret-cr
subjects:
  - kind: ServiceAccount
    name: argo
    namespace: argo