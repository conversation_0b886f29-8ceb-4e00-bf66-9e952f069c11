---
apiVersion: v1
kind: Namespace
metadata:
  name: found
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: found-sa
  namespace: found
secrets:
  - apiVersion: v1
    kind: Secret
    name: found-sa-token
    namespace: found
---
apiVersion: v1
kind: Secret
metadata:
  name: found-sa-token
  namespace: found
  annotations:
    kubernetes.io/service-account.name: found-sa
type: kubernetes.io/service-account-token

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: found-rb
  namespace: found
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: admin
subjects:
  - kind: ServiceAccount
    name: found-sa
    namespace: found

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: patch-workflowtasksets
  namespace: found
rules:
  - apiGroups:
      - argoproj.io
    resources:
      - workflowtasksets/status
      # required to GC the artifacts
      - workflowartifactgctasks
      - workflowartifactgctasks/status
    verbs:
      - get
      - list
      - watch
      - patch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: found-rb-argo
  namespace: found
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: patch-workflowtasksets
subjects:
  - kind: ServiceAccount
    name: found-sa
    namespace: found

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: read-secret-cr
rules:
  - apiGroups:
      - ""
    resources:
      - secrets
    verbs:
      - get
      - list
      - watch
    resourceNames:
      - argo-workflows-agent-ca-certificates

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: argo-read-secret
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: read-secret-cr
subjects:
  - kind: ServiceAccount
    name: argo
    namespace: argo

---
# Similarly you can use a ClusterRole and ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: operate-workflow-role
  namespace: found
rules:
  - apiGroups:
      - argoproj.io
    verbs:
      - "*"
    resources:
      - workflows
      - workflowtemplates
      - cronworkflows
      - clusterworkflowtemplates

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: operate-workflow-role-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: operate-workflow-role
subjects:
  - kind: ServiceAccount
    name: found-sa

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: executor
rules:
  - apiGroups:
      - argoproj.io
    resources:
      - workflowtaskresults
    verbs:
      - create
      - patch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: executor-argo
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: executor
subjects:
  - kind: ServiceAccount
    name: argo
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: found
  name: workflowtaskset-status-patcher
rules:
  - apiGroups:
      - argoproj.io
    resources:
      - workflowtasksets/status
    verbs:
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: workflowtaskset-status-patcher-binding
  namespace: found
subjects:
  - kind: ServiceAccount
    name: argo
    namespace: argo
roleRef:
  kind: Role
  name: workflowtaskset-status-patcher
  apiGroup: rbac.authorization.k8s.io
