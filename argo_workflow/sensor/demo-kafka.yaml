apiVersion: argoproj.io/v1alpha1
kind: Sensor
metadata:
  name: kafka
  namespace: argo
spec:
  # eventBusName: eventbus
  eventBusName: default
  template:
    serviceAccountName: argo
  dependencies:
    - name: test-dep-kafka
      eventSourceName: kafka
      eventName: example
  triggers:
    - template:
        name: kafka-workflow-trigger
        k8s:
          operation: create
          source:
            resource:
              apiVersion: argoproj.io/v1alpha1
              kind: Workflow
              metadata:
                generateName: kafka-workflow-
                namespace: found
                serviceAccountName: found-sa
              spec:
                entrypoint: whalesay
                arguments:
                  parameters:
                    - name: message
                      value: hello world
                    - name: header
                      value: default vaule
                    - name: id
                      value: ""
                templates:
                  - name: whalesay
                    inputs:
                      parameters:
                        - name: message
                        - name: header
                        - name: id
                    container:
                      image: nexus.jkopay.app:5001/found/argo-hellodotnet:1.5
                      # image: jamisliao/argo_sample:1.3
                      args: ["{{inputs.parameters.header}}","{{inputs.parameters.message}}","{{inputs.parameters.id}}"]
          parameters:
            - src:
                dependencyName: test-dep-kafka
                dataKey: body
              dest: spec.arguments.parameters.0.value
            - src:
                dependencyName: test-dep-kafka
                dataKey: headers.owner
              dest: spec.arguments.parameters.1.value
            - src:
                dependencyName: test-dep-kafka
                contextKey: id
              dest: spec.arguments.parameters.2.value