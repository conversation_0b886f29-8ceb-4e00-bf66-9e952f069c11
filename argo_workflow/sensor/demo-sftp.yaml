---
apiVersion: argoproj.io/v1alpha1
kind: Sensor
metadata:
  name: sftp-sensor
spec:
  dependencies:
    - name: dependency-1
      eventSourceName: sftp-eventsource
      eventName: example
  triggers:
    - template:
        name: workflow-trigger-1
        k8s:
          source:
            resource:
              apiVersion: argoproj.io/v1alpha1
              kind: Workflow
              metadata:
                generateName: workflow-from-sftp-sensor-
              spec:
                entrypoint: main
                arguments:
                  parameters:
                    - name: message
                      value: hello world
                templates:
                  - name: main
                    inputs:
                      parameters:
                        - name: message
                    container:
                      # image: argoproj/argosay:v2
                      # image: nexus.jkopay.app:5001/found/argo-hellodotnet:1.6
                      image: jamisliao/argo_sample:1.6
                      args: ["{{inputs.parameters.message}}"]
          parameters:
            - src:
                dependencyName: dependency-1
                dataKey: name
              dest: spec.arguments.parameters.0.value
          operation: create
  template:
    # serviceAccountName: operate-workflow-sa
    serviceAccountName: found-sa
