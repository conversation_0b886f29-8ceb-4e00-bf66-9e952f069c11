---
apiVersion: argoproj.io/v1alpha1
kind: Sensor
metadata:
  name: kafka
  namespace: argo
spec:
  # eventBusName: eventbus
  eventBusName: default
  template:
    serviceAccountName: argo
  dependencies:
    - name: test-dep-kafka
      eventSourceName: kafka
      eventName: example
  triggers:
    - template:
        name: http-trigger
        http:
          url: https://www.google.com
          method: GET
        retryStrategy:
          steps: 3
          duration: 3s
    - template:
        name: multi-trigger-workflow-1
        k8s:
          operation: create
          source:
            resource:
              apiVersion: argoproj.io/v1alpha1
              kind: Workflow
              metadata:
                generateName: hello-world-
              spec:
                entrypoint: hello-hello
                serviceAccountName: argo
                templates:
                  - name: hello-hello
                    steps:
                      - - name: hello1
                          template: whalesay
                          arguments:
                            parameters:
                              - name: message
                                value: "hello1"
                      - - name: hello2
                          template: whalesay
                          arguments:
                            parameters:
                              - name: message
                                value: "hello2"
                  - container:
                      args:
                        - "hello world"
                      command:
                        - cowsay
                      image: docker/whalesay:latest
                    name: whalesay
