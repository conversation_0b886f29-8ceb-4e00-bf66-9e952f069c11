apiVersion: argoproj.io/v1alpha1
kind: Sensor
metadata:
  name: webhook-sensor
  namespace: argo-events
spec:
  dependencies:
    - name: example
      eventSourceName: webhook
      eventName: example
  triggers:
    - template:
        name: trigger-workflow
        k8s:
          group: argoproj.io
          version: v1alpha1
          resource: workflows
          operation: create
          source:
            resource:
              apiVersion: argoproj.io/v1alpha1
              kind: Workflow
              metadata:
                generateName: sample-workflow-
                namespace: argo
              spec:
                entrypoint: main
                workflowTemplateRef:
                  name: sample-workflow-template
