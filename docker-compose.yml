﻿services:
  api:
    image: jkopay.batchsystem.api
    build:
      context: .
      dockerfile: src/JKOPay.BatchSystem.Api/Dockerfile
    ports:
      - 8080:8080
    environment:
      - RedisSettings__Host=redis
      - ConnectionStrings__BatchMaster=Server=mysql;Database=batchsystem;User ID=batchuser;Password=batchpw;
#   redis:
#     restart: always
#     image: redis:7.2.4
#     ports:
#       - 6379:6379
  mysql:
    restart: always
    image: mysql:8.3.0
    ports:
      - 3306:3306
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=batchsystem
      - MYSQL_USER=batchuser
      - MYSQL_PASSWORD=batchpw