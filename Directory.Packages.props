<?xml version="1.0" encoding="utf-8"?>
<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageVersion Include="Confluent.Kafka" Version="2.5.3" />
    <PackageVersion Include="JKOPay.Platform.BatchSystem" Version="0.0.13" />
    <PackageVersion Include="JKOPay.Platform.HttpClientExtension" Version="1.1.0" />
    <PackageVersion Include="JKOPay.Platform.LoggingExtension" Version="1.1.0" />
    <PackageVersion Include="JKOPay.Platform.OpenTelemetry" Version="1.0.1" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.3" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="8.0.8" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.8" />
    <PackageVersion Include="Microsoft.Net.Compilers.Toolset" Version="4.12.0" />
    <PackageVersion Include="NetEscapades.Configuration.Yaml" Version="3.1.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.2" />
    <PackageVersion Include="Serilog" Version="4.2.0" />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageVersion Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageVersion Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.OpenTelemetry" Version="4.1.1" />
    <PackageVersion Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.7.3" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Filters" Version="8.0.2" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="9.0.0" />
    <PackageVersion Include="FluentAssertions" Version="6.12.1" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.8" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageVersion Include="NSubstitute" Version="5.1.0" />
    <PackageVersion Include="RichardSzalay.MockHttp" Version="7.0.0" />
    <PackageVersion Include="xunit" Version="2.9.0" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="KubernetesClient" Version="16.0.2" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageVersion Include="OpenTelemetry" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.Console" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.Prometheus.HttpListener" Version="1.9.0-beta.2" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Runtime" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.9.0-beta.2" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.EntityFrameworkCore" Version="1.0.0-beta.12" />
  </ItemGroup>
</Project>