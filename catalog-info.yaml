apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  title: Batch System API
  name: batch-system-api
  namespace: jkopay
  description: The Batch System Service API allows you to create workflow.
spec:
  type: openapi
  lifecycle: production
  owner: group:default/group-foundation-aad
  definition:
    $text: ./openapi.json

---
apiVersion: jko.backstage/v1alpha1
kind: Component
metadata:
  namespace: jkopay
  name: foundation-jkopay.batchsystem
  title: jkopay.batchsystem (jkopay)
  description: batchsystem
  annotations:
    gitlab.com/project-id: "586"
    gitlab.com/instance: gitlab.jkopay.app
    jko.backstage/gitlab-url: https://gitlab.jkopay.app/found/jkopay.batchsystem
    argocd/app-selector: team=foundation,app=jkopay.batchsystem,company=jkopay
    backstage.io/kubernetes-label-selector: team=foundation,app=jkopay.batchsystem,company=jkopay
    jko.backstage/swarmpit-svc-selector: team=foundation,app=jkopay.batchsystem,company=jkopay
  labels:
    app: jkopay.batchsystem
    company: jkopay
    namespace: foundation
    team: foundation
    module: ""
    product: ""
  language:
    name: ""
    framework: ""
    version: ""
spec:
  type: application
  owner: group:default/group-foundation-aad
  lifecycle: production
  serviceRoles:
    - app
  environments:
    - prod
    - sit
  locations:
    - idc
  dependsOn: []

---
apiVersion: jko.backstage/v1alpha1
kind: Component
metadata:
  namespace: jkopay
  name: foundation-jkopay.batchsystem-app-prod-idc
  title: jkopay.batchsystem-app (jkopay-prod-idc)
  description: batchsystem
  annotations:
    gitlab.com/project-id: "586"
    gitlab.com/instance: gitlab.jkopay.app
    argocd/app-selector: team=foundation,app=jkopay.batchsystem,company=jkopay,location=idc,environment=prod
    backstage.io/kubernetes-label-selector: team=foundation,app=jkopay.batchsystem,company=jkopay,serviceRole=app,location=idc,environment=prod
  labels:
    app: jkopay.batchsystem
    team: foundation
    serviceRole: app
    company: jkopay
    namespace: foundation
    module: ""
    product: ""
    environment: prod
    location: idc
  links: []
  details:
    criticality: C
    SLO: 90
    confidentiality: Internal
    monitoring: false
    stateless: false
    promethues: false
    vault: false
  information: {}
  language:
    name: ""
    framework: ""
    version: ""
spec:
  type: service
  owner: group:default/group-foundation-aad
  lifecycle: production
  subcomponentOf: component:jkopay/foundation-jkopay.batchsystem
  dependsOn:
    - Resource:default/argo_workflow
  providesApis:
    - jkopay/batch-system-api
  consumesApis: []
  runningOn:
    - resource:jkopay/kubernetes-admin_prod-jkopay

---
apiVersion: jko.backstage/v1alpha1
kind: Component
metadata:
  namespace: jkopay
  name: foundation-jkopay.batchsystem-app-sit-idc
  title: jkopay.batchsystem-app (jkopay-sit-idc)
  description: batchsystem
  annotations:
    gitlab.com/project-id: "586"
    gitlab.com/instance: gitlab.jkopay.app
    argocd/app-selector: team=foundation,app=jkopay.batchsystem,company=jkopay,location=idc,environment=sit
    backstage.io/kubernetes-label-selector: team=foundation,app=jkopay.batchsystem,company=jkopay,serviceRole=app,location=idc,environment=sit
  labels:
    app: jkopay.batchsystem
    team: foundation
    serviceRole: app
    company: jkopay
    namespace: foundation
    module: ""
    product: ""
    environment: sit
    location: idc
  links: []
  details:
    criticality: C
    SLO: 90
    confidentiality: Internal
    monitoring: false
    stateless: false
    promethues: false
    vault: false
  information: {}
  language:
    name: ""
    framework: ""
    version: ""
spec:
  type: service
  owner: group:default/group-foundation-aad
  lifecycle: production
  subcomponentOf: component:jkopay/foundation-jkopay.batchsystem
  dependsOn:
    - Resource:default/argo_workflow
  providesApis:
    - jkopay/batch-system-api
  consumesApis: []
  runningOn:
    - resource:jkopay/kubernetes-admin_application-01