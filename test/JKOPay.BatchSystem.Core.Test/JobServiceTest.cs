// ReSharper disable MethodHasAsyncOverload
using FluentAssertions;
using JKOPay.BatchSystem.Core.Models;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Models.Argo;
using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;
using JKOPay.BatchSystem.Core.Models.Core;
using JKOPay.BatchSystem.Core.Models.Job;
using JKOPay.BatchSystem.Core.Services;
using JKOPay.BatchSystem.Core.Test.Mocks;
using JKOPay.BatchSystem.Core.Utilities;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;

namespace JKOPay.BatchSystem.Core.Test;

public class JobServiceTest
{
    private static readonly string ExpectedGuid = "8d275755-69a8-4f32-ae4f-f88ed3641588";
    private const string ExpectedWorkflowName = "found-unittest-72ae8d564d7a49b2a6790690b07b22b3";
    private static readonly string[] Predicate = ["unit-test-job"];

    [Fact]
    public async Task Job_Successfully_Created_Priority_Immediate()
    {
        var dbContext = CreateStubDbContext(out var jobRecordDbSet, out var workflowDbSet);

        var logger = Substitute.For<ILogger<JobService>>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        argoUtility.SubmitWorkflowAsync(Arg.Any<SubmitRequest>()).Returns(new QueryResult<SubmitResponse>
        {
            IsSuccess = true,
            Data = new SubmitResponse
            {
                Metadata = new Metadata
                {
                    Uid = ExpectedGuid.ToString(),
                    Name = ExpectedWorkflowName,
                },
                IsSucceed = true
            }
        });

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var sourceJob = new CreateJob
        {
            JobName = "FoundTest",
            ServiceNamespace = "Foundation",
            Priority = JobPriorityEnum.Immediate,
            WorkflowTemplateName = "unit-test-workflow-template",
            Message = new Dictionary<string, object>()
        };

        var result = await target.CreateJobAsync(sourceJob);

        var time = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime;
        var expectedJob = new JobRecord()
        {
            JobId = ExpectedGuid,
            Priority = JobPriorityEnum.Immediate.ToString(),
            WorkflowName = "found-unittest-72ae8d564d7a49b2a6790690b07b22b3",
            WorkflowId = "8D275755-69A8-4F32-AE4F-F88ED3641588",
            WorkflowTemplateName = "unit-test-workflow-template",
            Team = "Foundation",
            CreatedTime = time,
            UpdatedTime = time,
            Status = JobStatusEnum.New.ToString(),
        };

        var expectedSubmitRequest = new SubmitRequest(
            sourceJob.ServiceNamespace,
            sourceJob.JobName,
            $"dev-{sourceJob.ServiceNamespace}-batchsystem-workflow-template",
            "main",
            sourceJob.Message,
            new Dictionary<string, string>
            {
                { "team", sourceJob.ServiceNamespace }
            });

        await argoUtility.Received()
            .SubmitWorkflowAsync(Arg.Is<SubmitRequest>(sr => sr.Namespace == expectedSubmitRequest.Namespace
                                                             && sr.ResourceName == expectedSubmitRequest.ResourceName
                                                             && sr.SubmitOptions.Parameters.First(p =>
                                                                 p == $"templateName=dev-{sourceJob.WorkflowTemplateName}") == "templateName=dev-unit-test-workflow-template"
                                                             && sr.SubmitOptions.EntryPoint == "main"));
        jobRecordDbSet.Received().Add(Arg.Is<JobRecord>(job => job.JobId == expectedJob.JobId
                                                               && job.Priority == expectedJob.Priority
                                                               && job.Team == expectedJob.Team
                                                               && job.Status == "New"
                                                               && job.WorkflowTemplateName ==
                                                               expectedJob.WorkflowTemplateName));
        workflowDbSet.Received().Add(Arg.Is<Workflow>(wf =>
            wf.WorkflowId!.Equals(expectedJob.WorkflowId, StringComparison.CurrentCultureIgnoreCase)
            && wf.WorkflowName == expectedJob.WorkflowName));
        await dbContext.Received().SaveChangesAsync();
        result.ResultObject["JobId"].Should().BeEquivalentTo(ExpectedGuid.ToString());
        Assert.Equal("0001", result.Result);
    }

    [Fact]
    public async Task Job_Successfully_Created_Priority_Priority()
    {
        var dbContext = CreateStubDbContext(out _, out _);

        var logger = Substitute.For<ILogger<JobService>>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        argoWorkflowService.SubmitWorkflowAsync(Arg.Any<CreateJob>(), Arg.Any<string>()).Returns(
            new ResponseModel<SubmitResponse>
            {
                Result = "0001",
                ResultObject = new SubmitResponse
                {
                    Metadata = new Metadata
                    {
                        Uid = ExpectedGuid.ToString(),
                        Name = ExpectedWorkflowName,
                    },
                    IsSucceed = true
                }
            });

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var sourceJob = new CreateJob
        {
            JobName = "FoundTest",
            ServiceNamespace = "Found",
            Priority = JobPriorityEnum.Priority,
            WorkflowTemplateName = "unit-test-workflow-template",
            Message = new Dictionary<string, object>()
        };

        await target.CreateJobAsync(sourceJob);
        var expectedWorkflow = new Workflow
        {
            WorkflowName = ExpectedWorkflowName,
            Status = "Pending"
        };

        dbContext.Workflows.Received().Add(Arg.Is<Workflow>(wf => wf.WorkflowName == expectedWorkflow.WorkflowName
                                                                  && wf.Status == expectedWorkflow.Status));
    }

    [Fact]
    public async Task Job_Failed_To_Create_Use_ArgoWorkflow_API()
    {
        var dbContext = CreateStubDbContext(out _, out _);

        var logger = Substitute.For<ILogger<JobService>>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        argoUtility.SubmitWorkflowAsync(Arg.Any<SubmitRequest>()).Returns(new QueryResult<SubmitResponse>
        {
            IsSuccess = false,
            Data = new SubmitResponse
            {
                Metadata = new Metadata
                {
                    Uid = ExpectedGuid,
                    Name = ExpectedWorkflowName,
                },
                IsSucceed = false
            },
            ErrorMessage = "Submit workflow failed"
        });

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var sourceJob = new CreateJob
        {
            JobName = "FoundTest",
            ServiceNamespace = "Found",
            Priority = JobPriorityEnum.Immediate,
            WorkflowTemplateName = "unit-test-workflow-template",
            Message = new Dictionary<string, object>()
        };

        var result = await target.CreateJobAsync(sourceJob);
        var expectedResponseModel = new ResponseModel<Dictionary<string, string>>
        {
            ResultCode = ResultCode.BatchJobEx0002CreateFailed,
            Message = "Submit workflow failed"
        };

        result.Should().BeEquivalentTo(expectedResponseModel);
    }

    // test for JobService.CreateJobAsync(CreateScheduleJob sourceJob)
    [Fact]
    public async Task Job_Successfully_Created_JobRecord_From_CreateScheduleJob()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        InitScheduleJob(dbContext, [
            new ScheduleJob
            {
                JobId = 1,
                IsEnable = true,
                IsDeleted = false,
                ScheduleJobName = "dev-unit-test-job",
            }
        ]);


        var logger = Substitute.For<ILogger<JobService>>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        argoUtility.SubmitWorkflowAsync(Arg.Any<SubmitRequest>()).Returns(new QueryResult<SubmitResponse>
        {
            IsSuccess = true,
            Data = new SubmitResponse
            {
                Metadata = new Metadata
                {
                    Uid = ExpectedGuid,
                    Name = ExpectedWorkflowName,
                },
                IsSucceed = true
            }
        });

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var sourceJob = new CreateScheduleJob
        {
            JobName = "unit-test-job",
            WorkflowId = ExpectedGuid,
            WorkflowName = "unit-test-workflow",
            WorkflowTemplateName = "unit-test-workflow-template",
            Priority = JobPriorityEnum.Schedule,
            Team = "found"
        };

        var actual = await target.CreateJobAsync(sourceJob);

        var time = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime;
        var expectedJob = new JobRecord()
        {
            Id = 1,
            JobId = ExpectedGuid,
            JobName = "",
            Priority = JobPriorityEnum.Schedule.ToString(),
            WorkflowName = "unit-test-workflow",
            WorkflowId = "8d275755-69a8-4f32-ae4f-f88ed3641588",
            WorkflowTemplateName = "unit-test-workflow-template",
            Team = "found",
            CreatedTime = time,
            UpdatedTime = time,
            Status = JobStatusEnum.New.ToString(),
        };

        var actualJobRecord = dbContext.JobRecords.FirstOrDefault(j => j.JobId == ExpectedGuid);
        actualJobRecord.Should().BeEquivalentTo(expectedJob);
        actual.ResultObject.Should().BeEquivalentTo(new
        {
            JobId = ExpectedGuid
        });
        Assert.Equal("0001", actual.Result);
    }

    [Fact]
    public async Task Job_Failed_Created_JobRecord_From_CreateScheduleJob()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        // InitScheduleJob(dbContext, [
        //     new ScheduleJob
        //     {
        //         JobId = 1,
        //         IsEnable = true,
        //         IsDeleted = false,
        //         ScheduleJobName = "dev-unit-test-job",
        //     }
        // ]);

        var logger = Substitute.For<ILogger<JobService>>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        argoUtility.SubmitWorkflowAsync(Arg.Any<SubmitRequest>()).Returns(new QueryResult<SubmitResponse>
        {
            IsSuccess = true,
            Data = new SubmitResponse
            {
                Metadata = new Metadata
                {
                    Uid = ExpectedGuid.ToString(),
                    Name = ExpectedWorkflowName,
                },
                IsSucceed = true
            }
        });

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var sourceJob = new CreateScheduleJob
        {
            WorkflowName = "unit-test-workflow",
            WorkflowTemplateName = "unit-test-workflow-template",
            Priority = JobPriorityEnum.Schedule,
            JobName = "unit-test-job",
            Team = "found"
        };

        var actual = await target.CreateJobAsync(sourceJob);

        var time = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime;
        var expectedJob = new JobRecord()
        {
            Id = 1,
            JobId = ExpectedGuid,
            JobName = "",
            Priority = JobPriorityEnum.Schedule.ToString(),
            WorkflowName = "unit-test-workflow",
            WorkflowTemplateName = "unit-test-workflow-template",
            Team = "found",
            CreatedTime = time,
            UpdatedTime = time,
            Status = JobStatusEnum.New.ToString(),
        };

        var actualJobRecord = dbContext.JobRecords.FirstOrDefault(j => j.JobId == ExpectedGuid);
        actualJobRecord.Should().BeNull();
        actual.ResultObject.Should().BeEquivalentTo(new
        {
            JobId = ExpectedGuid
        });

        Assert.Equal("Batch-ScheduleJob-Ex-0002", actual.Result);
    }

    // Test for JobService.CreateJobAsync(string @namespace, string jobId)
    [Fact]
    public async Task JobRecord_Successfully_Created_With_Namespace_And_JobId()
    {
        var jobRecordsList = new List<JobRecord>
        {
            new JobRecord
            {
                // create a job with the same job id
                JobId = ExpectedGuid,
                JobName = "",
                Priority = JobPriorityEnum.Immediate.ToString(),
                WorkflowName = "found-unittest-72ae8d564d7a49b2a6790690b07b22b3",
                WorkflowId = "8D275755-69A8-4F32-AE4F-F88ED3641588",
                WorkflowTemplateName = "unit-test-workflow-template",
                Team = "found",
                CreatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
                UpdatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
                Status = JobStatusEnum.New.ToString()
            }
        };

        var dbContext = CreateInMemoryBatchDbContext();
        InitJobRecord(dbContext, jobRecordsList);

        var logger = Substitute.For<ILogger<JobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var @namespace = "Found";
        var jobId = ExpectedGuid.ToString();
        var result = await target.UpdateJobAsync(@namespace, jobId);

        var time = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime;
        var expectedJob = new JobRecord()
        {
            Id = 1,
            JobName = "",
            JobId = ExpectedGuid,
            Priority = JobPriorityEnum.Immediate.ToString(),
            WorkflowName = "found-unittest-72ae8d564d7a49b2a6790690b07b22b3",
            WorkflowId = "8D275755-69A8-4F32-AE4F-F88ED3641588",
            WorkflowTemplateName = "unit-test-workflow-template",
            Team = "found",
            CreatedTime = time,
            UpdatedTime = time,
            Status = JobStatusEnum.Created.ToString(),
        };

        var actualJobRecord = dbContext.JobRecords.FirstOrDefault(j => j.JobId == ExpectedGuid);
        actualJobRecord.Should().BeEquivalentTo(expectedJob);
        result.Should().BeEquivalentTo(new ResponseModel<object>
        {
            Result = "0001",
            Message = "Job record executed successfully."
        });

        Assert.Equal("0001", result.Result);
    }

    [Fact]
    public async Task JobRecord_Failed_Created_With_Namespace_And_JobId_When_JobRecord_Not_Exist()
    {
        var dbContext = CreateInMemoryBatchDbContext();

        var logger = Substitute.For<ILogger<JobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var @namespace = "Found";
        var jobId = ExpectedGuid.ToString();
        var result = await target.UpdateJobAsync(@namespace, jobId);

        result.Should().BeEquivalentTo(new ResponseModel<object>
        {
            ResultCode = ResultCode.BatchJobEx0002JobNotFound,
            Message = jobId
        });
    }

    [Fact]
    public async Task JobRecord_Update_To_Canceled_Successfully_When_Job_Status_New()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        var logger = Substitute.For<ILogger<JobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var existJobRecord = new JobRecord
        {
            JobId = ExpectedGuid,
            JobName = "unit-test-job",
            Priority = JobPriorityEnum.Immediate.ToString(),
            WorkflowName = "found-unittest-72ae8d564d7a49b2a6790690b07b22b3",
            WorkflowId = "8D275755-69A8-4F32-AE4F-F88ED3641588",
            WorkflowTemplateName = "unit-test-workflow-template",
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
            Status = JobStatusEnum.New.ToString()
        };

        dbContext.JobRecords.Add(existJobRecord);
        dbContext.SaveChanges();

        await target.CanceledJobAsync("found", ExpectedGuid);
        var actual = dbContext.JobRecords.First(p => p.JobName == "unit-test-job");
        var expectedJobRecord = new JobRecord
        {
            Id = 1,
            JobId = ExpectedGuid,
            JobName = "unit-test-job",
            Priority = JobPriorityEnum.Immediate.ToString(),
            WorkflowName = "found-unittest-72ae8d564d7a49b2a6790690b07b22b3",
            WorkflowId = "8D275755-69A8-4F32-AE4F-F88ED3641588",
            WorkflowTemplateName = "unit-test-workflow-template",
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
            Status = JobStatusEnum.Canceled.ToString()
        };

        actual.Should().BeEquivalentTo(expectedJobRecord);
    }

    [Fact]
    public async Task JobRecord_Update_To_Canceled_Successfully_When_Job_Status_WorkflowStarted()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        var logger = Substitute.For<ILogger<JobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();
        argoWorkflowService.TerminateWorkflowAsync(Arg.Any<string>(), ExpectedWorkflowName).Returns(new ResponseModel<object>
        {
            Result = "0001",
            Message = "Workflow terminated successfully."
        });

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var existJobRecord = new JobRecord
        {
            JobId = ExpectedGuid,
            JobName = "unit-test-job",
            Priority = JobPriorityEnum.Immediate.ToString(),
            WorkflowName = "found-unittest-72ae8d564d7a49b2a6790690b07b22b3",
            WorkflowId = "8D275755-69A8-4F32-AE4F-F88ED3641588",
            WorkflowTemplateName = "unit-test-workflow-template",
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
            Status = JobStatusEnum.WorkflowStarted.ToString()
        };

        dbContext.JobRecords.Add(existJobRecord);
        dbContext.SaveChanges();

        await target.CanceledJobAsync("found", ExpectedGuid);
        var actual = dbContext.JobRecords.First(p => p.JobName == "unit-test-job");
        var expectedJobRecord = new JobRecord
        {
            Id = 1,
            JobId = ExpectedGuid,
            JobName = "unit-test-job",
            Priority = JobPriorityEnum.Immediate.ToString(),
            WorkflowName = "found-unittest-72ae8d564d7a49b2a6790690b07b22b3",
            WorkflowId = "8D275755-69A8-4F32-AE4F-F88ED3641588",
            WorkflowTemplateName = "unit-test-workflow-template",
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
            Status = JobStatusEnum.Canceled.ToString()
        };

        actual.Should().BeEquivalentTo(expectedJobRecord);
        await argoWorkflowService.Received().TerminateWorkflowAsync(Arg.Is<string>(p => p == "found"),
            Arg.Is<string>(p => p == ExpectedWorkflowName));
    }



    [Fact]
    public async Task Update_JobRecord_Status_Successfully()
    {
       var dbContext = CreateInMemoryBatchDbContext();
        var logger = Substitute.For<ILogger<JobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var existJobRecord = new JobRecord
        {
            JobId = ExpectedGuid,
            JobName = "unit-test-job",
            Priority = JobPriorityEnum.Immediate.ToString(),
            WorkflowName = "found-unittest-72ae8d564d7a49b2a6790690b07b22b3",
            WorkflowId = ExpectedGuid.ToString(),
            WorkflowTemplateName = "unit-test-workflow-template",
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
            Status = JobStatusEnum.New.ToString()
        };

        dbContext.JobRecords.Add(existJobRecord);
        dbContext.SaveChanges();

        await target.UpdateJobStatusAsync(new UpdateJob
        {
            JobId = ExpectedGuid.ToString(),
            Status = JobStatusEnum.WorkflowStarted.ToString(),
            WorkflowId = ExpectedGuid.ToString(),
            Priority = JobPriorityEnum.Immediate,
            WorkflowName = "test-workflow",
        });
        var actual = dbContext.JobRecords.First(p => p.WorkflowId == existJobRecord.WorkflowId);
        var expectedJobRecord = new JobRecord
        {
            Id = 1,
            JobId = ExpectedGuid,
            JobName = "unit-test-job",
            Priority = JobPriorityEnum.Immediate.ToString(),
            WorkflowName = "test-workflow",
            WorkflowId = ExpectedGuid.ToString().ToLower(),
            WorkflowTemplateName = "unit-test-workflow-template",
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
            Status = JobStatusEnum.WorkflowStarted.ToString()
        };

        actual.Should().BeEquivalentTo(expectedJobRecord);
    }

    [Fact]
    public async Task Update_JobRecord_Status_Failed_Status_Parser_Error()
    {
       var dbContext = CreateInMemoryBatchDbContext();
        var logger = Substitute.For<ILogger<JobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var result = await target.UpdateJobStatusAsync(new UpdateJob
        {
            JobId = ExpectedGuid,
            Status = "test",
            WorkflowId = ExpectedGuid,
            Priority = JobPriorityEnum.Immediate,
            WorkflowName = "test-workflow",
        });

        logger.Received().Log(
            LogLevel.Error,
            0,
            Arg.Is<object>(v => v.ToString() == "Input status error: test"),
            null,
            Arg.Any<Func<object, Exception, string>>()!
        );
        result.Should().BeEquivalentTo(new ResponseModel<Dictionary<string, string>>
            {
                ResultCode = ResultCode.BatchJobEx0002UpdateFailed,
                Message = ExpectedGuid.ToLower()
            });
    }

    [Fact]
    public async Task Update_JobRecord_Status_Failed_JobRecord_Not_Exist()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        var logger = Substitute.For<ILogger<JobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var result = await target.UpdateJobStatusAsync(new UpdateJob
        {
            JobId = ExpectedGuid.ToString(),
            Status = JobStatusEnum.WorkflowStarted.ToString(),
            WorkflowId = ExpectedGuid.ToString(),
            Priority = JobPriorityEnum.Immediate,
            WorkflowName = "test-workflow",
        });

        logger.Received().Log(
            LogLevel.Error,
            0,
            Arg.Is<object>(v => v.ToString() == $"JobRecord not exist, WorkflowId: {ExpectedGuid.ToString().ToLower()}"),
            null,
            Arg.Any<Func<object, Exception, string>>()!
        );

        result.Should().BeEquivalentTo(
            ResponseModel<Dictionary<string, string>>.Create(
                ResultCode.BatchJobEx0003WorkflowJobNotFound,ExpectedGuid.ToLower(), ExpectedGuid.ToLower()));
    }

    private static BatchDbContext CreateStubDbContext(out DbSet<JobRecord> jobRecordDbSet,
        out DbSet<Workflow> workflowDbSet)
    {
        var dbContext = Substitute.For<BatchDbContext>();
        jobRecordDbSet = Substitute.For<DbSet<JobRecord>>();
        workflowDbSet = Substitute.For<DbSet<Workflow>>();
        dbContext.JobRecords.Returns(jobRecordDbSet);
        dbContext.Workflows.Returns(workflowDbSet);
        return dbContext;
    }

    // private static BatchDbContext CreateCustomBatchDbContext()
    // {
    //     var options = new DbContextOptionsBuilder<BatchDbContext>()
    //         .UseInMemoryDatabase(Guid.NewGuid().ToString())
    //         .Options;
    //
    //     return new MockBatchDbContext(options);
    // }

    private static BatchDbContext CreateInMemoryBatchDbContext()
    {
        var options = new DbContextOptionsBuilder<BatchDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;
        return new BatchDbContext(options);
    }

    private static void InitJobRecord(BatchDbContext dbContext, List<JobRecord> jobRecords)
    {
        dbContext.JobRecords.AddRange(jobRecords);
        dbContext.SaveChanges();
    }

    private static void InitScheduleJob(BatchDbContext dbContext, List<ScheduleJob> scheduleJobs)
    {
        dbContext.ScheduleJobs.AddRange(scheduleJobs);
        dbContext.SaveChanges();
    }

    private static MockJobService CreateTarget(BatchDbContext dbContext, IArgoWorkflowUtility argoUtility,
        IArgoWorkflowService argoWorkflowService, ILogger<JobService> logger)
    {
        var target = new MockJobService(dbContext, argoUtility, argoWorkflowService, logger)
        {
            MockGuid = ExpectedGuid
        };

        return target;
    }
}
