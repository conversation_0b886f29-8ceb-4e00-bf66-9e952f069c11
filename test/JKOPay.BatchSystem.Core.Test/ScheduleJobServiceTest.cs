using FluentAssertions;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Models.Argo;
using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;
using JKOPay.BatchSystem.Core.Services;
using JKOPay.BatchSystem.Core.Test.Mocks;
using JKOPay.BatchSystem.Core.Utilities;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;

namespace JKOPay.BatchSystem.Core.Test;

public class ScheduleJobServiceTest
{
    private static readonly Guid ExpectedGuid = new Guid("8d275755-69a8-4f32-ae4f-f88ed3641588");
    private const string ExpectedWorkflowName = "found-unittest-72ae8d564d7a49b2a6790690b07b22b3";
    private static readonly string[] Predicate = new [] { "unit-test-job" };

    [Fact]
    public async Task Create_Schedule_Job_Successfully()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        var logger = Substitute.For<ILogger<ScheduleJobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var actualScheduleJob = new ScheduleJob
        {
            CronWorkflowId = ExpectedGuid.ToString(),
            ScheduleJobName = "DailyBackup",
            LatestStatus = "none",
            ExecutionCycle = "* */20 * * *",
            WorkflowTemplateRef = "batchsystem-main-cron-workflow-template",
            IsDeleted = false,
            IsEnable = true,
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime
        };
        var result = await target.CreateScheduleJobAsync(actualScheduleJob);
        var actual = dbContext.ScheduleJobs.FirstOrDefault(p => p.JobId == 1);

        var time = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime;
        var expectedJob = new ScheduleJob
        {
            JobId = 1,
            CronWorkflowId = ExpectedGuid.ToString(),
            ScheduleJobName = "DailyBackup",
            LatestStatus = "none",
            ExecutionCycle = "* */20 * * *",
            WorkflowTemplateRef = "batchsystem-main-cron-workflow-template",
            IsDeleted = false,
            IsEnable = true,
            Team = "found",
            CreatedTime = time,
            UpdatedTime = time
        };

        actual.Should().BeEquivalentTo(expectedJob);
    }

    [Fact]
    public async Task Create_Schedule_Job_Failed_When_JobId_Exist()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        var logger = Substitute.For<ILogger<ScheduleJobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var actualScheduleJob = new ScheduleJob
        {
            CronWorkflowId = ExpectedGuid.ToString(),
            ScheduleJobName = "DailyBackup",
            LatestStatus = "none",
            ExecutionCycle = "* */20 * * *",
            WorkflowTemplateRef = "batchsystem-main-cron-workflow-template",
            IsDeleted = false,
            IsEnable = true,
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime
        };

        dbContext.ScheduleJobs.Add(actualScheduleJob);
        await dbContext.SaveChangesAsync();

        var result = await target.CreateScheduleJobAsync(actualScheduleJob);
        var actual = dbContext.ScheduleJobs.FirstOrDefault(p => p.JobId == 1);
        var time = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime;
        var expectedJob = new ScheduleJob
        {
            JobId = 1,
            CronWorkflowId = ExpectedGuid.ToString(),
            ScheduleJobName = "DailyBackup",
            LatestStatus = "none",
            ExecutionCycle = "* */20 * * *",
            WorkflowTemplateRef = "batchsystem-main-cron-workflow-template",
            IsDeleted = false,
            IsEnable = true,
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime
        };

        actual.Should().BeEquivalentTo(expectedJob);
    }

     [Fact]
    public async Task Schedule_Job_Deleted_Successfully()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        var logger = Substitute.For<ILogger<ScheduleJobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();
        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var existScheduleJob = new ScheduleJob()
        {
            JobId = 1,
            CronWorkflowId = "1234",
            ScheduleJobName = "unit-test-job",
            LatestStatus = "none",
            ExecutionCycle = "* * * * *",
            WorkflowTemplateRef = "unit-test-workflow-template",
            Comments = "",
            IsDeleted = false,
            IsEnable = true,
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
        };

        dbContext.ScheduleJobs.Add(existScheduleJob);
        dbContext.SaveChanges();

        var result = await target.DeleteScheduleJobAsync(new ScheduleJob
        {
            ScheduleJobName = "unit-test-job",
            Team = "found",
            IsDeleted = false,
            IsEnable = true
        });
        var actual = dbContext.ScheduleJobs.First(p => p.ScheduleJobName == "unit-test-job");
        var expectedScheduleJob = new ScheduleJob()
        {
            JobId = 1,
            CronWorkflowId = "1234",
            ScheduleJobName = "unit-test-job",
            LatestStatus = "none",
            ExecutionCycle = "* * * * *",
            WorkflowTemplateRef = "unit-test-workflow-template",
            Comments = "",
            IsDeleted = true,
            IsEnable = false,
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
        };

        actual.Should().BeEquivalentTo(expectedScheduleJob);
        result.Should().BeEquivalentTo(new ResponseModel<object>
        {
            Result = "0001",
            ResultObject = new {},
            Message = "Succeeded"
        });
    }

    [Fact]
    public async Task Schedule_Job_Deleted_Failed_Job_Not_Exist()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        var logger = Substitute.For<ILogger<ScheduleJobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();
        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var result = await target.DeleteScheduleJobAsync(new ScheduleJob
        {
            ScheduleJobName = "unit-test-job",
            Team = "found",
            IsDeleted = false,
            IsEnable = true
        });

        result.Should().BeEquivalentTo(new ResponseModel<object>
        {
            Result = "Batch-ScheduleJob-Ex-0003",
            ResultObject = new { },
            Message = "Schedule Job not exist.",
        });
    }

    [Fact]
    public async Task Schedule_Job_Deleted_Failed_Update_DB_Failed()
    {
        var dbContext = CreateCustomBatchDbContext();
        var logger = Substitute.For<ILogger<ScheduleJobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();
        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var existScheduleJob = new ScheduleJob()
        {
            JobId = 1,
            CronWorkflowId = "1234",
            ScheduleJobName = "unit-test-job",
            LatestStatus = "none",
            ExecutionCycle = "* * * * *",
            WorkflowTemplateRef = "unit-test-workflow-template",
            Comments = "",
            IsDeleted = false,
            IsEnable = true,
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
        };

        dbContext.ScheduleJobs.Add(existScheduleJob);
        dbContext.SaveChanges();

        var result = await target.DeleteScheduleJobAsync(new ScheduleJob
        {
            ScheduleJobName = "unit-test-job",
            Team = "found",
            IsDeleted = false,
            IsEnable = true
        });

        logger.Received().Log(
            LogLevel.Warning,
            0,
            Arg.Is<object>(v => v.ToString() == $"Update schedule job failed, schedule name: unit-test-job not exist."),
            null,
            Arg.Any<Func<object, Exception, string>>()!
        );

        result.Should().BeEquivalentTo(new ResponseModel<object>
        {
            Result = "Batch-ScheduleJob-Ex-0002",
            ResultObject = new { },
            Message = "disable schedule Job failed",
        });
    }

    [Fact]
    public async Task Schedule_Job_Updated_Successfully()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        var logger = Substitute.For<ILogger<ScheduleJobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();
        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var existScheduleJob = new ScheduleJob()
        {
            JobId = 1,
            CronWorkflowId = "1234",
            ScheduleJobName = "unit-test-job",
            LatestStatus = "none",
            ExecutionCycle = "* * * * *",
            WorkflowTemplateRef = "unit-test-workflow-template",
            Comments = "",
            IsDeleted = false,
            IsEnable = true,
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
        };

        dbContext.ScheduleJobs.Add(existScheduleJob);
        dbContext.SaveChanges();

        var result = await target.UpdateScheduleJobAsync(new ScheduleJob
        {
            CronWorkflowId = "5678",
            Comments = "unit-test",
            ExecutionCycle = "1 * * * *",
            WorkflowTemplateRef = "unit-test-workflow",
            ScheduleJobName = "unit-test-job",
            Team = "found",
            IsDeleted = false,
            IsEnable = true
        });
        var actual = dbContext.ScheduleJobs.First(p => p.ScheduleJobName == "unit-test-job");
        var expectedScheduleJob = new ScheduleJob()
        {
            CronWorkflowId = "5678",
            Comments = "unit-test",
            ExecutionCycle = "1 * * * *",
            WorkflowTemplateRef = "unit-test-workflow",
            ScheduleJobName = "unit-test-job",
            JobId = 1,
            LatestStatus = "none",
            IsDeleted = false,
            IsEnable = true,
            Team = "found",
            CreatedTime = new DateTimeOffset(2024, 6, 15, 0, 0, 0, TimeSpan.Zero).DateTime,
            UpdatedTime = new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero).DateTime,
        };

        actual.Should().BeEquivalentTo(expectedScheduleJob);
        result.Should().BeEquivalentTo(new ResponseModel<object>
        {
            Result = "0001",
            ResultObject = new {},
            Message = "Succeeded"
        });
    }

    [Fact]
    public async Task Schedule_Job_Updated_Failed_Job_Not_Exist()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        var logger = Substitute.For<ILogger<ScheduleJobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();
        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var result = await target.UpdateScheduleJobAsync(new ScheduleJob
        {
            ScheduleJobName = "unit-test-job",
            Team = "found",
            IsDeleted = false,
            IsEnable = true
        });

        logger.Received().Log(
            LogLevel.Warning,
            0,
            Arg.Is<object>(v => v.ToString() == $"Update schedule job failed, schedule name: unit-test-job not exist."),
            null,
            Arg.Any<Func<object, Exception, string>>()!
        );

        result.Should().BeEquivalentTo(new ResponseModel<object>
        {
            Result = "Batch-ScheduleJob-Ex-0003",
            ResultObject = new { },
            Message = "Schedule Job not exist.",
        });
    }

    [Fact]
    public async Task Execute_Schedule_Job_Successfully()
    {
        var dbContext = CreateInMemoryBatchDbContext();
        dbContext.JobRecords.Add(new JobRecord
        {
            JobId = "12345678",
            WorkflowName = "workflow-template-submittable",
        });
       dbContext.SaveChanges();

        var logger = Substitute.For<ILogger<ScheduleJobService>>();
        var argoUtility = Substitute.For<IArgoWorkflowUtility>();
        var argoWorkflowService = Substitute.For<IArgoWorkflowService>();

        argoWorkflowService.SubmitCronWorkflowAsync(Arg.Any<string>(), Arg.Any<string>()).Returns(Task.FromResult(new ResponseModel<SubmitResponse>
        {
            Result = "0001",
            ResultObject = ExpectedSubmitResponse,
            Message = "Succeeded"
        }));

        var target = CreateTarget(dbContext, argoUtility, argoWorkflowService, logger);
        var @namespace = "foundation";
        var scheduleJobName = "workflow-template-submittable";
        var result = await target.ExecuteScheduleJobAsync(@namespace, scheduleJobName);

        result.Should().BeEquivalentTo(new ResponseModel<object>
        {
            Result = "0001",
            ResultObject = new Dictionary<string, string>
            {
                { "JobId", "12345678" },
                { "WorkflowId", "8d275755-69a8-4f32-ae4f-f88ed3641588"},
                { "WorkflowName", "workflow-template-submittable" }
            },
            Message = "Succeeded"
        });
    }

    private static BatchDbContext CreateStubDbContext(out DbSet<JobRecord> jobRecordDbSet,
        out DbSet<Workflow> workflowDbSet)
    {
        var dbContext = Substitute.For<BatchDbContext>();
        jobRecordDbSet = Substitute.For<DbSet<JobRecord>>();
        workflowDbSet = Substitute.For<DbSet<Workflow>>();
        dbContext.JobRecords.Returns(jobRecordDbSet);
        dbContext.Workflows.Returns(workflowDbSet);
        return dbContext;
    }

    private static BatchDbContext CreateCustomBatchDbContext()
    {
        var options = new DbContextOptionsBuilder<BatchDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        return new MockBatchDbContext(options);
    }

    private static BatchDbContext CreateInMemoryBatchDbContext()
    {
        var options = new DbContextOptionsBuilder<BatchDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;
        return new BatchDbContext(options);
    }

    private static BatchDbContext InitJobRecord(BatchDbContext dbContext, List<JobRecord> jobRecords)
    {
        dbContext.JobRecords.AddRange(jobRecords);
        dbContext.SaveChanges();
        return dbContext;
    }

    private static MockScheduleJobService CreateTarget(BatchDbContext dbContext, IArgoWorkflowUtility argoUtility,
        IArgoWorkflowService argoWorkflowService, ILogger<ScheduleJobService> logger)
    {
        var target = new MockScheduleJobService(dbContext, argoWorkflowService, logger, ExpectedGuid)
        {
            MockGuid = ExpectedGuid
        };

        return target;
    }

    private static SubmitResponse ExpectedSubmitResponse => new()
    {
        // 因為這一個屬性已經被 "JsonIgnore" 所以會都是 false
        IsSucceed = false,
        Metadata = new Metadata
        {
            Name = "workflow-template-submittable",
            Namespace = "argo",
            Uid = "8d275755-69a8-4f32-ae4f-f88ed3641588",
            CreationTimestamp = new DateTime(2024, 06, 06, 03, 49, 11)
        },
        Spec = new WorkflowSpec
        {
            Arguments = new WorkflowArgs
            {
                Parameters =
                [
                    new WorkflowParameter
                    {
                        Name = "message",
                        Value = "Test by batch system api"
                    }
                ]
            }
        }
    };
}