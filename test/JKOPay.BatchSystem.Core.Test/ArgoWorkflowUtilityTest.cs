using System.Net;
using System.Text;
using System.Text.Json;
using FluentAssertions;
using JKOPay.BatchSystem.Core.Models;
using JKOPay.BatchSystem.Core.Models.Argo;
using JKOPay.BatchSystem.Core.Models.Argo.Eventsource;
using JKOPay.BatchSystem.Core.Models.Argo.Sensor;
using JKOPay.BatchSystem.Core.Models.Argo.Workflow;
using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;
using JKOPay.BatchSystem.Core.Models.Option.ArgoWorkflow;
using JKOPay.BatchSystem.Core.Test.Mocks;
using JKOPay.BatchSystem.Core.Utilities;
using JKOPay.Platform.BatchSystem.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NSubstitute;
using RichardSzalay.MockHttp;

namespace JKOPay.BatchSystem.Core.Test;

public class ArgoWorkflowUtilityTest
{
    private string _namespace = "argo";

    private MockSubmitRequest SubmitRequest
    {
        get
        {
            var @namespace = _namespace;
            const string jobName = "workflow-template-submittable";
            const string entryPoint = "whalesay-template";
            var parameters = new Dictionary<string, object>
            {
                { "message", "Test by batch system api" }
            };

            var labels = new Dictionary<string, string>
            {
                { "owner", "found" }
            };

            var request = new MockSubmitRequest(@namespace, jobName, "", entryPoint, parameters, labels);
            return request;
        }
    }

    private static SubmitResponse ExpectedSubmitResponse => new()
    {
        // 因為這一個屬性已經被 "JsonIgnore" 所以會都是 false
        IsSucceed = false,
        Metadata = new Metadata
        {
            Name = "workflow-template-submittable",
            Namespace = "argo",
            Uid = "8d275755-69a8-4f32-ae4f-f88ed3641588",
            CreationTimestamp = new DateTime(2024, 06, 06, 03, 49, 11)
        },
        Spec = new WorkflowSpec
        {
            Arguments = new WorkflowArgs
            {
                Parameters =
                [
                    new WorkflowParameter
                    {
                        Name = "message",
                        Value = "Test by batch system api"
                    }
                ]
            }
        }
    };

    private static WorkflowListResponse ExpectedWorkflowListResponse => new()
    {
        Items = ExpectedWorkflows
    };

    private static List<Workflow> ExpectedWorkflows
    {
        get
        {
            var list = new List<Workflow>
            {
                new Workflow
                {
                    Metadata = new Metadata
                    {
                        Name = "workflow-template-submittable",
                        Namespace = "argo",
                        Uid = "8d275755-69a8-4f32-ae4f-f88ed3641588",
                        CreationTimestamp = new DateTime(2024, 06, 06, 03, 49, 11)
                    },
                    Spec = new WorkflowSpec
                    {
                        Arguments = new WorkflowArgs
                        {
                            Parameters =
                            [
                                new WorkflowParameter
                                {
                                    Name = "message",
                                    Value = "Test by batch system api"
                                }
                            ]
                        }
                    }
                }
            };
            return list;
        }
    }

    private static object ExpectedResponse(int code, string message)
    {
        var expectedResponse = new
        {
            code,
            message
        };

        return expectedResponse;
    }

    [Fact]
    public async Task SubmitWorkflow_Successfully_Created()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Workflow = new WorkflowOption
            {
                Submit = "api/v1/workflows/argo/submit",
                List = "api/v1/workflows",
            },
        };

        var request = SubmitRequest;
        var mockHttp = new MockHttpMessageHandler();
        var tmp = JsonConvert.SerializeObject(ExpectedSubmitResponse);
        mockHttp.When(HttpMethod.Post, "https://localhost:2746/api/v1/workflows/argo/submit")
            .WithJsonContent(SubmitRequest, new JsonSerializerOptions(JsonSerializerDefaults.Web))
            .Respond("application/json", tmp);

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());


        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.SubmitWorkflowAsync(request);
        var expected = new QueryResult<SubmitResponse>
        {
            IsSuccess = true,
            Data = ExpectedSubmitResponse
        };
        actual.Data.Should().BeEquivalentTo(expected.Data);
    }

    [Fact]
    public async Task SubmitWorkflow_Successfully_Created_Use_Found_Namespace()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Workflow = new WorkflowOption
            {
                Submit = "api/v1/workflows/argo/submit",
                List = "api/v1/workflows",
            },
        };

        _namespace = "foundation";
        var request = SubmitRequest;
        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Post, "https://localhost:2746/api/v1/workflows/foundation/submit")
            .WithJsonContent(SubmitRequest, new JsonSerializerOptions(JsonSerializerDefaults.Web))
            .Respond("application/json", JsonConvert.SerializeObject(ExpectedSubmitResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());


        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.SubmitWorkflowAsync(request);
        var expected = ExpectedSubmitResponse;
        actual.Data.Should().BeEquivalentTo(expected);
    }

    [Fact]
    public async Task SubmitWorkflow_Failed_Created()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Workflow = new WorkflowOption
            {
                Submit = "api/v1/workflows/argo/submit",
                List = "api/v1/workflows",
            },
        };

        var request = SubmitRequest;
        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Post, "https://localhost:2746/api/v1/workflows/argo/submit")
            .WithJsonContent(SubmitRequest, new JsonSerializerOptions(JsonSerializerDefaults.Web))
            .Respond(HttpStatusCode.InternalServerError);

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());


        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.SubmitWorkflowAsync(request);
        actual.IsSuccess.Should().BeFalse();
        mockHttp.VerifyNoOutstandingExpectation();
    }

    [Fact]
    public async Task SubmitCronWorkflow_Successfully_Created()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            CronWorkflow = new CronWorkflowOption
            {
                Submit = "api/v1/workflows/argo/submit",
            }
        };

        var @namespace = "foudnation";
        var cronWorkflowName = "workflow-template-submittable";

        var request = new Dictionary<string, object>
        {
            { "namespace", @namespace },
            { "resourceName", cronWorkflowName },
            { "resourceKind", "cronwf" },
        };

        var mockHttp = new MockHttpMessageHandler();
        var tmp = JsonConvert.SerializeObject(ExpectedSubmitResponse);
        mockHttp.When(HttpMethod.Post, argoWorkflowOption.Domain + argoWorkflowOption.CronWorkflow.Submit)
            .WithJsonContent(request, new JsonSerializerOptions(JsonSerializerDefaults.Web))
            .Respond("application/json", tmp);

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());


        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.SubmitCronWorkflowAsync(@namespace, cronWorkflowName);
        var expected = new QueryResult<SubmitResponse>
        {
            IsSuccess = true,
            Data = ExpectedSubmitResponse
        };

        actual.Data.Should().BeEquivalentTo(expected.Data);
    }

    [Fact]
    public async Task SubmitCronWorkflow_Failed_Created()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            CronWorkflow = new CronWorkflowOption()
            {
                Submit = "api/v1/workflows/argo/submit",
            },
        };

        var @namespace = "foudnation";
        var cronWorkflowName = "workflow-template-submittable";
        var request = new Dictionary<string, object>
        {
            { "namespace", @namespace },
            { "resourceName", cronWorkflowName },
            { "resourceKind", "cronwf" },
        };

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Post, "https://localhost:2746/api/v1/workflows/argo/submit")
            .WithJsonContent(request, new JsonSerializerOptions(JsonSerializerDefaults.Web))
            .Respond(HttpStatusCode.InternalServerError);

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());


        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.SubmitCronWorkflowAsync(@namespace, cronWorkflowName);
        actual.IsSuccess.Should().BeFalse();
        mockHttp.VerifyNoOutstandingExpectation();
    }

    [Fact]
    public async Task GetWorkflow_Successfully()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Workflow = new WorkflowOption
            {
                Submit = "api/v1/workflows/argo/submit",
                List = "api/v1/workflows",
            },
        };

        _namespace = "found";
        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Get, "https://localhost:2746/api/v1/workflows/")
            .Respond("application/json", JsonConvert.SerializeObject(ExpectedWorkflowListResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.GetWorkflowsAsync();
        var expected = new QueryResult<List<Workflow>>
        {
            IsSuccess = true,
            Data = ExpectedWorkflowListResponse.Items
        };

        actual.Should().BeEquivalentTo(expected);
    }

    [Fact]
    public async Task GetWorkflow_Successfully_With_LabelSelector()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Workflow = new WorkflowOption
            {
                Submit = "api/v1/workflows/argo/submit",
                List = "api/v1/workflows",
            },
        };

        _namespace = "found";
        var labelSelector = "owner=found";
        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Get,
                $"https://localhost:2746/api/v1/workflows/{_namespace}?listOptions.labelSelector={labelSelector}")
            .Respond("application/json", JsonConvert.SerializeObject(ExpectedWorkflowListResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.GetWorkflowsAsync(_namespace, labelSelector);
        var expected = new QueryResult<List<Workflow>>
        {
            IsSuccess = true,
            Data = ExpectedWorkflowListResponse.Items
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility GetWorkflowTemplateAsync Success
    [Fact]
    public async Task GetWorkflowTemplateAsync_Success()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            WorkflowTemplate = new WorkflowTemplateOption
            {
                List = "api/v1/workflow-templates"
            },
        };

        _namespace = "found";
        var expectedResponse = new WorkflowTemplateListResponse
        {
            // generate expected response for testing
            Items =
            [
                new WorkflowTemplate
                {
                    Metadata = new Metadata
                    {
                        Name = "workflow-template-submittable",
                        Namespace = "argo",
                        Uid = "8d275755-69a8-4f32-ae4f-f88ed3641588",
                        Labels = new Dictionary<string, string>(),
                        CreationTimestamp = new DateTime(2024, 06, 06, 03, 49, 11)
                    },
                }
            ]
        };

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Get, $"https://localhost:2746/api/v1/workflow-templates/{_namespace}")
            .Respond("application/json", JsonConvert.SerializeObject(expectedResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.GetWorkflowTemplatesAsync(_namespace, "");
        var expected = new QueryResult<List<WorkflowTemplate>>
        {
            IsSuccess = true,
            Data = expectedResponse.Items
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility GetWorkflowTemplateAsync With LabelSelector Success
    [Fact]
    public async Task GetWorkflowTemplateAsync_With_LabelSelector_Success()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            WorkflowTemplate = new WorkflowTemplateOption
            {
                List = "api/v1/workflow-templates"
            },
        };

        _namespace = "found";
        var labelSelector = "owner=found";
        var expectedResponse = new WorkflowTemplateListResponse
        {
            Items =
            [
                new WorkflowTemplate
                {
                    Metadata = new Metadata
                    {
                        Name = "workflow-template-submittable",
                        Namespace = "argo",
                        Uid = "8d275755-69a8-4f32-ae4f-f88ed3641588",
                        Labels = new Dictionary<string, string>(),
                        CreationTimestamp = new DateTime(2024, 06, 06, 03, 49, 11)
                    },
                }
            ]
        };

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Get,
                $"https://localhost:2746/api/v1/workflow-templates/{_namespace}?listOptions.labelSelector={labelSelector}")
            .Respond("application/json", JsonConvert.SerializeObject(expectedResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.GetWorkflowTemplatesAsync(_namespace, labelSelector);
        var expected = new QueryResult<List<WorkflowTemplate>>
        {
            IsSuccess = true,
            Data = expectedResponse.Items
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility DeleteWorkflowTemplate Success
    [Fact]
    public async Task DeleteWorkflowTemplate_Success()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            WorkflowTemplate = new WorkflowTemplateOption
            {
                Delete = "api/v1/workflow-templates"
            },
        };

        _namespace = "found";
        var templateName = "workflow-template-submittable";

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Delete, $"https://localhost:2746/api/v1/workflow-templates/{_namespace}/{templateName}")
            .Respond("application/json", "");

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.DeleteWorkflowTemplate(_namespace, templateName);
        var expected = new QueryResult<object>
        {
            IsSuccess = true,
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility DeleteTemplateWorkflow Failed, WorkflowTemplate Not Found
    [Fact]
    public async Task DeleteWorkflowTemplate_Failed_WorkflowTemplate_Not_Found()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            WorkflowTemplate = new WorkflowTemplateOption
            {
                Delete = "api/v1/workflow-templates"
            },
        };

        var @namespace = "found";
        var templateName = "non-existent-template";
        var expectedResponse = ExpectedResponse(5, "workflowtemplates.argoproj.io \"non-existent-template\" not found");

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Delete, $"https://localhost:2746/api/v1/workflow-templates/{@namespace}/{templateName}")
            .Respond(HttpStatusCode.NotFound, "application/json", JsonConvert.SerializeObject(expectedResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.DeleteWorkflowTemplate(@namespace, templateName);
        var expected = new QueryResult<object>
        {
            IsSuccess = false,
            Code = "5",
            ErrorMessage = "workflowtemplates.argoproj.io \"non-existent-template\" not found"
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility GetSensor Success
    [Fact]
    public async Task GetSensor_Success()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Sensor = new SensorOption
            {
                List = "api/v1/sensors"
            },
        };

        _namespace = "found";
        var expectedResponse = new SensorListResponse
        {
            Items =
            [
                new Sensor
                {
                    Metadata = new Metadata
                    {
                        Name = "sensor-name",
                        Namespace = _namespace,
                        Uid = "8d275755-69a8-4f32-ae4f-f88ed3641588",
                        CreationTimestamp = new DateTime(2024, 06, 06, 03, 49, 11)
                    },
                }
            ]
        };

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Get, $"https://localhost:2746/api/v1/sensors/{_namespace}")
            .Respond("application/json", JsonConvert.SerializeObject(expectedResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.GetSensors(_namespace);
        var expected = new QueryResult<List<Sensor>>
        {
            IsSuccess = true,
            Data = expectedResponse.Items
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility DeleteSensor Success
    [Fact]
    public async Task DeleteSensor_Success()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Sensor = new SensorOption
            {
                Delete = "api/v1/sensors"
            },
        };

        _namespace = "found";
        var sensorName = "sensor-name";

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Delete, $"https://localhost:2746/api/v1/sensors/{_namespace}/{sensorName}")
            .Respond("application/json", "");

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.DeleteSensor(_namespace, sensorName);
        var expected = new QueryResult<object>
        {
            IsSuccess = true,
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility DeleteSensor Failed, Sensor Not Found
    [Fact]
    public async Task DeleteSensor_Failed_Sensor_Not_Found()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Sensor = new SensorOption
            {
                Delete = "api/v1/sensors"
            },
        };

        _namespace = "found";
        var sensorName = "non-existent-sensor";
        var expectedResponse = ExpectedResponse(5, "sensors.argoproj.io \"non-existent-sensor\" not found");
        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Delete, $"https://localhost:2746/api/v1/sensors/{_namespace}/{sensorName}")
            .Respond(HttpStatusCode.NotFound, "application/json", JsonConvert.SerializeObject(expectedResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.DeleteSensor(_namespace, sensorName);
        var expected = new QueryResult<object>
        {
            IsSuccess = false,
            Code = "5",
            ErrorMessage = "sensors.argoproj.io \"non-existent-sensor\" not found"
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility GetEventsource Success
    [Fact]
    public async Task GetEventsource_Success()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Eventsource = new EventsourceOption
            {
                List = "api/v1/event-sources"
            },
        };

        _namespace = "found";
        var expectedResponse = new EventsourceListResponse
        {
            Items =
            [
                new Eventsource
                {
                    Metadata = new Metadata
                    {
                        Name = "eventsource-name",
                        Namespace = _namespace,
                        Uid = "8d275755-69a8-4f32-ae4f-f88ed3641588",
                        CreationTimestamp = new DateTime(2024, 06, 06, 03, 49, 11)
                    },
                }
            ]
        };

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Get, $"https://localhost:2746/api/v1/event-sources/{_namespace}")
            .Respond("application/json", JsonConvert.SerializeObject(expectedResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.GetEventsource(_namespace);
        var expected = new QueryResult<List<Eventsource>>
        {
            IsSuccess = true,
            Data = expectedResponse.Items
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility DeleteEventsource Success
    [Fact]
    public async Task DeleteEventsource_Success()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Eventsource = new EventsourceOption
            {
                Delete = "api/v1/event-sources"
            },
        };

        var @namespace = "found";
        var eventsourceName = "eventsource-name";

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Delete, $"https://localhost:2746/api/v1/event-sources/{@namespace}/{eventsourceName}")
            .Respond("application/json", "");

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.DeleteEventsource(@namespace, eventsourceName);
        var expected = new QueryResult<object>
        {
            IsSuccess = true,
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility DeleteEventsource Failed, Eventsource Not Found
    [Fact]
    public async Task DeleteEventsource_Failed_Eventsource_Not_Found()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Eventsource = new EventsourceOption
            {
                Delete = "api/v1/event-sources"
            },
        };

        var @namespace = "found";
        var eventsourceName = "non-existent-eventsource";

        var expectedResponse = ExpectedResponse(5, "eventsources.argoproj.io \"non-existent-eventsource\" not found");
        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Delete, $"https://localhost:2746/api/v1/event-sources/{@namespace}/{eventsourceName}")
            .Respond(HttpStatusCode.NotFound, "application/json", JsonConvert.SerializeObject(expectedResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.DeleteEventsource(@namespace, eventsourceName);
        var expected = new QueryResult<object>
        {
            IsSuccess = false,
            Code = "5",
            ErrorMessage = "eventsources.argoproj.io \"non-existent-eventsource\" not found"
        };

        actual.Should().BeEquivalentTo(expected);
    }

    [Fact]
    public async Task Stop_Workflow_Successfully()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Workflow = new WorkflowOption
            {
                Submit = "api/v1/workflows/argo/submit",
                List = "api/v1/workflows",
                Stop = "api/v1/workflows/{namespace}/{name}/stop"
            },
        };

        _namespace = "found";
        var stubJobName = "workflow-template-submittable";
        var body = new Dictionary<string, string>
        {
            { "name", stubJobName },
            { "namespace", _namespace }
        };

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Put, $"https://localhost:2746/api/v1/workflows/{_namespace}/{stubJobName}/stop")
            .WithJsonContent(body, new JsonSerializerOptions(JsonSerializerDefaults.Web))
            .Respond("application/json", "");

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.StopWorkflowAsync(_namespace, stubJobName);
        var expected = new QueryResult<object>
        {
            IsSuccess = true,
        };

        actual.Should().BeEquivalentTo(expected);
    }

    [Fact]
    public async Task Stop_Workflow_Failed_Workflow_Not_Exist()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Workflow = new WorkflowOption
            {
                Stop = "api/v1/workflows/{namespace}/{name}/stop"
            },
        };

        _namespace = "found";
        var stubJobName = "workflow-template-submittable";
        var body = new Dictionary<string, string>
        {
            { "name", stubJobName },
            { "namespace", _namespace }
        };

        var expectedResponse = ExpectedResponse(5, "workflows.argoproj.io \"workflow-name\" not found");
        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Put, $"https://localhost:2746/api/v1/workflows/{_namespace}/{stubJobName}/stop")
            .WithJsonContent(body, new JsonSerializerOptions(JsonSerializerDefaults.Web))
            .Respond(HttpStatusCode.NotFound, "application/json", JsonConvert.SerializeObject(expectedResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.StopWorkflowAsync(_namespace, stubJobName);
        var expected = new QueryResult<object>
        {
            IsSuccess = false,
            Code = "5",
            ErrorMessage = "workflows.argoproj.io \"workflow-name\" not found"
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility TerminateWorkflowAsync Success
    [Fact]
    public async Task Terminate_Workflow_Successfully()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Workflow = new WorkflowOption
            {
                Terminate = "api/v1/workflows/{namespace}/{name}/terminate"
            },
        };

        _namespace = "found";
        var stubJobName = "workflow-template-submittable";
        var body = new Dictionary<string, string>
        {
            { "name", stubJobName },
            { "namespace", _namespace }
        };

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Put, $"https://localhost:2746/api/v1/workflows/{_namespace}/{stubJobName}/terminate")
            .WithJsonContent(body, new JsonSerializerOptions(JsonSerializerDefaults.Web))
            .Respond("application/json", "");

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.TerminateWorkflowAsync(_namespace, stubJobName);
        var expected = new QueryResult<object>
        {
            IsSuccess = true,
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility TerminateWorkflowAsync Failed
    [Fact]
    public async Task Terminate_Workflow_Failed_Workflow_Not_Exist()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Workflow = new WorkflowOption
            {
                Terminate = "api/v1/workflows/{namespace}/{name}/terminate"
            },
        };

        _namespace = "found";
        var stubJobName = "workflow-template-submittable";
        var body = new Dictionary<string, string>
        {
            { "name", stubJobName },
            { "namespace", _namespace }
        };

        var expectedResponse = ExpectedResponse(5, "workflows.argoproj.io \"workflow-name\" not found");
        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Put, $"https://localhost:2746/api/v1/workflows/{_namespace}/{stubJobName}/terminate")
            .WithJsonContent(body, new JsonSerializerOptions(JsonSerializerDefaults.Web))
            .Respond(HttpStatusCode.NotFound, "application/json", JsonConvert.SerializeObject(expectedResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.TerminateWorkflowAsync(_namespace, stubJobName);
        var expected = new QueryResult<object>
        {
            IsSuccess = false,
            Code = "5",
            ErrorMessage = "workflows.argoproj.io \"workflow-name\" not found"
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility DeleteWorkflowAsync Success
    [Fact]
    public async Task Delete_Workflow_Successfully()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Workflow = new WorkflowOption
            {
                Delete = "api/v1/workflows/{namespace}/{name}"
            },
        };

        _namespace = "found";
        var stubJobName = "workflow-template-submittable";
        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Delete, $"https://localhost:2746/api/v1/workflows/{_namespace}/{stubJobName}")
            .Respond("application/json", "");

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.DeleteWorkflowAsync(_namespace, stubJobName);
        var expected = new QueryResult<object>
        {
            IsSuccess = true,
        };

        actual.Should().BeEquivalentTo(expected);
    }

    // Test For ArgoWorkflowUtility DeleteWorkflowAsync Failed
    [Fact]
    public async Task Delete_Workflow_Failed_Workflow_Not_Exist()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption
        {
            Domain = "https://localhost:2746/",
            Workflow = new WorkflowOption
            {
                Delete = "api/v1/workflows/{namespace}/{name}"
            },
        };

        _namespace = "found";
        var stubJobName = "workflow-template-submittable";
        var expectedResponse = ExpectedResponse(5, "workflows.argoproj.io \"workflow-name\" not found");
        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Delete, $"https://localhost:2746/api/v1/workflows/{_namespace}/{stubJobName}")
            .Respond(HttpStatusCode.NotFound, "application/json", JsonConvert.SerializeObject(expectedResponse));

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.DeleteWorkflowAsync(_namespace, stubJobName);
        var expected = new QueryResult<object>
        {
            IsSuccess = false,
            Code = "5",
            ErrorMessage = "workflows.argoproj.io \"workflow-name\" not found"
        };

        actual.Should().BeEquivalentTo(expected);
    }

    [Fact]
    public async Task Suspend_CronWorkflow_Successfully()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption()
        {
            Domain = "https://localhost:2746/",
            CronWorkflow = new CronWorkflowOption()
            {
                Suspend = "api/v1/cron-workflows/{namespace}/{name}/suspend"
            },
        };

        _namespace = "found";
        var stubJobName = "workflow-template-submittable";
        var body = new Dictionary<string, string>
        {
            { "name", stubJobName },
            { "namespace", _namespace }
        };

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Put, $"https://localhost:2746/api/v1/cron-workflows/{_namespace}/{stubJobName}/suspend")
            .WithJsonContent(body, new JsonSerializerOptions(JsonSerializerDefaults.Web))
            .Respond("application/json", "");

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p => p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.SuspendCronWorkflow(_namespace, stubJobName);
        var expected = new QueryResult<object>
        {
            IsSuccess = true,
        };

        actual.Should().BeEquivalentTo(expected);
    }

    [Fact]
    public async Task Resume_CronWorkflow_Successfully()
    {
        var logger = Substitute.For<ILogger<ArgoWorkflowUtility>>();
        var argoWorkflowOption = new ArgoWorkflowOption()
        {
            Domain = "https://localhost:2746/",
            CronWorkflow = new CronWorkflowOption()
            {
                Resume = "api/v1/cron-workflows/{namespace}/{name}/resume"
            },
        };

        _namespace = "found";
        var stubJobName = "workflow-template-submittable";
        var body = new Dictionary<string, string>
        {
            { "name", stubJobName },
            { "namespace", _namespace }
        };

        var mockHttp = new MockHttpMessageHandler();
        mockHttp.When(HttpMethod.Put, $"https://localhost:2746/api/v1/cron-workflows/{_namespace}/{stubJobName}/resume")
            .WithJsonContent(body, new JsonSerializerOptions(JsonSerializerDefaults.Web))
            .Respond("application/json", "");

        var httpFactory = Substitute.For<IHttpClientFactory>();
        httpFactory.CreateClient(Arg.Is<string>(p =>p == "Argo")).Returns(mockHttp.ToHttpClient());

        var target = new ArgoWorkflowUtility(httpFactory, new JsonNetSerializerUtility(), argoWorkflowOption, logger);
        var actual = await target.ResumeCronWorkflow(_namespace, stubJobName);
        var expected = new QueryResult<object>
        {
            IsSuccess = true,
        };

        actual.Should().BeEquivalentTo(expected);
    }
}