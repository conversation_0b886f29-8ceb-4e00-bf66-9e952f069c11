using JKOPay.BatchSystem.Core.Services;
using JKOPay.BatchSystem.Core.Utilities;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.Extensions.Logging;

namespace JKOPay.BatchSystem.Core.Test.Mocks;

public class MockJobService(BatchDbContext dbContext, IArgoWorkflowUtility argoUtility, IArgoWorkflowService argoWorkflowService, ILogger<JobService> logger)
    : JobService(dbContext, argoUtility, argoWorkflowService, "dev", logger)
{
    public string? MockGuid { get; set; }
    protected override DateTimeOffset GetNow()
    {
        return new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero);
    }

    protected override string? GetGuid()
    {
        return MockGuid?.ToString();
    }
}