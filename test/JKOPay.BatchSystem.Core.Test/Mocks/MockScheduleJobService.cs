using JKOPay.BatchSystem.Core.Services;
using JKOPay.Platform.BatchSystem.Models.Database;
using Microsoft.Extensions.Logging;

namespace JKOPay.BatchSystem.Core.Test.Mocks;

public class MockScheduleJobService(BatchDbContext dbContext, IArgoWorkflowService argoWorkflowService, ILogger<ScheduleJobService> logger, Guid mockGuid)
    : ScheduleJobService(dbContext, argoWorkflowService, logger)
{
    public Guid MockGuid { get; set; } = mockGuid;

    protected override DateTimeOffset GetNow()
    {
        return new DateTimeOffset(2024, 6, 17, 0, 0, 0, TimeSpan.Zero);
    }

    protected override Guid GetGuid()
    {
        return MockGuid;
    }
}