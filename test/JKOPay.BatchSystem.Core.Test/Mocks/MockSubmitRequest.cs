using JKOPay.BatchSystem.Core.Models.Argo.WorkflowTemplate;

namespace JKOPay.BatchSystem.Core.Test.Mocks;

public class MockSubmitRequest(
    string ns,
    string jobName,
    string workflowTemplateName,
    string entryPoint,
    Dictionary<string, object> parameters,
    Dictionary<string, string> labels)
    : SubmitRequest(ns, jobName, workflowTemplateName, entryPoint, parameters, labels)
{

    protected override Guid GetGuid()
    {
        return new Guid("8d275755-69a8-4f32-ae4f-f88ed3641588");
    }
}