using FluentAssertions;
using JKOPay.BatchSystem.Operator.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit.Abstractions;

namespace JKOPay.BatchSystem.Operator.Test
{
    public class ServiceControllerTest(ITestOutputHelper testOutputHelper) : TestBase(testOutputHelper)
    {
        /// <summary>
        /// 測試當註冊已存在於 HealthCheck 字典中的服務時，驗證註冊結果應為成功。
        /// 測試情境：將 ScheduleJobOperator 類型的服務註冊到 ServiceController 中，
        /// 並確認返回註冊成功的訊息及 HealthCheck 字典中該服務的狀態被更新為 true。
        /// </summary>
        [Fact]
        public void RegisteredServices_ShouldRegisterServiceSuccessfully_WhenServiceExists()
        {
            // Arrange
            // 先重置靜態字典，確保測試環境乾淨
            ServiceController.HealthCheck = new Dictionary<Type, bool>
            {
                { typeof(ScheduleJobOperator), false },
                { typeof(WorkflowOperator), false }
            };

            // Act
            var result = ServiceController.RegisteredServices(typeof(ScheduleJobOperator));

            // Assert
            result.Result.Should().BeTrue();
            result.Message.Should().Be("Service registered successfully");
            ServiceController.HealthCheck[typeof(ScheduleJobOperator)].Should().BeTrue();
        }

        /// <summary>
        /// 測試當註冊不存在於 HealthCheck 字典中的新服務時，驗證系統應該成功添加並註冊該服務。
        /// 測試情境：將一個新的 HttpListenerService 類型服務註冊到 ServiceController 中，
        /// 確認返回註冊成功的訊息，並驗證 HealthCheck 字典中已添加該服務且狀態設為 true。
        /// </summary>
        [Fact]
        public void RegisteredServices_ShouldAddNewServiceAndRegisterSuccessfully_WhenServiceDoesNotExist()
        {
            // Arrange
            // 先重置靜態字典，確保測試環境乾淨
            ServiceController.HealthCheck = new Dictionary<Type, bool>
            {
                { typeof(ScheduleJobOperator), false },
                { typeof(WorkflowOperator), false }
            };
            var newServiceType = typeof(HttpListenerService);

            // Act
            var result = ServiceController.RegisteredServices(newServiceType);

            // Assert
            result.Result.Should().BeTrue();
            result.Message.Should().Be("Service registered successfully");
            ServiceController.HealthCheck.Should().ContainKey(newServiceType);
            ServiceController.HealthCheck[newServiceType].Should().BeTrue();
        }

        /// <summary>
        /// 測試 ServiceController 的 ExecuteAsync 方法能夠偵測並重啟不健康的服務。
        /// 測試情境：設置 ScheduleJobOperator 服務的健康狀態為 false，
        /// 模擬 ServiceController 偵測到不健康服務後的行為，
        /// 確認其能夠正確記錄日誌、停止並重啟不健康的服務、更新服務健康狀態為 true。
        ///
        /// 注意：此測試使用分離的取消令牌來避免 TaskCanceledException。
        /// 一個令牌用於測試控制，另一個用於服務生命週期，確保測試可以正確驗證結果。
        /// </summary>
        [Fact]
        public async Task ExecuteAsync_ShouldRestartUnhealthyServices()
        {
            // Arrange
            var serviceProvider = Substitute.For<IServiceProvider>();
            var scheduleJobOperator = Substitute.For<IHostedService>();
            var customMetricsProvider = Substitute.For<CustomMetricsProvider>();

            // 設置 HealthCheck 靜態字典
            ServiceController.HealthCheck = new Dictionary<Type, bool>
            {
                { typeof(ScheduleJobOperator), false },
                { typeof(WorkflowOperator), true }
            };

            // 設置 ServiceProvider.GetService 返回模擬的 service
            serviceProvider.GetService(typeof(ScheduleJobOperator)).Returns(scheduleJobOperator);

            var serviceController = new ServiceController(serviceProvider, "test", customMetricsProvider, LoggerMock);

            // 為服務創建單獨的取消令牌，不會在測試期間取消
            var serviceCts = new CancellationTokenSource();

            try
            {
                // Act - 啟動服務
                await serviceController.StartAsync(serviceCts.Token);

                // 確保 ExecuteAsync 有時間執行，但不使用 serviceCts.Token
                // 使用 Task.Delay 但不傳入取消令牌，避免任務被取消
                await Task.Delay(1000, serviceCts.Token);

                // Assert
                // 驗證 logger 被調用，表示不健康的服務被檢測到
                LogAllLoggerCalls();
                AssertLoggerSetReceived("Log", ["Information", "0", "Service ScheduleJobOperator is unhealthy"]);

                // 驗證停止和啟動方法被調用
                await scheduleJobOperator.Received(1).StopAsync(Arg.Any<CancellationToken>());
                await scheduleJobOperator.Received(1).StartAsync(Arg.Any<CancellationToken>());

                // 驗證健康狀態被更新
                ServiceController.HealthCheck[typeof(ScheduleJobOperator)].Should().BeTrue();

                // 驗證記錄服務恢復健康的日誌
                AssertLoggerSetReceived("Log", ["Information", "0", "Service ScheduleJobOperator is healthy"]);
            }
            finally
            {
                // 無論測試是否成功都確保停止服務
                // 取消 serviceCts 然後停止服務
                await serviceCts.CancelAsync();
                await serviceController.StopAsync(CancellationToken.None);
                serviceCts.Dispose();
            }
        }

        /// <summary>
        /// 測試當所有服務都處於健康狀態時，ServiceController 的 ExecuteAsync 方法不應重啟任何服務。
        /// 測試情境：設置所有服務（ScheduleJobOperator 和 WorkflowOperator）的健康狀態為 true，
        /// 執行 ServiceController 的監控流程，驗證沒有服務被停止或重啟。
        /// </summary>
        [Fact]
        public async Task ExecuteAsync_ShouldNotRestartServices_WhenAllServicesAreHealthy()
        {
            // Arrange
            var logger = Substitute.For<ILogger<ServiceController>>();
            var serviceProvider = Substitute.For<IServiceProvider>();
            var scheduleJobOperator = Substitute.For<IHostedService>();
            var workflowOperator = Substitute.For<IHostedService>();
            var customMetricsProvider = Substitute.For<CustomMetricsProvider>();

            // 設置所有服務為健康狀態
            ServiceController.HealthCheck = new Dictionary<Type, bool>
            {
                { typeof(ScheduleJobOperator), true },
                { typeof(WorkflowOperator), true }
            };

            serviceProvider.GetService(typeof(ScheduleJobOperator)).Returns(scheduleJobOperator);
            serviceProvider.GetService(typeof(WorkflowOperator)).Returns(workflowOperator);

            var serviceController = new ServiceController(serviceProvider, "test", customMetricsProvider, logger);
            var cts = new CancellationTokenSource();

            // 設置取消令牌在一小段時間後取消
            cts.CancelAfter(1000);

            // Act - 啟動服務
            await serviceController.StartAsync(cts.Token);

            // 確保 ExecuteAsync 有時間執行
            await Task.Delay(500, cts.Token);

            // Assert
            // 驗證服務的 StopAsync 和 StartAsync 方法沒有被調用
            await scheduleJobOperator.DidNotReceive().StopAsync(Arg.Any<CancellationToken>());
            await scheduleJobOperator.DidNotReceive().StartAsync(Arg.Any<CancellationToken>());

            await workflowOperator.DidNotReceive().StopAsync(Arg.Any<CancellationToken>());
            await workflowOperator.DidNotReceive().StartAsync(Arg.Any<CancellationToken>());

            // 停止服務
            await serviceController.StopAsync(cts.Token);
        }

        /// <summary>
        /// 測試 ServiceController 的 ExecuteAsync 方法能夠妥善處理在重啟服務過程中發生的異常。
        /// 測試情境：設置 ScheduleJobOperator 服務的健康狀態為 false，
        /// 並模擬 ServiceProvider.GetService 方法拋出異常的情況，
        /// 驗證異常被正確記錄，且系統的整體健康狀態 HttpListenerService.Opahealth 被設置為 false。
        /// </summary>
        [Fact]
        public async Task ExecuteAsync_ShouldHandleExceptions_WhenErrorOccurs()
        {
            // Arrange
            var logger = Substitute.For<ILogger<ServiceController>>();
            var serviceProvider = Substitute.For<IServiceProvider>();
            var scheduleJobOperator = Substitute.For<IHostedService>();
            var customMetricsProvider = Substitute.For<CustomMetricsProvider>();

            // 設置 HealthCheck 字典，使得一個服務不健康
            ServiceController.HealthCheck = new Dictionary<Type, bool>
            {
                { typeof(ScheduleJobOperator), false }
            };

            // 設置 ServiceProvider.GetService 拋出異常
            serviceProvider.GetService(typeof(ScheduleJobOperator)).Returns(x => throw new Exception("Test exception"));

            var serviceController = new ServiceController(serviceProvider, "test", customMetricsProvider, LoggerMock);
            var cts = new CancellationTokenSource();

            // 設置取消令牌在一小段時間後取消
            cts.CancelAfter(1000);

            // Act - 啟動服務
            await serviceController.StartAsync(cts.Token);

            // 確保 ExecuteAsync 有時間執行
            await Task.Delay(500, cts.Token);

            // Assert
            // 驗證異常被記錄
            LogAllLoggerCalls();
            AssertLoggerSetReceived("Log", ["Error", "0", "Test exception"]);


            // 驗證 HttpListenerService.Opahealth 被設置為 false
            HttpListenerService.Opahealth.Should().BeFalse();

            // 停止服務
            await serviceController.StopAsync(cts.Token);
        }

        /// <summary>
        /// 測試當 ServiceProvider.GetService 返回 null 時，ServiceController 的 ExecuteAsync 方法應跳過該服務。
        /// 測試情境：設置 ScheduleJobOperator 服務的健康狀態為 false，
        /// 並模擬 ServiceProvider.GetService 方法返回 null 的情況，
        /// 驗證系統記錄該服務不健康的日誌，但不會嘗試重啟，且該服務的健康狀態維持為 false。
        /// </summary>
        [Fact]
        public async Task ExecuteAsync_ShouldSkipService_WhenGetServiceReturnsNull()
        {
            // Arrange
            var logger = Substitute.For<ILogger<ServiceController>>();
            var serviceProvider = Substitute.For<IServiceProvider>();
            var customMetricsProvider = Substitute.For<CustomMetricsProvider>();

            // 設置 HealthCheck 字典，使得一個服務不健康
            ServiceController.HealthCheck = new Dictionary<Type, bool>
            {
                { typeof(ScheduleJobOperator), false }
            };

            // 設置 ServiceProvider.GetService 返回 null
            serviceProvider.GetService(typeof(ScheduleJobOperator)).Returns(null);

            var serviceController = new ServiceController(serviceProvider, "test", customMetricsProvider, LoggerMock);
            var cts = new CancellationTokenSource();

            // 設置取消令牌在一小段時間後取消
            cts.CancelAfter(1000);

            // Act - 啟動服務
            await serviceController.StartAsync(cts.Token);

            // 確保 ExecuteAsync 有時間執行
            await Task.Delay(500, cts.Token);

            // Assert
            // 驗證記錄不健康的服務
            LogAllLoggerCalls();
            AssertLoggerSetReceived("Log", ["Information", "0", "Service ScheduleJobOperator is unhealthy"]);

            // 健康狀態應該仍然為 false，因為服務未找到
            ServiceController.HealthCheck[typeof(ScheduleJobOperator)].Should().BeFalse();

            // 停止服務
            await serviceController.StopAsync(cts.Token);
        }
    }
}
