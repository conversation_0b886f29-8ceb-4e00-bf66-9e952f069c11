﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
    </PropertyGroup>
    <PropertyGroup>
      <!-- 確保使用中央套件管理 -->
      <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
      <!-- 指定要使用的 NuGet 配置檔案位置 -->
      <RestoreConfigFile>$(MSBuildThisFileDirectory)..\..\nuget.config</RestoreConfigFile>
    </PropertyGroup>
    <ItemGroup>
      <PackageReference Include="FluentAssertions" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" />
      <PackageReference Include="Microsoft.NET.Test.Sdk" />
      <PackageReference Include="NSubstitute" />
      <PackageReference Include="RichardSzalay.MockHttp" />
      <PackageReference Include="xunit" />
      <PackageReference Include="xunit.runner.visualstudio" >
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        <PrivateAssets>all</PrivateAssets>
      </PackageReference>
      <PackageReference Include="coverlet.collector" >
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        <PrivateAssets>all</PrivateAssets>
      </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\JKOPay.BatchSystem.Operator\JKOPay.BatchSystem.Operator.csproj" />
    </ItemGroup>

</Project>
