using System.Globalization;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Serilog;
using Xunit.Abstractions;

namespace JKOPay.BatchSystem.Operator.Test
{
    /// <summary>
    /// AntiRequestResponseLoggingMiddleware 測試的共用基底類別。
    /// </summary>
    public abstract class TestBase(ITestOutputHelper testOutputHelper)
    {
        protected readonly RequestDelegate NextMock = Substitute.For<RequestDelegate>();
        protected readonly IDiagnosticContext DiagnosticContextMock = Substitute.For<IDiagnosticContext>();
        protected readonly ILogger<ServiceController> LoggerMock = Substitute.For<ILogger<ServiceController>>();

        /// <summary>
        /// 建立 AntiRequestResponseLoggingMiddleware 測試實例，提供模擬的 RequestDelegate 和必要的依賴項。
        /// </summary>
        /// <param name="serviceProvider">IServiceProvider</param>
        /// <param name="loggerMock">由外部傳入的 ILogger mock，用於檢查日誌記錄行為。</param>
        /// <returns>測試用的 AntiRequestResponseLoggingMiddleware 實例。</returns>
        protected ServiceController CreateTarget(IServiceProvider serviceProvider, ILogger<ServiceController> loggerMock)
        {
            var target = new ServiceController(
                serviceProvider,
                "test",
                null,
                loggerMock
            );

            return target;
        }

        /// <summary>
        /// 驗證 JSON 結構與預期屬性和值是否匹配，包含 Decimal 值的處理。
        /// </summary>
        protected bool IsJsonWithExpectedDecimalValues(string json, Dictionary<string, object> expectedValues)
        {
            try
            {
                foreach (var kvp in expectedValues)
                {
                    var key = kvp.Key;
                    var expectedValue = kvp.Value;

                    switch (expectedValue)
                    {
                        case string expectedStr:
                        {
                            // 用正則表達式直接從原始 JSON 中抽取字串值（保留 escape 序列）
                            var pattern = $"\"{Regex.Escape(key)}\"\\s*:\\s*\"((?:\\\\.|[^\"])*)\"";
                            var match = Regex.Match(json, pattern);
                            if (!match.Success)
                            {
                                return false;
                            }
                            var actualRawStr = match.Groups[1].Value;
                            if (actualRawStr != expectedStr)
                            {
                                return false;
                            }

                            break;
                        }
                        case decimal expectedDec:
                        {
                            // 用正則表達式抽取數字值
                            var pattern = $"\"{Regex.Escape(key)}\"\\s*:\\s*([-+]?\\d+(?:\\.\\d+)?)";
                            var match = Regex.Match(json, pattern);
                            if (!match.Success)
                            {
                                return false;
                            }
                            if (!decimal.TryParse(match.Groups[1].Value, NumberStyles.Any, CultureInfo.InvariantCulture, out var actualDec))
                            {
                                return false;
                            }
                            if (actualDec != expectedDec)
                            {
                                return false;
                            }

                            break;
                        }
                        default:
                            // 若有其他型別的需求，可在此擴充處理邏輯
                            return false;
                    }
                }
                return true;
            }
            catch
            {
                return false; // 無效的 JSON
            }
        }

        #region 共用的驗證方法

        /// <summary>
        /// 記錄 DiagnosticContextMock 收到的所有調用
        /// </summary>
        protected void LogAllLoggerCalls()
        {
            var receivedCalls = LoggerMock.ReceivedCalls().ToList();
            testOutputHelper.WriteLine($"Total received calls: {receivedCalls.Count}");
            foreach (var call in receivedCalls)
            {
                testOutputHelper.WriteLine($"Called: {call.GetMethodInfo().Name} with args: {string.Join(", ", call.GetArguments())}");
            }
        }

        /// <summary>
        /// 驗證 DiagnosticContext 是否收到特定參數的 Set 調用
        /// </summary>
        /// <param name="methodName">預期呼叫的方法</param>
        /// <param name="expectedValues">預期的參數值，如果為null則不檢查值</param>
        protected void AssertLoggerSetReceived(string methodName, IReadOnlyList<string> expectedValues)
        {
            var tmpQuery = LoggerMock.ReceivedCalls().Where(call => call.GetMethodInfo().Name == methodName);
            for (var i = 0; i < expectedValues.Count; i++)
            {
                var index = i;
                tmpQuery = tmpQuery.Where(call => call.GetArguments()[index]?.ToString() == expectedValues[index]);
            }

            Assert.Single(tmpQuery.ToList());
        }

        #endregion
    }
}
